// A custom interface for managing Postgres connections, based on the `Pool`
// from the "pg" package.
import {
  QueryResultBase,
  Pool as NodePgPool,
  PoolClient as NodePgPoolClient,
  ClientConfig as NodePgClientConfig,
  Client as NodePgClient,
} from "pg";
import { z } from "zod";

export interface BtQueryResult extends QueryResultBase {
  rows: Record<string, unknown>[];
}

export interface BtPg {
  query(queryText: string, values?: unknown[]): Promise<BtQueryResult>;

  connect(): Promise<BtPgClient>;

  end(): Promise<void>;

  onError(listener: (err: Error, client: BtPgClient) => void): void;
}

export interface BtPgClient {
  query(queryText: string, values?: unknown[]): Promise<BtQueryResult>;

  end(): Promise<void>;
}

export class PoolingBtPg implements BtPg {
  constructor(private readonly pool: NodePgPool) {}

  async query(queryText: string, values?: unknown[]): Promise<BtQueryResult> {
    return this.pool.query(queryText, values);
  }

  async connect(): Promise<PoolingBtPgClient> {
    const client = await this.pool.connect();
    return new PoolingBtPgClient(client);
  }

  async end(): Promise<void> {
    return this.pool.end();
  }

  onError(listener: (err: Error, client: BtPgClient) => void): void {
    this.pool.on("error", (err, client) => {
      listener(err, new PoolingBtPgClient(client));
    });
  }

  totalCount(): number {
    return this.pool.totalCount;
  }

  idleCount(): number {
    return this.pool.idleCount;
  }

  waitingCount(): number {
    return this.pool.waitingCount;
  }
}

export class PoolingBtPgClient implements BtPgClient {
  constructor(private readonly client: NodePgPoolClient) {}

  async query(queryText: string, values?: unknown[]): Promise<BtQueryResult> {
    return this.client.query(queryText, values);
  }

  async end(): Promise<void> {
    return this.client.release();
  }

  release(err?: Error | boolean): void {
    return this.client.release(err);
  }

  connectionPid(): number {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- It works in practice
    return z.number().parse((this.client as any).processID);
  }
}

export class NonPoolingBtPg implements BtPg {
  private errorListeners: ((err: Error, client: BtPgClient) => void)[] = [];

  constructor(private readonly config: NodePgClientConfig) {}

  async query(queryText: string, values?: unknown[]): Promise<BtQueryResult> {
    const client = await this.makeClient();
    try {
      // Make sure to await the result *before* running the `finally` clause.
      return await client.query(queryText, values);
    } finally {
      await client.end();
    }
  }

  async connect(): Promise<NonPoolingBtPgClient> {
    return new NonPoolingBtPgClient(await this.makeClient());
  }

  async end(): Promise<void> {
    return;
  }

  onError(listener: (err: Error, client: BtPgClient) => void): void {
    this.errorListeners.push(listener);
  }

  private async makeClient(): Promise<NodePgClient> {
    const client = new NodePgClient(this.config);
    if (this.errorListeners.length > 0) {
      client.on("error", (err) => {
        for (const listener of this.errorListeners) {
          listener(err, client);
        }
      });
    }
    await client.connect();
    return client;
  }
}

export class NonPoolingBtPgClient implements BtPgClient {
  constructor(private readonly client: NodePgClient) {}

  async query(queryText: string, values?: unknown[]): Promise<BtQueryResult> {
    return this.client.query(queryText, values);
  }

  async end(): Promise<void> {
    return this.client.end();
  }
}
