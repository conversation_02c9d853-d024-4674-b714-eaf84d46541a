import {
  customColumnSchema,
  datetimeStringSchema,
} from "@braintrust/core/typespecs";
import { z } from "zod";

// Designed to be usable in the app against old data planes, so keep all fields
// nullish.
export const brainstoreSegmentInfoSchema = z.object({
  segment_id: z.string().nullish(),
  object_id: z.string().nullish(),
  is_live: z.boolean().nullish(),
  last_written_ts: datetimeStringSchema.nullish(),
  vacuum_index_last_successful_start_ts: datetimeStringSchema.nullish(),
  vacuum_index_info: z.unknown().nullish(),
  last_compacted_index_meta_xact_id: z.number().nullish(),
  last_compacted_index_meta_tantivy_meta: z.string().nullish(),
  minimum_pagination_key: z.coerce.string().nullish(), // numeric as string
  num_rows: z.number().nullish(),
  index_operation_start: datetimeStringSchema.nullish(),
  index_operation_last_updated: datetimeStringSchema.nullish(),
  index_operation: z.unknown().nullish(),
});

export const customColumnScopeSchema = z.object({
  object_type: customColumnSchema.shape.object_type,
  object_id: customColumnSchema.shape.object_id,
  subtype: customColumnSchema.shape.subtype,
});
export type CustomColumnScope = z.infer<typeof customColumnScopeSchema>;
