use object_store::{self, path::Path, ObjectStore, PutMode, PutOptions, PutPayload};
use stable_deref_trait::StableDeref;
use std::ops::Deref;
use std::{path::PathBuf, pin::Pin, sync::Arc};
use tracing::instrument;
use util::async_trait::async_trait;
use util::futures::{stream::BoxStream, StreamExt, TryStreamExt};
use util::{anyhow::Result, tokio::io::AsyncWriteExt};

use crate::time;
use crate::{instrumented::Instrumented, timer::TimerManager};

use super::async_directory::{AsyncDirectory, AsyncFileHandle, AsyncWriteHandler};

/// Implementation of tantivy::directory::Directory that uses an ObjectStore as the backend.
#[derive(Clone, Debug)]
pub struct ObjectStoreDirectory {
    pub store: Arc<dyn ObjectStore>,
    debug_timer: Arc<TimerManager>,
}

impl ObjectStoreDirectory {
    pub fn new(store: Arc<dyn ObjectStore>) -> Self {
        Self {
            store,
            debug_timer: TimerManager::new("ObjectStoreDirectory"),
        }
    }
}

impl Instrumented for ObjectStoreDirectory {
    fn enable_timing(&self) {
        self.debug_timer.enable_granular_timing();
    }

    fn reset_timing(&self) {
        self.debug_timer.reset();
    }

    fn timers(&self) -> Vec<Arc<TimerManager>> {
        vec![self.debug_timer.clone()]
    }
}

// NOTE: We skip instrumenting the errors here, because some of the errors like
// FileDoesNotExist are handled as expected and we don't want to spam logs.
#[async_trait]
impl AsyncDirectory for ObjectStoreDirectory {
    #[instrument(skip(self), level = "debug")]
    async fn async_get_file_handle(
        &self,
        path: &std::path::Path,
        len: Option<u64>,
    ) -> Result<Arc<dyn AsyncFileHandle>, tantivy::directory::error::OpenReadError> {
        let full_path = make_path(path);
        let store = self.store.clone();

        time!(self.debug_timer, "head");

        let len = match len {
            Some(len) => len,
            None => {
                let meta = self.store.head(&full_path).await.map_err(|e| {
                    tantivy::directory::error::OpenReadError::IoError {
                        io_error: Arc::new(std::io::Error::new(std::io::ErrorKind::Other, e)),
                        filepath: PathBuf::from(full_path.to_string()),
                    }
                })?;
                meta.size as u64
            }
        };

        Ok(Arc::new(ObjectStoreFileHandle {
            timer: self.debug_timer.clone(),
            store,
            path: full_path,
            len,
        }))
    }

    #[instrument(skip(self), level = "debug")]
    async fn async_delete(
        &self,
        path: &std::path::Path,
    ) -> Result<(), tantivy::directory::error::DeleteError> {
        time!(self.debug_timer, "delete");

        let full_path = make_path(path);
        self.store.delete(&full_path).await.map_err(|e| match e {
            object_store::Error::NotFound { .. } => {
                tantivy::directory::error::DeleteError::FileDoesNotExist(PathBuf::from(path))
            }
            _ => tantivy::directory::error::DeleteError::IoError {
                io_error: Arc::new(std::io::Error::new(std::io::ErrorKind::Other, e)),
                filepath: PathBuf::from(full_path.to_string()),
            },
        })
    }

    #[instrument(skip(self), level = "debug")]
    async fn async_exists(
        &self,
        path: &std::path::Path,
    ) -> Result<bool, tantivy::directory::error::OpenReadError> {
        time!(self.debug_timer, "exists");

        let full_path = make_path(path);
        self.store
            .head(&full_path)
            .await
            .map(|_| true)
            .or_else(|e| match e {
                object_store::Error::NotFound { .. } => Ok(false),
                _ => Err(tantivy::directory::error::OpenReadError::IoError {
                    io_error: Arc::new(std::io::Error::new(std::io::ErrorKind::Other, e)),
                    filepath: PathBuf::from(full_path.to_string()),
                }),
            })
    }

    // This is used to implement concurrency control, and it needs to return
    // a special error if the file already exists. We can solve this
    // on S3 using If-None-Match, which they just released support for:
    // https://aws.amazon.com/about-aws/whats-new/2024/08/amazon-s3-conditional-writes/
    //
    // Because Minio has supported it for a while, it looks like it's in the object_store package,
    // if you specify put_opts with PutMode::Create and client.config.conditional_put = Some(S3ConditionalPut::ETagMatch)
    // https://github.com/apache/arrow-rs/blob/master/object_store/src/aws/mod.rs#L172
    // (we just can't do Update(v) since If-Match is not supported).
    //
    // They also released object locks, but I don't think we need them:
    // https://docs.aws.amazon.com/AmazonS3/latest/userguide/object-lock.html
    #[instrument(skip(self), level = "debug")]
    async fn async_open_write(
        &self,
        path: &std::path::Path,
    ) -> Result<Pin<Box<dyn AsyncWriteHandler>>, tantivy::directory::error::OpenWriteError> {
        time!(self.debug_timer, "open_write");

        let full_path = make_path(path);
        let store = self.store.clone();
        let full_path_copy = full_path.clone();
        let put_result = store
            .put_opts(
                &full_path_copy,
                PutPayload::from_static(b""),
                PutOptions {
                    mode: PutMode::Create,
                    ..Default::default()
                },
            )
            .await;
        match put_result {
            Ok(_result) => (),
            Err(e) => match e {
                object_store::Error::AlreadyExists { path, .. } => {
                    return Err(
                        tantivy::directory::error::OpenWriteError::FileAlreadyExists(
                            PathBuf::from(path),
                        ),
                    );
                }
                _ => {
                    return Err(tantivy::directory::error::OpenWriteError::IoError {
                        io_error: Arc::new(std::io::Error::new(std::io::ErrorKind::Other, e)),
                        filepath: PathBuf::from(full_path.to_string()),
                    });
                }
            },
        }

        Ok(Box::pin(ObjectStoreWriter {
            timer: self.debug_timer.clone(),
            writer: tokio::sync::Mutex::new(object_store::buffered::BufWriter::new(
                self.store.clone(),
                full_path,
            )),
        }))
    }

    #[instrument(skip(self), level = "debug")]
    async fn async_atomic_read(
        &self,
        path: &std::path::Path,
    ) -> Result<Vec<u8>, tantivy::directory::error::OpenReadError> {
        time!(self.debug_timer, "atomic_read");

        let full_path = make_path(path);

        let data = self.store.get(&full_path).await.map_err(|e| match e {
            object_store::Error::NotFound { .. } => {
                tantivy::directory::error::OpenReadError::FileDoesNotExist(PathBuf::from(
                    full_path.to_string(),
                ))
            }
            _ => tantivy::directory::error::OpenReadError::IoError {
                io_error: Arc::new(std::io::Error::new(std::io::ErrorKind::Other, e)),
                filepath: PathBuf::from(full_path.to_string()),
            },
        })?;
        let result = data
            .bytes()
            .await
            .map_err(|e| tantivy::directory::error::OpenReadError::IoError {
                io_error: Arc::new(std::io::Error::new(std::io::ErrorKind::Other, e)),
                filepath: PathBuf::from(full_path.to_string()),
            })?
            .to_vec();

        Ok(result)
    }

    #[instrument(skip(self, data), fields(len = data.len()), level = "debug")]
    async fn async_atomic_write(&self, path: &std::path::Path, data: &[u8]) -> std::io::Result<()> {
        time!(self.debug_timer, "atomic_write");

        let full_path = make_path(path);
        let store = self.store.clone();
        store
            .put(&full_path, data.to_vec().into())
            .await
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e))?;

        Ok(())
    }

    async fn async_sync_directory(&self) -> std::io::Result<()> {
        // Object stores are typically eventually consistent, so we don't need to do anything here
        Ok(())
    }

    #[instrument(skip(self, locations), level = "debug")]
    fn delete_stream<'a>(
        &'a self,
        locations: BoxStream<'a, Result<PathBuf, object_store::Error>>,
    ) -> BoxStream<'a, Result<object_store::path::Path, object_store::Error>> {
        time!(self.debug_timer, "delete_stream");

        let object_store_paths: BoxStream<'a, Result<Path, object_store::Error>> =
            locations.map_ok(|path| make_path(&path)).boxed();

        self.store.delete_stream(object_store_paths).boxed()
    }

    async fn load_redirects(&self, _path: &std::path::Path) -> util::anyhow::Result<bool> {
        Ok(false)
    }
}

#[derive(Clone, Debug)]
pub struct ObjectStoreFileHandle {
    timer: Arc<TimerManager>,
    store: Arc<dyn ObjectStore>,
    path: Path,
    len: u64,
}

#[async_trait]
impl AsyncFileHandle for ObjectStoreFileHandle {
    async fn async_read_bytes(
        &self,
        range: std::ops::Range<usize>,
    ) -> std::io::Result<tantivy::directory::OwnedBytes> {
        time!(self.timer, "read_bytes");

        let data = self
            .store
            .get_range(&self.path, (range.start as u64)..(range.end as u64))
            .await
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e))?;

        Ok(tantivy::directory::OwnedBytes::new(BytesOwnedBytes(data)))
    }
}

pub struct BytesOwnedBytes(bytes::Bytes);

unsafe impl StableDeref for BytesOwnedBytes {}

impl Deref for BytesOwnedBytes {
    type Target = [u8];

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}
impl tantivy::HasLen for ObjectStoreFileHandle {
    fn len(&self) -> usize {
        self.len as usize
    }
}

struct ObjectStoreWriter {
    // This has to be wrapped in a mutex because the underlying writer is not thread safe
    writer: tokio::sync::Mutex<object_store::buffered::BufWriter>,
    timer: Arc<TimerManager>,
}

#[async_trait]
impl AsyncWriteHandler for ObjectStoreWriter {
    async fn write<'a>(
        mut self: std::pin::Pin<&'a mut Self>,
        buf: &'a [u8],
    ) -> std::io::Result<usize> {
        time!(self.timer, "write");
        let mut writer = self.writer.lock().await;
        writer.write(buf).await
    }

    async fn flush(mut self: std::pin::Pin<&mut Self>) -> std::io::Result<()> {
        let mut writer = self.writer.lock().await;
        writer.flush().await
    }

    async fn shutdown(mut self: std::pin::Pin<&mut Self>) -> std::io::Result<()> {
        let mut writer = self.writer.lock().await;
        writer.shutdown().await
    }
}

fn make_path(path: &std::path::Path) -> Path {
    Path::from(path.to_str().unwrap())
}
