use clap::Parser;
use serde::{Deserialize, Serialize};
use std::{
    collections::{HashMap, HashSet},
    sync::Arc,
};
use tantivy::common::BitSet;
use tantivy::{
    query::{BitSetDocSet, EnableScoring, Exclude, Query, QueryClone, TermSetQuery, Weight},
    DocAddress, DocId, SegmentId, SegmentReader, TantivyError, Term,
};
use tokio::{join, sync::SemaphorePermit};

use util::{
    anyhow::{self, anyhow, Result},
    await_spawn_blocking,
    chrono::{DateTime, Utc},
    futures::{future::join_all, stream::BoxStream, FutureExt, StreamExt},
    schema::{Schema, TantivyFastValueType, TantivyFieldFilterOpts},
    system_types::{FullObjectIdOwned, FullRowId, FullRowIdOwned},
    test_util::TwoWaySyncPointSendAndWait,
    tracer::{trace_if, trace_if_async, EnterTraceGuard, TracedNode},
    uuid::Uuid,
    xact::{PaginationKey, TransactionId},
    Value,
};

use crate::{
    config_with_store::ConfigWithStore,
    directory::dynamic_readonly_directory::{DynamicIndexSpec, DynamicReadonlyDirectory},
    global_store::{
        GlobalStore, IdSegmentMembershipType, LastCompactedIndexMeta, ObjectMetadata,
        SegmentFieldStatistics, SegmentWalEntriesCursor,
    },
    index_document::IndexDocument,
    limits::{global_limits, AsyncLimit, TryAsyncLimitError},
    segment_batches::{
        compute_segment_batches, ComputeSegmentBatchInput, ComputeSegmentBatchOpts, SegmentBatch,
        SegmentBatchingSortSpec, SegmentEliminationFilterSpec,
    },
    tantivy_index::{
        collect_column_raw_value_for_doc, make_tantivy_schema,
        str_column_read_single_bytes_for_doc, TantivyIndexScope, WritableTantivySchema,
    },
    wal::{
        merge_wal_metadata_streams, wal_stream, AsDynWalMetadata, WALScope, Wal, WalDataDetail,
        WalMetadata, WalMetadataStreamOptionalInput, WalStreamOptions,
    },
    wal_entry::WalEntry,
};

/// The IndexWalReader is an abstraction that helps you read from compacted data in segments as well
/// as the WAL for each segment. It works by:
/// * Constructing a set of un-merged WAL entries from the global WAL (up to the last processed xact id)
///     * Snapshotting the global last processed xact id
///     * Snapshotting each segment's last compacted WAL transaction id (up to the global last processed xact id)
///     * Fetching the WAL entries for each segment from the global merged WAL (NOTE: it's ok for these to overlap, but
///         it's important that we snapshot the global WAL first so we don't accidentally miss WAL entries from any
///         particular segment)
/// * Constructing a RAM-backed tantivy index from the set of un-merged WAL entries
/// * Producing two artifacts that queriers can use to access this view of the data:
///     * A Directory that reads from the RAM-backed tantivy index as well as the on-disk segments
///     * A Query that excludes any documents in the on-disk segments that have newer versions in the RAM-backed index
#[derive(Clone)]
pub struct IndexWalReader(Arc<IndexWalReaderInner>);

// Keep this in sync with realtimeStateSchema in local/js/app-schema/brainstore.ts
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", rename_all = "lowercase")]
pub enum IndexWalReaderRealtimeState {
    #[serde(rename = "disabled")]
    Disabled,
    // Realtime was attempted but didn't reach the end due to the memory limit.
    #[serde(rename = "exhausted_memory_budget")]
    ExhaustedMemoryBudget {
        // The minimum xact_id that the realtime read would have to progress to
        // in order to be valid. It is defined as the maximum
        // last_compacted_xact_id across all segments being queried.
        minimum_xact_id: Option<TransactionId>,
        // The size in bytes of WAL entries that realtime read before bailing.
        read_bytes: usize,
        // The xact_id that realtime progressed to within the budget. If
        // actual_xact_id >= minimum_xact_id, then the query will be
        // transactionally consistent. Otherwise, it may not be consistent
        // because there may be transactions <= minimum_xact_id that we missed.
        actual_xact_id: Option<TransactionId>,
    },
    // Realtime was attempted but didn't reach the end due to the timeout. This
    // means we won't use any realtime records.
    #[serde(rename = "exhausted_timeout")]
    ExhaustedTimeout {
        // The timeout in milliseconds.
        timeout_ms: u64,
    },
    // Realtime reached the end of the wal. It is necessarily transactionally
    // consistent.
    #[serde(rename = "on")]
    On {
        minimum_xact_id: Option<TransactionId>,
        read_bytes: usize,
        actual_xact_id: Option<TransactionId>,
    },
}

struct IndexWalReaderInner {
    base_directories: Vec<DynamicReadonlyDirectory>,
    segment_batches: Vec<SegmentBatch>,
    schema: util::schema::Schema,
    excluded_records: HashMap<SegmentId, Vec<DocId>>,
    merged_wal_docs: Vec<Value>,
    realtime_state: IndexWalReaderRealtimeState,
    _realtime_state_permit: Option<SemaphorePermit<'static>>,
}

#[derive(Debug, Clone)]
struct ObjectSegmentInfo {
    segment_ids: Vec<Uuid>,
    object_metadata: ObjectMetadata,
}

#[derive(Debug, Clone, PartialEq, Eq, Hash)]
struct SegmentObjectInfo {
    object_id: FullObjectIdOwned,
    // This corresponds to the global last_processed_xact_id for the segment's object
    last_processed_xact_id: Option<TransactionId>,
}

#[derive(Debug, Default, Clone, PartialEq, Eq, Parser, Serialize, Deserialize)]
pub struct IndexWalReaderOpts {
    #[arg(
        long,
        help = "If true, skip realtime WAL entries. Defaults to false.",
        env = "BRAINSTORE_SKIP_REALTIME_WAL_ENTRIES"
    )]
    #[serde(default)]
    pub skip_realtime_wal_entries: Option<bool>,

    #[arg(
        long,
        help = "The initial number of segments to read in parallel. Set to a 0 to read all segments at once.",
        env = "BRAINSTORE_INITIAL_SEGMENT_BATCH_SIZE"
    )]
    pub initial_segment_batch_size: Option<usize>,

    #[arg(
        long,
        help = "If true, skip the realtime WAL memory limit check. Defaults to false.",
        env = "BRAINSTORE_SKIP_REALTIME_WAL_MEMORY_LIMIT"
    )]
    pub skip_realtime_wal_memory_limit: Option<bool>,

    #[arg(
        long,
        help = "The maximum number of objects to read from the realtime WAL. Defaults to 10.",
        env = "BRAINSTORE_MAX_REALTIME_OBJECTS"
    )]
    pub max_realtime_objects: Option<usize>,

    #[arg(
        long,
        help = format!("The timeout for the realtime WAL read. Defaults to {} milliseconds. If 0, no timeout is applied.", default_realtime_read_timeout_ms()),
        env = "BRAINSTORE_REALTIME_READ_TIMEOUT_MS"
    )]
    pub realtime_read_timeout_ms: Option<u64>,
}

#[derive(Debug, Clone)]
struct IndexWalReaderOptsInner {
    skip_realtime_wal_entries: bool,
    initial_segment_batch_size: usize,
    skip_realtime_wal_memory_limit: bool,
    max_realtime_objects: usize,
    realtime_read_timeout_ms: u64,
}

impl IndexWalReaderOpts {
    // For any options that are unset in `self`, use the value from `other`.
    pub fn merge_unset(self, other: &IndexWalReaderOpts) -> Self {
        Self {
            skip_realtime_wal_entries: self
                .skip_realtime_wal_entries
                .or(other.skip_realtime_wal_entries),
            initial_segment_batch_size: self
                .initial_segment_batch_size
                .or(other.initial_segment_batch_size),
            skip_realtime_wal_memory_limit: self
                .skip_realtime_wal_memory_limit
                .or(other.skip_realtime_wal_memory_limit),
            max_realtime_objects: self.max_realtime_objects.or(other.max_realtime_objects),
            realtime_read_timeout_ms: self
                .realtime_read_timeout_ms
                .or(other.realtime_read_timeout_ms),
        }
    }

    fn into_inner(self) -> IndexWalReaderOptsInner {
        IndexWalReaderOptsInner {
            skip_realtime_wal_entries: self.skip_realtime_wal_entries.unwrap_or(false),
            initial_segment_batch_size: self
                .initial_segment_batch_size
                .unwrap_or(default_initial_segment_batch_size()),
            skip_realtime_wal_memory_limit: self
                .skip_realtime_wal_memory_limit
                .unwrap_or(default_skip_realtime_wal_memory_limit()),
            max_realtime_objects: self
                .max_realtime_objects
                .unwrap_or(default_max_realtime_objects()),
            realtime_read_timeout_ms: self
                .realtime_read_timeout_ms
                .unwrap_or(default_realtime_read_timeout_ms()),
        }
    }
}

#[cfg(not(test))]
fn default_initial_segment_batch_size() -> usize {
    1
}

#[cfg(test)]
fn default_initial_segment_batch_size() -> usize {
    0
}

#[cfg(not(test))]
fn default_skip_realtime_wal_memory_limit() -> bool {
    false
}

// For unit tests, we skip the memory limit by default to prevent tests from
// randomly encountering it.
#[cfg(test)]
fn default_skip_realtime_wal_memory_limit() -> bool {
    true
}

fn default_max_realtime_objects() -> usize {
    10
}

#[cfg(not(test))]
fn default_realtime_read_timeout_ms() -> u64 {
    10 * 1000 // 10 seconds
}

// For unit tests, we skip the realtime read timeout by default to prevent tests from
// randomly encountering it.
#[cfg(test)]
fn default_realtime_read_timeout_ms() -> u64 {
    0
}

#[derive(Default)]
pub struct IndexWalReaderTestingSyncPoints {
    pub before_stream_first_realtime_wal_entry: Option<TwoWaySyncPointSendAndWait>,
    pub after_get_all_realtime_wal_entries: Option<TwoWaySyncPointSendAndWait>,
}

pub struct IndexWalReaderInput<'a> {
    pub config_with_store: &'a ConfigWithStore,
    pub full_schema: util::schema::Schema,
    pub object_ids: &'a [FullObjectIdOwned],
    pub sort: &'a Option<SegmentBatchingSortSpec>,
    pub filters: &'a [SegmentEliminationFilterSpec],
}

#[derive(Default)]
pub struct IndexWalReaderOptionalInput {
    pub tantivy_executor: Option<Arc<tantivy::Executor>>,
    pub tracer: Option<Arc<TracedNode>>,
    pub testing_sync_points: IndexWalReaderTestingSyncPoints,
    pub testing_global_realtime_memory_bytes_limit: Option<&'static AsyncLimit>,
}

#[cfg(not(test))]
fn get_realtime_read_per_query_bytes_limit() -> usize {
    global_limits().realtime_read_max_per_query_bytes
}

#[cfg(test)]
fn get_realtime_read_per_query_bytes_limit() -> usize {
    // 1 MB
    1 * 1024 * 1024
}

// TODO:
// * Optimize by only materializing a required set of projected fields (+ id, _xact_id)
// * Bound the WAL size (see comment)
impl IndexWalReader {
    /// This function creates an IndexWalReader, and it does a bunch of work so it should be timed and reported.
    pub async fn new(
        input: IndexWalReaderInput<'_>,
        optional_input: IndexWalReaderOptionalInput,
        opts: IndexWalReaderOpts,
    ) -> Result<Self> {
        let opts = opts.into_inner();
        let config_with_store = input.config_with_store;
        let full_schema = input.full_schema;
        let object_ids = input.object_ids;
        let tantivy_executor = optional_input.tantivy_executor;
        let tracer = optional_input.tracer;
        let testing_sync_points = optional_input.testing_sync_points;

        let realtime_timeout_fut = if opts.realtime_read_timeout_ms == 0 {
            util::futures::future::pending().boxed()
        } else {
            tokio::time::sleep(std::time::Duration::from_millis(
                opts.realtime_read_timeout_ms,
            ))
            .boxed()
        };

        // First, find the list of segment ids corresponding to each object id. We may in the future want to permit the
        // user to expclitily pass in some segments (e.g. if you are only considering a subset of segments).
        let object_segment_ids = trace_if_async(
            log::Level::Info,
            &tracer,
            "Get object segment ids",
            move |child| async move {
                let object_id_refs = object_ids.iter().map(|o| o.as_ref()).collect::<Vec<_>>();
                let (object_segment_ids, object_metadatas) = join!(
                    config_with_store
                        .global_store
                        .list_segment_ids(&object_id_refs, None),
                    config_with_store
                        .global_store
                        .query_object_metadatas(&object_id_refs)
                );
                let object_segment_ids = object_segment_ids?;
                let object_metadatas = object_metadatas?;

                let segment_ids = object_ids
                    .iter()
                    .zip(object_segment_ids.into_iter())
                    .zip(object_metadatas.into_iter())
                    .map(|((o, s), x)| {
                        (
                            o.clone(),
                            ObjectSegmentInfo {
                                segment_ids: s,
                                object_metadata: x,
                            },
                        )
                    })
                    .collect::<HashMap<_, _>>();

                child.increment_counter(
                    "num_segments",
                    segment_ids
                        .values()
                        .map(|s| s.segment_ids.len())
                        .sum::<usize>() as u64,
                );
                Ok::<_, util::anyhow::Error>(segment_ids)
            },
        )
        .await?;

        let segment_metadatas = Self::get_all_segment_metadatas(
            config_with_store.clone(),
            object_segment_ids.clone(),
            tracer.clone(),
        )
        .await?;
        let (all_segment_ids, segment_object_infos, segment_index_metadatas) = &*segment_metadatas;

        let tantivy_schema = make_tantivy_schema(&full_schema)?;

        // We read from the snapshot of the tantivy index corresponding to
        // exactly where we read realtime WAL entries from. This is preferable
        // to reading the current state of the index, which may have advanced
        // past the WAL entries by the time we end up reading it, which would
        // cause inconsistencies.
        let durable_segment_specs = all_segment_ids
            .iter()
            .zip(segment_index_metadatas.iter())
            .filter_map(|(s, meta)| {
                meta.as_ref().map(|meta| {
                    (
                        *s,
                        DynamicIndexSpec {
                            directory: config_with_store.index.directory.clone(),
                            path: TantivyIndexScope::Segment(*s)
                                .path(&config_with_store.index.prefix),
                            meta: meta.tantivy_meta.clone(),
                        },
                    )
                })
            })
            .collect::<HashMap<_, _>>();

        let compacted_segment_ids = durable_segment_specs.keys().cloned().collect::<Vec<_>>();
        let (statistics, id_to_segment_id, root_span_id_to_segment_id) = get_segment_statistics(
            tracer.clone(),
            object_ids,
            &compacted_segment_ids,
            input.config_with_store.global_store.clone(),
            input.sort,
            input.filters,
        )
        .await?;
        let segment_batch_input = ComputeSegmentBatchInput {
            segment_ids: &compacted_segment_ids,
            statistics: &statistics,
            id_to_segment_id: &id_to_segment_id,
            root_span_id_to_segment_id: &root_span_id_to_segment_id,
            sort: input.sort,
            filters: input.filters,
        };
        let segment_batches = trace_if(
            log::Level::Info,
            &tracer,
            "Compute segment batches",
            |child| {
                child.increment_counter("num_input_segments", all_segment_ids.len() as u64);
                let batches = compute_segment_batches(
                    segment_batch_input,
                    ComputeSegmentBatchOpts {
                        initial_segment_batch_size: opts.initial_segment_batch_size,
                    },
                );
                child.increment_counter(
                    "num_output_segments",
                    batches.iter().map(|b| b.segments.len() as u64).sum::<u64>(),
                );
                child.increment_counter("num_batches", batches.len() as u64);
                if !batches.is_empty() {
                    child.increment_counter("first_batch_size", batches[0].segments.len() as u64);
                }
                batches
            },
        );

        let base_directories = trace_if_async(
            log::Level::Info,
            &tracer,
            "Create dynamic segment reader",
            |_child| async {
                join_all(segment_batches.iter().map(|segment_batch| {
                    DynamicReadonlyDirectory::new(
                        tantivy_schema.schema.clone(),
                        segment_batch
                            .segments
                            .iter()
                            .map(|segment_id| {
                                durable_segment_specs.get(segment_id).unwrap().clone()
                            })
                            .collect::<Vec<_>>(),
                    )
                }))
                .await
                .into_iter()
                .collect::<Result<Vec<_>>>()
            },
        )
        .await?;

        let mut root_span_id_filters: Option<&HashSet<String>> = None;
        for filter in input.filters.iter() {
            match filter {
                SegmentEliminationFilterSpec::IdFilter { root_span_ids, .. }
                    if !root_span_ids.is_empty() =>
                {
                    root_span_id_filters = Some(root_span_ids);
                    break;
                }
                _ => {}
            }
        }

        let (excluded_records, merged_wal_docs, realtime_state, realtime_state_permit) = tokio::select! {
            res = Self::read_realtime_and_compute_excluded_merged_docs(
                &opts,
                &object_ids,
                &config_with_store,
                &full_schema,
                &tantivy_schema,
                &durable_segment_specs,
                &object_segment_ids,
                &all_segment_ids,
                &segment_object_infos,
                &segment_index_metadatas,
                root_span_id_filters,
                tantivy_executor.clone(),
                tracer.clone(),
                optional_input.testing_global_realtime_memory_bytes_limit,
                testing_sync_points.before_stream_first_realtime_wal_entry,
                testing_sync_points.after_get_all_realtime_wal_entries,
            ) => res?,
            _ = realtime_timeout_fut => {
                (HashMap::new(), Vec::new(), IndexWalReaderRealtimeState::ExhaustedTimeout {
                    timeout_ms: opts.realtime_read_timeout_ms,
                }, None)
            }
        };

        Ok(Self(Arc::new(IndexWalReaderInner {
            base_directories,
            segment_batches,
            schema: full_schema,
            excluded_records,
            merged_wal_docs,
            realtime_state,
            _realtime_state_permit: realtime_state_permit,
        })))
    }

    pub fn num_batches(&self) -> usize {
        self.0.base_directories.len()
    }

    pub fn directory_with_sort_filters(
        &self,
        batch_idx: usize,
        query: Box<dyn tantivy::query::Query>,
        sort: Option<&(tantivy::Order, String, TantivyFastValueType)>,
    ) -> (&DynamicReadonlyDirectory, Box<dyn tantivy::query::Query>) {
        let directory = &self.0.base_directories[batch_idx];
        let query = match (sort, &self.0.segment_batches[batch_idx].filter) {
            (Some((_, field, _)), Some(filter)) => {
                Box::new(tantivy::query::BooleanQuery::new(vec![
                    (
                        tantivy::query::Occur::Must,
                        // The lenient thing here tells Tantivy that we don't need it to filter the columnstore
                        // by values that have the same type as these bounds. For example, if the underlying field
                        // is a datetime, since we're passing u64s in here, we don't want Tantivy to try and verify
                        // that we're reading a u64 typed column. These protections within Tantivy exist because the
                        // same column name can correspond to multiple types.
                        // See https://github.com/braintrustdata/tantivy/blob/0.22.0-tweaks/src/fastfield/readers.rs#L275
                        Box::new(tantivy::query::FastFieldRangeWeight::new_u64_lenient(
                            field.clone(),
                            filter.lower,
                            filter.upper,
                        )),
                    ),
                    (tantivy::query::Occur::Must, query),
                ]))
            }
            _ => query,
        };
        (directory, query)
    }

    pub fn only_directory(&self) -> &DynamicReadonlyDirectory {
        assert_eq!(self.0.base_directories.len(), 1);
        &self.0.base_directories[0]
    }

    pub fn schema(&self) -> &util::schema::Schema {
        &self.0.schema
    }

    pub fn in_memory_docs(&self) -> &[Value] {
        &self.0.merged_wal_docs
    }

    pub fn realtime_state(&self) -> &IndexWalReaderRealtimeState {
        &self.0.realtime_state
    }

    pub fn wrap_exclude_query(&self, query: Box<dyn Query>) -> Box<dyn Query> {
        Box::new(ExcludedDocQuery::new(
            query,
            self.0.excluded_records.clone(),
        ))
    }

    async fn get_all_segment_metadatas(
        config_with_store: ConfigWithStore,
        object_segment_ids: HashMap<FullObjectIdOwned, ObjectSegmentInfo>,
        tracer: Option<Arc<TracedNode>>,
    ) -> Result<
        Arc<(
            Vec<Uuid>,
            Vec<SegmentObjectInfo>,
            Vec<Option<LastCompactedIndexMeta>>,
        )>,
    > {
        let (segment_ids, segment_object_infos): (Vec<Uuid>, Vec<SegmentObjectInfo>) =
            object_segment_ids
                .iter()
                .flat_map(|(object_id, object_segment_info)| {
                    let segment_object_info = SegmentObjectInfo {
                        object_id: object_id.clone(),
                        last_processed_xact_id: object_segment_info
                            .object_metadata
                            .last_processed_xact_id,
                    };
                    object_segment_info
                        .segment_ids
                        .iter()
                        .map(move |segment_id| (segment_id, segment_object_info.clone()))
                })
                .unzip();

        let last_compacted_index_metas = trace_if_async(
            log::Level::Info,
            &tracer,
            "Get last compacted xact ids per segment",
            |_child| async {
                Ok::<_, util::anyhow::Error>(
                    config_with_store
                        .global_store
                        .query_segment_metadatas(&segment_ids)
                        .await?
                        .into_iter()
                        .map(|m| m.last_compacted_index_meta)
                        .collect::<Vec<_>>(),
                )
            },
        )
        .await?;

        Ok(Arc::new((
            segment_ids,
            segment_object_infos,
            last_compacted_index_metas,
        )))
    }

    async fn get_merged_wal_metadata_stream(
        config_with_store: ConfigWithStore,
        segment_ids: &[Uuid],
        segment_object_infos: &[SegmentObjectInfo],
        last_compacted_index_metas: &[Option<LastCompactedIndexMeta>],
        object_segment_ids: &HashMap<FullObjectIdOwned, ObjectSegmentInfo>,
        tracer: Option<Arc<TracedNode>>,
    ) -> Result<BoxStream<'static, Result<Box<dyn WalMetadata>>>> {
        // In one future, fetch a metadata stream for each segment which has any entries past its
        // last_compacted_index_meta_xact_id.
        //
        // For each segment with entries, retrieve the records in the segment's WAL from its
        // last_compacted_xact_id _up to_ the object WAL's last processed xact id. This is
        // important because if there are concurrent processing tasks, we don't want to include WAL
        // entries that have been since processed (although technically that would not break
        // anything).
        let segment_wal_streams_fut = {
            let config_with_store = &config_with_store;
            async move {
                // First query the set of segments which have any WAL entries beyond their
                // last_compacted_xact_id in the first place.
                let segment_id_cursors = segment_ids
                    .iter()
                    .zip(last_compacted_index_metas.iter())
                    .map(|(segment_id, last_compacted_index_meta)| {
                        (
                            *segment_id,
                            last_compacted_index_meta.as_ref().map(|meta| {
                                SegmentWalEntriesCursor::XactIdGe(TransactionId(meta.xact_id.0 + 1))
                            }),
                        )
                    })
                    .collect::<Vec<_>>();
                let segment_has_entries = config_with_store
                    .global_store
                    .query_segment_wal_entries_existence(&segment_id_cursors, None)
                    .await?;
                let segment_index_wal = config_with_store.segment_index_wal();
                let streams = {
                    let segment_index_wal = &segment_index_wal;
                    join_all(
                        segment_ids
                            .iter()
                            .zip(segment_object_infos.iter())
                            .zip(last_compacted_index_metas.iter())
                            .zip(segment_has_entries.into_iter())
                            .map(
                                |(
                                    ((segment_id, segment_object_info), last_compacted_index_meta),
                                    has_entries,
                                )| {
                                    async move {
                                        if !has_entries {
                                            return None;
                                        }
                                        let last_processed_xact_id =
                                            match segment_object_info.last_processed_xact_id {
                                                Some(x) => x,
                                                None => {
                                                    // If we don't have a last processed xact id, then we don't read any WAL entries from
                                                    // the segment, since nothing has "officially" been processed.
                                                    return None;
                                                }
                                            };
                                        let stream = segment_index_wal
                                            .wal_metadata_stream(
                                                WALScope::Segment(*segment_id),
                                                WalMetadataStreamOptionalInput {
                                                    start_xact_id: last_compacted_index_meta
                                                        .as_ref()
                                                        .map(|x| TransactionId(x.xact_id.0 + 1)),
                                                    end_xact_id: Some(last_processed_xact_id),
                                                    ..Default::default()
                                                },
                                            )
                                            .await;
                                        Some(stream)
                                    }
                                },
                            ),
                    )
                    .await
                    .into_iter()
                    .filter_map(|x| x)
                    .collect::<Result<Vec<_>>>()?
                };
                Ok::<_, util::anyhow::Error>(streams)
            }
        };

        // In another future, fetch a metadata stream for each object.
        let object_wal_streams_fut = {
            let config_with_store = &config_with_store;
            async move {
                let streams = join_all(object_segment_ids.iter().map(
                    |(object_id, object_segment_info)| async {
                        let wal = &config_with_store.realtime_wal;
                        let metadata_stream = match wal {
                            Some(wal) => {
                                wal.wal_metadata_stream(
                                    WALScope::ObjectId(
                                        object_id.as_ref(),
                                        object_segment_info.object_metadata.wal_token,
                                    ),
                                    WalMetadataStreamOptionalInput {
                                        start_xact_id: object_segment_info
                                            .object_metadata
                                            .last_processed_xact_id
                                            .map(|x| TransactionId(x.0 + 1)),
                                        ..Default::default()
                                    },
                                )
                                .await?
                            }
                            None => util::futures::stream::empty().boxed(),
                        };
                        Ok(metadata_stream)
                    },
                ))
                .await
                .into_iter()
                .collect::<Result<Vec<_>>>()?;
                Ok::<_, util::anyhow::Error>(streams)
            }
        };

        let merged_stream = trace_if_async(
            log::Level::Info,
            &tracer,
            "Get merged WAL metadata stream",
            |_child| async move {
                let (segment_streams, object_streams) =
                    join!(segment_wal_streams_fut, object_wal_streams_fut);
                let segment_streams = segment_streams?;
                let object_streams = object_streams?;
                Ok::<_, util::anyhow::Error>(merge_wal_metadata_streams(
                    segment_streams
                        .into_iter()
                        .chain(object_streams.into_iter()),
                ))
            },
        )
        .await?;
        Ok(merged_stream)
    }

    // In order to populate the most realtime records we can within a fixed memory budget, we
    // merge all the WAL streams across the segments and objects we wish to read from, and read
    // a single stream ordered by xact id. We read up to the number of transactions we have
    // budget for.
    //
    // We return a tuple of:
    // - The list of (xact_id, wal_entries)
    // - The permit to use to limit the memory usage of the query
    // - The xact_id of the transaction we were in when we aborted, if we did
    // - The number of bytes read
    // - Whether or not we read the entire WAL stream
    async fn read_realtime_wal_entries(
        config_with_store: ConfigWithStore,
        segment_ids: &[Uuid],
        segment_object_infos: &[SegmentObjectInfo],
        last_compacted_index_metas: &[Option<LastCompactedIndexMeta>],
        object_segment_ids: &HashMap<FullObjectIdOwned, ObjectSegmentInfo>,
        root_span_id_filters: Option<&HashSet<String>>,
        tracer: Option<Arc<TracedNode>>,
        skip_realtime_wal_memory_limit: bool,
        realtime_global_memory_limit_bytes: &'static AsyncLimit,
        mut before_stream_first_realtime_wal_entry: Option<TwoWaySyncPointSendAndWait>,
    ) -> Result<(
        Vec<(TransactionId, Vec<WalEntry>)>,
        Option<SemaphorePermit<'static>>,
        usize,
        bool,
    )> {
        let merged_metadata_stream = Self::get_merged_wal_metadata_stream(
            config_with_store,
            segment_ids,
            segment_object_infos,
            last_compacted_index_metas,
            object_segment_ids,
            tracer.clone(),
        )
        .await?;

        let res = trace_if_async(
            log::Level::Info,
            &tracer,
            "Read realtime WAL entries",
            |child| async move {
                let mut all_wal_entries: Vec<(TransactionId, Vec<WalEntry>)> = vec![];
                let mut stream = wal_stream(
                    merged_metadata_stream,
                    WalStreamOptions {
                        max_num_bytes: if skip_realtime_wal_memory_limit {
                            None
                        } else {
                            Some(get_realtime_read_per_query_bytes_limit())
                        },
                        ..Default::default()
                    },
                );
                let mut permit: Option<SemaphorePermit<'_>> = None;
                let mut query_bytes = 0;
                let mut num_segment_wal_entries = 0;
                let mut num_object_wal_entries = 0;
                let mut read_entire_wal_stream = true;

                if let Some(sync_point) = before_stream_first_realtime_wal_entry.take() {
                    sync_point.send_and_wait().await;
                }

                while let Some(wal_item) = stream.next().await {
                    let wal_data = wal_item.item?;

                    if wal_data.detail == WalDataDetail::ExhaustedMaxNumBytes {
                        read_entire_wal_stream = false;
                        break;
                    }

                    // Process each xact entry one by one
                    for entry in wal_data.entries {
                        let entry_bytes = entry
                            .2
                            .iter()
                            .map(|m| m.as_dyn_wal_metadata().num_bytes())
                            .sum::<usize>();

                        // Check per-query limit first
                        if !skip_realtime_wal_memory_limit
                            && query_bytes + entry_bytes > get_realtime_read_per_query_bytes_limit()
                        {
                            read_entire_wal_stream = false;
                            break;
                        }

                        // Then check global limit
                        if !skip_realtime_wal_memory_limit {
                            let permit_count = u32::try_from(entry_bytes)
                                .map_err(|_| anyhow::anyhow!("Realtime WAL entry size {} bytes is too large to fit in u32 (max: {} bytes)", entry_bytes, u32::MAX))?;
                            match realtime_global_memory_limit_bytes.try_start_many(permit_count) {
                                Ok(new_permit) => {
                                    // Merge with existing permit if we have one
                                    permit = match permit {
                                        Some(mut p) => {
                                            p.merge(new_permit);
                                            Some(p)
                                        }
                                        None => Some(new_permit),
                                    };
                                }
                                Err(
                                    TryAsyncLimitError::NoPermits
                                    | TryAsyncLimitError::InsufficientCapacity,
                                ) => {
                                    // Hit global memory limit, stop reading
                                    read_entire_wal_stream = false;
                                    break;
                                }
                                Err(e) => {
                                    return Err::<_, util::anyhow::Error>(e.into());
                                }
                            }
                        }

                        // Categorize each WAL entry as a segment or object WAL entry,
                        // by looking at its object ID and xact ID. IF the xact_id is
                        // less than the last_processed_xact_id, then it is a segment
                        // WAL entry. Otherwise, it is an object WAL entry.
                        for wal_entry in entry.1.iter() {
                            let object_segment_info =
                                &object_segment_ids[&wal_entry.full_object_id().to_owned()];
                            if Some(entry.0)
                                <= object_segment_info.object_metadata.last_processed_xact_id
                            {
                                num_segment_wal_entries += 1;
                            } else {
                                num_object_wal_entries += 1;
                            }
                        }

                        all_wal_entries.push((entry.0, entry.1));
                        query_bytes += entry_bytes;
                    }
                }


                let mut filtered_wal_entries = 0;
                // We don't need to access any entries that do not correspond to this root_span_id
                if let Some(root_span_id_filters) = root_span_id_filters {
                    all_wal_entries.iter_mut().for_each(|(_, entries)| {
                        let pre_filtered = entries.len();
                        entries.retain(|entry| {
                            root_span_id_filters
                                .iter()
                                .any(|root_span_id| entry.root_span_id == *root_span_id)
                        });
                        filtered_wal_entries += pre_filtered - entries.len();
                    });
                }

                child.increment_counter("num_segment_wal_entries", num_segment_wal_entries as u64);
                child.increment_counter("num_object_wal_entries", num_object_wal_entries as u64);
                child.increment_counter("num_wal_entries_filtered_out", filtered_wal_entries as u64);
                Ok((all_wal_entries, permit, query_bytes, read_entire_wal_stream))
            },
        )
        .await?;
        Ok(res)
    }

    async fn read_realtime_and_compute_excluded_merged_docs(
        opts: &IndexWalReaderOptsInner,
        object_ids: &[FullObjectIdOwned],
        config_with_store: &ConfigWithStore,
        full_schema: &Schema,
        tantivy_schema: &WritableTantivySchema,
        durable_segment_specs: &HashMap<Uuid, DynamicIndexSpec>,
        object_segment_ids: &HashMap<FullObjectIdOwned, ObjectSegmentInfo>,
        // These three fields should line up.
        segment_ids: &[Uuid],
        segment_object_infos: &[SegmentObjectInfo],
        segment_index_metadatas: &[Option<LastCompactedIndexMeta>],
        root_span_id_filters: Option<&HashSet<String>>,
        tantivy_executor: Option<Arc<tantivy::Executor>>,
        tracer: Option<Arc<TracedNode>>,
        testing_global_realtime_memory_bytes_limit: Option<&'static AsyncLimit>,
        testing_sync_point_before_stream_first_realtime_wal_entry: Option<
            TwoWaySyncPointSendAndWait,
        >,
        testing_sync_point_after_get_all_realtime_wal_entries: Option<TwoWaySyncPointSendAndWait>,
    ) -> Result<(
        // excluded records
        HashMap<SegmentId, Vec<u32>>,
        // merged wal docs
        Vec<Value>,
        // realtime state
        IndexWalReaderRealtimeState,
        // realtime state permit
        Option<SemaphorePermit<'static>>,
    )> {
        let (realtime_wal_entries, realtime_state, realtime_state_permit) =
            if opts.skip_realtime_wal_entries || get_realtime_read_per_query_bytes_limit() == 0 {
                (vec![], IndexWalReaderRealtimeState::Disabled, None)
            } else {
                let realtime_global_memory_limit_bytes = testing_global_realtime_memory_bytes_limit
                    .unwrap_or(&global_limits().realtime_read_max_inflight_bytes);
                let num_realtime_objects = opts.max_realtime_objects.min(object_ids.len());
                let realtime_object_ids = &object_ids[..num_realtime_objects];

                // Filter object_segment_ids, segment_ids, segment_object_infos,
                // and segment_index_metadatas to only include the
                // realtime_object_ids
                let realtime_object_segment_ids = object_segment_ids
                    .iter()
                    .filter(|(object_id, _)| realtime_object_ids.contains(object_id))
                    .map(|(object_id, object_segment_info)| {
                        (object_id.clone(), object_segment_info.clone())
                    })
                    .collect::<HashMap<_, _>>();
                let realtime_segment_id_set = realtime_object_segment_ids
                    .values()
                    .flat_map(|object_segment_info| object_segment_info.segment_ids.iter())
                    .cloned()
                    .collect::<HashSet<_>>();

                let mut realtime_segment_ids = Vec::new();
                let mut realtime_segment_object_infos = Vec::new();
                let mut realtime_segment_index_metadatas = Vec::new();

                for i in 0..segment_ids.len() {
                    if realtime_segment_id_set.contains(&segment_ids[i]) {
                        realtime_segment_ids.push(segment_ids[i].clone());
                        realtime_segment_object_infos.push(segment_object_infos[i].clone());
                        realtime_segment_index_metadatas.push(segment_index_metadatas[i].clone());
                    }
                }

                let (
                    realtime_wal_entries,
                    realtime_entries_permit,
                    realtime_bytes_read,
                    read_entire_wal_stream,
                ) = Self::read_realtime_wal_entries(
                    config_with_store.clone(),
                    &realtime_segment_ids,
                    &realtime_segment_object_infos,
                    &realtime_segment_index_metadatas,
                    &realtime_object_segment_ids,
                    root_span_id_filters,
                    tracer.clone(),
                    opts.skip_realtime_wal_memory_limit,
                    realtime_global_memory_limit_bytes,
                    testing_sync_point_before_stream_first_realtime_wal_entry,
                )
                .await?;

                // The minimum xact_id we must reach in order to provide a
                // semantically-consistent query is the max(last_compacted_xact_id)
                // across all segments we are reading.
                let minimum_xact_id = realtime_segment_index_metadatas
                    .iter()
                    .map(|meta| meta.as_ref().map(|meta| meta.xact_id))
                    .max()
                    .flatten();
                // This is the last xact_id we read from realtime.
                let actual_xact_id = realtime_wal_entries
                    .last()
                    .map(|(xact_id, _)| xact_id)
                    .copied();
                let realtime_state = if read_entire_wal_stream {
                    IndexWalReaderRealtimeState::On {
                        minimum_xact_id,
                        read_bytes: realtime_bytes_read,
                        // Since we didn't abort the realtime read, that
                        // means we have read at least up to the minimum
                        // xact id, even if the only unprocessed entries we
                        // read are less than than xact_id.
                        actual_xact_id: std::cmp::max(actual_xact_id, minimum_xact_id),
                    }
                } else {
                    IndexWalReaderRealtimeState::ExhaustedMemoryBudget {
                        minimum_xact_id,
                        read_bytes: realtime_bytes_read,
                        actual_xact_id,
                    }
                };
                (
                    realtime_wal_entries,
                    realtime_state,
                    realtime_entries_permit,
                )
            };

        if let Some(sync_point) = testing_sync_point_after_get_all_realtime_wal_entries {
            sync_point.send_and_wait().await;
        }

        if realtime_wal_entries.is_empty() {
            return Ok((
                HashMap::new(),
                Vec::new(),
                realtime_state,
                realtime_state_permit,
            ));
        }

        let num_realtime_wal_entries = realtime_wal_entries
            .iter()
            .map(|x| x.1.len())
            .sum::<usize>();
        tracing::info!(
            num_realtime_wal_entries = num_realtime_wal_entries,
            "Merging and excluding realtime records",
        );
        let merged_wal_entries = merge_wal_entries(realtime_wal_entries);

        let (excluded_records, merged_wal_entries) = trace_if_async(
            log::Level::Info,
            &tracer,
            "Get excluded records",
            |child| async move {
                process_existing_compacted_records(
                    child,
                    full_schema,
                    tantivy_schema,
                    durable_segment_specs,
                    tantivy_executor.clone(),
                    config_with_store.global_store.clone(),
                    &segment_ids,
                    merged_wal_entries,
                )
                .await
            },
        )
        .await?;

        // Construct queryable, fully merged rows from the WAL entries.
        let merged_wal_docs = trace_if(log::Level::Info, &tracer, "Merge WAL entries", |_child| {
            make_in_memory_wal_rows(merged_wal_entries)
        })?;

        Ok((
            excluded_records,
            merged_wal_docs,
            realtime_state,
            realtime_state_permit,
        ))
    }
}

fn make_dynamic_tantivy_index(
    directory: DynamicReadonlyDirectory,
    executor: Option<Arc<tantivy::Executor>>,
) -> Result<tantivy::Index> {
    let mut tantivy_index = tantivy::Index::open(directory)?;
    if let Some(executor) = executor {
        tantivy_index.set_shared_multithread_executor(executor)?;
    }
    Ok(tantivy_index)
}

fn get_full_row_id_field(
    schema: &util::schema::Schema,
    tantivy_schema: &tantivy::schema::Schema,
) -> Result<tantivy::schema::Field> {
    let full_row_id_field = schema
        .find_field("_full_row_id")
        .ok_or_else(|| anyhow::anyhow!("No _full_row_id field"))?
        .find_tantivy_field(TantivyFieldFilterOpts::default())
        .ok_or_else(|| anyhow::anyhow!("No indexed _full_row_id field"))?;
    Ok(tantivy_schema.get_field(&full_row_id_field.name)?)
}

async fn process_existing_compacted_records(
    tracer: Option<Arc<TracedNode>>,
    full_schema: &util::schema::Schema,
    tantivy_schema: &WritableTantivySchema,
    durable_segment_specs: &HashMap<Uuid, DynamicIndexSpec>,
    executor: Option<Arc<tantivy::Executor>>,
    global_store: Arc<dyn GlobalStore>,
    segment_ids: &[Uuid],
    mut merged_wal_entries: HashMap<FullRowIdOwned, WalEntry>,
) -> Result<(
    HashMap<SegmentId, Vec<DocId>>,
    HashMap<FullRowIdOwned, WalEntry>,
)> {
    let full_row_ids: HashSet<FullRowIdOwned> = merged_wal_entries.keys().cloned().collect();
    if full_row_ids.is_empty() {
        return Ok((HashMap::new(), HashMap::new()));
    }

    let full_row_id_vec = full_row_ids.iter().map(|x| x.as_ref()).collect::<Vec<_>>();
    let segment_id_membership = trace_if_async(
        log::Level::Info,
        &tracer,
        "Query segment id membership",
        |child| {
            child.increment_counter("num_segments", segment_ids.len() as u64);
            child.increment_counter("num_row_ids", full_row_id_vec.len() as u64);
            async {
                global_store
                    .query_id_segment_membership(
                        IdSegmentMembershipType::RowId,
                        segment_ids,
                        &full_row_id_vec,
                    )
                    .await
            }
        },
    )
    .await?;
    let matching_segment_ids = segment_id_membership.values().collect::<HashSet<_>>();

    let matching_segment_specs = matching_segment_ids
        .iter()
        .filter_map(|segment_id| durable_segment_specs.get(segment_id).cloned())
        .collect::<Vec<_>>();

    let num_matching_segments = matching_segment_specs.len();

    let directory =
        DynamicReadonlyDirectory::new(tantivy_schema.schema.clone(), matching_segment_specs)
            .await?;

    directory.load_segment_footer(tracer.clone()).await?;

    let full_row_id_field = get_full_row_id_field(full_schema, directory.schema())?;
    let tracer = tracer.clone();
    let records = await_spawn_blocking!(
        move |tantivy_schema: &WritableTantivySchema| {
            let index = make_dynamic_tantivy_index(directory.clone(), executor.clone())?;
            let reader = trace_if(log::Level::Info, &tracer, "Open reader", |child| {
                child.increment_counter("num_segments", num_matching_segments as u64);
                child.increment_counter(
                    "num_chunks",
                    index.searchable_segment_metas()?.len() as u64,
                );
                index.reader()
            })?;
            let searcher = reader.searcher();
            let collector = tantivy::collector::DocSetCollector {};
            let terms = full_row_ids
                .iter()
                .map(|id| Term::from_field_text(full_row_id_field, id.to_string().as_str()));
            let term_query = TermSetQuery::new(terms);
            let records = trace_if(log::Level::Info, &tracer, "Search", |_child| {
                searcher.search(&term_query, &collector)
            })?;
            let mut excluded_ids: HashMap<SegmentId, Vec<DocId>> = HashMap::new();
            let executor = executor.unwrap_or(Arc::new(tantivy::Executor::SingleThread));

            let merge_control_fields = merged_wal_entries
                .iter()
                .map(|(k, v)| {
                    (
                        k.as_ref(),
                        MergeControlFields {
                            _is_merge: v._is_merge.unwrap_or(false),
                            _replace_sticky_system_fields: v
                                ._replace_sticky_system_fields
                                .unwrap_or(false),
                            _object_delete: v._object_delete.unwrap_or(false),
                        },
                    )
                })
                .collect::<HashMap<_, _>>();

            let excluded_docs = ExcludedDoc::extract_from_searcher(
                tracer.clone(),
                records,
                &searcher,
                &executor,
                &merge_control_fields,
                tantivy_schema,
            )?;

            for doc in excluded_docs {
                excluded_ids
                    .entry(directory.segment_ord_to_segment_id()[&doc.doc_address.segment_ord])
                    .or_default()
                    .push(doc.doc_address.doc_id);
                let mut base_entry = doc.base_entry;
                let existing_wal_entry = merged_wal_entries
                    .get_mut(&base_entry.full_row_id().to_owned())
                    .unwrap();
                base_entry.merge(std::mem::take(existing_wal_entry))?;
                // Remove any audit entries earlier than the latest _xact_id. Once we
                // start indexing the audit log, we'll want to preserve this.
                base_entry
                    .audit_data
                    .0
                    .retain(|x| x._xact_id >= base_entry._xact_id);
                *existing_wal_entry = base_entry;
            }

            Ok::<_, util::anyhow::Error>((excluded_ids, merged_wal_entries))
        },
        tantivy_schema
    )???;
    Ok(records)
}

// Merges the WAL entries for each segment together with the per-object WAL entries.
fn merge_wal_entries(
    realtime_wal_entries: Vec<(TransactionId, Vec<WalEntry>)>,
) -> HashMap<FullRowIdOwned, WalEntry> {
    let mut merged_wal_entries: HashMap<FullRowIdOwned, WalEntry> = HashMap::new();
    for (_, entries) in realtime_wal_entries {
        for entry in entries {
            merged_wal_entries
                .entry(entry.full_row_id().to_owned())
                .and_modify(|existing_entry| existing_entry.merge(entry.clone()).unwrap())
                .or_insert_with(|| entry);
        }
    }
    merged_wal_entries
}

fn make_in_memory_wal_rows(
    merged_wal_entries: HashMap<FullRowIdOwned, WalEntry>,
) -> Result<Vec<Value>> {
    let index_docs = merged_wal_entries
        .into_values()
        .filter(|v| {
            // Skip all deleted records. The fact that they are in the WAL means that we won't
            // query the un-deleted version from the tantivy segment, so we can just skip them here.
            !v._object_delete.unwrap_or(false)
        })
        .map(|wal_entry| IndexDocument { wal_entry }.to_json_value())
        .collect::<Result<Vec<_>>>()?;

    Ok(index_docs)
}

async fn get_segment_statistics(
    tracer: Option<Arc<TracedNode>>,
    object_ids: &[FullObjectIdOwned],
    segment_ids: &[Uuid],
    global_store: Arc<dyn GlobalStore>,
    sort: &Option<SegmentBatchingSortSpec>,
    filters: &[SegmentEliminationFilterSpec],
) -> Result<(
    // [field] -> [segment_id] -> stats
    HashMap<String, HashMap<Uuid, SegmentFieldStatistics>>,
    // [row_id] -> [segment_id]
    HashMap<String, HashSet<Uuid>>,
    // [root_span_id] -> [segment_id]
    HashMap<String, HashSet<Uuid>>,
)> {
    trace_if_async(
        log::Level::Info,
        &tracer,
        "Get segment statistics",
        |_child| async {
            let mut range_fields: HashSet<&str> = HashSet::new();
            let mut id_values: HashSet<FullRowId> = HashSet::new();
            let mut root_span_id_values: HashSet<FullRowId> = HashSet::new();

            if let Some(sort) = sort {
                range_fields.insert(sort.field.as_str());
            }
            for f in filters {
                match f {
                    SegmentEliminationFilterSpec::Range { field, .. } => {
                        range_fields.insert(field.as_str());
                    }
                    SegmentEliminationFilterSpec::IdFilter { ids, root_span_ids } => {
                        // We don't know which object the id belongs to, so we need to add all of them.
                        for object_id in object_ids {
                            for id in ids {
                                id_values
                                    .insert(FullRowId::from_full_object_id(object_id.as_ref(), id));
                            }
                            for root_span_id in root_span_ids {
                                root_span_id_values.insert(FullRowId::from_full_object_id(
                                    object_id.as_ref(),
                                    root_span_id,
                                ));
                            }
                        }
                    }
                }
            }

            let fields_vec = range_fields.iter().copied().collect::<Vec<_>>();
            let id_values_vec = id_values.into_iter().collect::<Vec<_>>();
            let root_span_id_values_vec = root_span_id_values.into_iter().collect::<Vec<_>>();

            let (all_field_stats, row_id_membership, root_span_id_membership) = join!(
                global_store.query_field_statistics(segment_ids, &fields_vec),
                global_store.query_id_segment_membership(
                    IdSegmentMembershipType::RowId,
                    segment_ids,
                    &id_values_vec
                ),
                global_store.query_id_segment_membership(
                    IdSegmentMembershipType::RootSpanId,
                    segment_ids,
                    &root_span_id_values_vec
                ),
            );
            let mut stats_by_field: HashMap<String, HashMap<Uuid, SegmentFieldStatistics>> =
                HashMap::new();
            for (segment_id, field_stats) in all_field_stats? {
                for (field, stats) in field_stats {
                    stats_by_field
                        .entry(field)
                        .or_default()
                        .insert(segment_id, stats);
                }
            }

            let mut id_to_segment_id: HashMap<String, HashSet<Uuid>> = HashMap::new();
            let row_id_membership = row_id_membership?;
            for (row_id, segment_id) in row_id_membership {
                let id = &row_id.id;
                if let Some(segment_ids) = id_to_segment_id.get_mut(id) {
                    segment_ids.insert(segment_id);
                } else {
                    id_to_segment_id.insert(id.to_string(), HashSet::from([segment_id]));
                }
            }

            let mut root_span_id_to_segment_id: HashMap<String, HashSet<Uuid>> = HashMap::new();
            let root_span_id_membership = root_span_id_membership?;
            for (root_span_id, segment_id) in root_span_id_membership {
                let id = &root_span_id.id;
                if let Some(segment_ids) = root_span_id_to_segment_id.get_mut(id) {
                    segment_ids.insert(segment_id);
                } else {
                    root_span_id_to_segment_id.insert(id.to_string(), HashSet::from([segment_id]));
                }
            }

            Ok((stats_by_field, id_to_segment_id, root_span_id_to_segment_id))
        },
    )
    .await
}

#[derive(Debug)]
struct ExcludedDocQuery {
    query: Box<dyn Query>,
    excluded_records: HashMap<SegmentId, Vec<DocId>>,
}

impl ExcludedDocQuery {
    pub fn new(query: Box<dyn Query>, excluded_records: HashMap<SegmentId, Vec<DocId>>) -> Self {
        Self {
            query,
            excluded_records,
        }
    }
}

impl QueryClone for ExcludedDocQuery {
    fn box_clone(&self) -> Box<dyn Query> {
        Box::new(Self {
            query: self.query.box_clone(),
            excluded_records: self.excluded_records.clone(),
        })
    }
}

impl Query for ExcludedDocQuery {
    fn weight(&self, enable_scoring: EnableScoring<'_>) -> tantivy::Result<Box<dyn Weight>> {
        Ok(Box::new(ExcludedDocWeight {
            query: self.query.weight(enable_scoring)?,
            excluded_records: self.excluded_records.clone(),
        }))
    }
}

struct ExcludedDocWeight {
    query: Box<dyn Weight>,
    excluded_records: HashMap<SegmentId, Vec<DocId>>,
}

impl Weight for ExcludedDocWeight {
    fn scorer(
        &self,
        reader: &SegmentReader,
        boost: tantivy::Score,
    ) -> tantivy::Result<Box<dyn tantivy::query::Scorer>> {
        if let Some(excluded_doc_ids) = self.excluded_records.get(&reader.segment_id()) {
            let mut excluded_docs = BitSet::with_max_value(reader.max_doc());
            for doc_id in excluded_doc_ids {
                excluded_docs.insert(*doc_id);
            }

            let bit_doc_set: BitSetDocSet = excluded_docs.into();

            Ok(Box::new(Exclude::new(
                self.query.scorer(reader, boost)?,
                bit_doc_set,
            )))
        } else {
            self.query.scorer(reader, boost)
        }
    }

    fn explain(
        &self,
        reader: &SegmentReader,
        doc: DocId,
    ) -> tantivy::Result<tantivy::query::Explanation> {
        // TODO
        self.query.explain(reader, doc)
    }
}

#[derive(Debug, Clone)]
struct ExcludedDoc {
    // A WalEntry that can serve as the "merge base" for the realtime WalEntry.
    //
    // For merge entries, this will contain the full document data, so we don't miss anything.
    //
    // For non-merge entries, this will contain just the fields that are preserved by
    // WalEntry::merge, so keep this logic in sync with the logic in
    // brainstore/storage/src/wal_entry.rs.
    base_entry: WalEntry,
    doc_address: DocAddress,
}

#[derive(Debug, Clone, Default)]
struct MergeControlFields {
    _is_merge: bool,
    _replace_sticky_system_fields: bool,
    _object_delete: bool,
}

impl ExcludedDoc {
    // This is like a mini-version of extract_docs_into_rows_columnar, but just for the fields we care about
    pub fn extract_from_searcher<I: IntoIterator<Item = DocAddress>>(
        tracer: Option<Arc<TracedNode>>,
        docs: I,
        searcher: &tantivy::Searcher,
        executor: &tantivy::Executor,
        merge_control_fields: &HashMap<FullRowId, MergeControlFields>,
        tantivy_schema: &WritableTantivySchema,
    ) -> Result<Vec<Self>> {
        let mut segment_to_docs: HashMap<u32, Vec<(DocAddress, usize)>> = HashMap::new();
        let mut num_docs = 0;
        for doc in docs {
            segment_to_docs
                .entry(doc.segment_ord)
                .or_default()
                .push((doc, num_docs));
            num_docs += 1;
        }

        let rows = std::sync::Mutex::new(vec![None; num_docs]);
        let span = tracing::Span::current();
        let _ = executor.map(
            |(segment_ord, docs)| {
                let _guard = span.enter();
                let segment_reader = searcher.segment_reader(segment_ord);
                let fast_fields = segment_reader.fast_fields();
                let _full_row_id_field = fast_fields.str("_full_row_id")?.ok_or_else(|| {
                    TantivyError::InternalError("No _full_row_id field".to_string())
                })?;
                let _pagination_key_field = fast_fields.u64("_pagination_key")?;
                let created_field = fast_fields.date("created")?;
                let res = trace_if(
                    log::Level::Info,
                    &tracer,
                    "Retrieve documents",
                    |_child| -> Result<()> {
                        for (doc, idx) in docs {
                            let full_row_id_bytes = str_column_read_single_bytes_for_doc(
                                &_full_row_id_field,
                                doc.doc_id,
                            )?;
                            let full_row_id = std::str::from_utf8(&full_row_id_bytes)?
                                .parse::<FullRowIdOwned>()?;
                            let merge_control_fields = merge_control_fields
                                .get(&full_row_id.as_ref())
                                .cloned()
                                .unwrap_or_default();
                            let base_entry = if merge_control_fields._is_merge {
                                let doc: tantivy::schema::document::TantivyDocument =
                                    searcher.doc(doc)?;
                                let index_doc = IndexDocument::from_tantivy_document(
                                    &doc,
                                    &tantivy_schema.invert_fields,
                                )?;
                                index_doc.wal_entry
                            } else if merge_control_fields._replace_sticky_system_fields
                                || merge_control_fields._object_delete
                            {
                                WalEntry::default()
                            } else {
                                let _pagination_key =
                                    PaginationKey(collect_column_raw_value_for_doc(
                                        &_pagination_key_field,
                                        doc.doc_id,
                                    )?);
                                let created = DateTime::<Utc>::from_timestamp_micros(
                                    collect_column_raw_value_for_doc(&created_field, doc.doc_id)?
                                        .into_timestamp_micros(),
                                )
                                .ok_or_else(|| anyhow!("Invalid DateTime"))?;
                                WalEntry {
                                    _pagination_key,
                                    created,
                                    ..Default::default()
                                }
                            };
                            let base_entry = WalEntry {
                                id: full_row_id.id,
                                _object_id: full_row_id.object_id,
                                _object_type: full_row_id.object_type,
                                ..base_entry
                            };
                            rows.lock().unwrap()[idx] = Some(ExcludedDoc {
                                base_entry,
                                doc_address: doc,
                            });
                        }
                        Ok(())
                    },
                );
                match res {
                    Ok(()) => Ok(()),
                    Err(e) => Err(TantivyError::InternalError(format!("{}", e))),
                }
            },
            segment_to_docs.into_iter(),
        )?;

        let rows = std::mem::take(&mut *rows.lock().unwrap())
            .into_iter()
            .flatten()
            .collect();

        Ok(rows)
    }
}

#[cfg(test)]
mod tests {
    use std::collections::{HashMap, HashSet};

    use serde_json::json;
    use tantivy::{directory::DirectoryClone, query::AllQuery};
    use util::{
        await_spawn_blocking,
        chrono::DateTime,
        itertools::Itertools,
        once_cell::sync::Lazy,
        system_types::{make_object_schema, FullObjectId, ObjectIdOwned},
        test_util::{assert_hashmap_eq, two_way_sync_point},
        xact::TransactionId,
    };

    use crate::{
        index_document::{make_full_schema, IndexDocument},
        index_wal_reader_test_util::{get_reader_full_docs, sanitize_reader_doc},
        process_wal::{
            compact_segment_wal, process_object_wal, CompactSegmentWalInput,
            CompactSegmentWalOptionalInput, CompactSegmentWalOptions,
            CompactSegmentWalTestingSyncPoints, ProcessObjectWalInput,
            ProcessObjectWalOptionalInput, ProcessObjectWalOptions,
            ProcessObjectWalTestingSyncPoints,
        },
        tantivy_index::TantivyIndexWriterOpts,
        test_util::TmpDirConfigWithStore,
        wal::wal_insert_unmerged,
        wal_entry::WalEntry,
    };

    use super::*;

    fn make_default_full_schema() -> util::schema::Schema {
        make_full_schema(&make_object_schema(FullObjectId::default().object_type).unwrap()).unwrap()
    }

    async fn make_default_reader(config: &ConfigWithStore) -> IndexWalReader {
        make_index_wal_reader(config, Default::default(), Default::default()).await
    }

    async fn get_values(config: &ConfigWithStore) -> HashMap<String, TransactionId> {
        get_reader_values(&make_default_reader(config).await).await
    }

    async fn get_reader_values(
        index_wal_reader: &IndexWalReader,
    ) -> HashMap<String, TransactionId> {
        let full_docs = get_reader_full_docs(index_wal_reader).await;
        full_docs
            .into_iter()
            .map(|(id, doc)| (id, doc.wal_entry._xact_id))
            .collect()
    }

    async fn add_wal_entries_full(
        config: &ConfigWithStore,
        entries: Vec<WalEntry>,
        process_wal_options: ProcessObjectWalOptions,
        testing_sync_points: ProcessObjectWalTestingSyncPoints,
    ) -> HashSet<Uuid> {
        wal_insert_unmerged(config.wal.as_ref(), &*config.global_store, entries)
            .await
            .unwrap();
        let process_output = process_object_wal(
            ProcessObjectWalInput {
                object_id: FullObjectId::default(),
                config,
            },
            ProcessObjectWalOptionalInput {
                testing_sync_points,
                ..Default::default()
            },
            process_wal_options,
        )
        .await
        .unwrap();
        process_output.modified_segment_ids
    }

    async fn run_compact_segment_wal(
        segment_id: Uuid,
        config: &ConfigWithStore,
        compact_segment_wal_options: CompactSegmentWalOptions,
        testing_sync_points: CompactSegmentWalTestingSyncPoints,
    ) {
        compact_segment_wal(
            CompactSegmentWalInput {
                segment_id,
                index_store: config.index.clone(),
                schema: make_default_full_schema(),
                global_store: config.global_store.clone(),
                locks_manager: &*config.locks_manager,
            },
            CompactSegmentWalOptionalInput {
                testing_sync_points,
                ..Default::default()
            },
            compact_segment_wal_options,
        )
        .await
        .unwrap();
    }

    async fn make_index_wal_reader(
        config_with_store: &ConfigWithStore,
        optional_input: IndexWalReaderOptionalInput,
        opts: IndexWalReaderOpts,
    ) -> IndexWalReader {
        IndexWalReader::new(
            IndexWalReaderInput {
                config_with_store,
                full_schema: make_default_full_schema(),
                object_ids: &[FullObjectId::default().to_owned()],
                filters: &[],
                sort: &None,
            },
            optional_input,
            opts,
        )
        .await
        .unwrap()
    }

    fn generate_large_data() -> serde_json::Map<String, serde_json::Value> {
        // Leave room for the auxiliary fields.
        json!({
            "input": "x".repeat(get_realtime_read_per_query_bytes_limit() - 1024)
        })
        .as_object()
        .unwrap()
        .clone()
    }

    fn large_data_size_bytes() -> usize {
        serde_json::to_string(&generate_large_data()).unwrap().len()
    }

    const fn make_lazy_realtime_memory_limit() -> Lazy<AsyncLimit> {
        Lazy::new(|| AsyncLimit::new("test_realtime_read_memory_limit", 5 * 1024 * 1024))
    }

    // This test illustrates the original implementation, but no longer works.
    // #[tokio::test]
    // async fn test_hand_rolled() {
    //     let fixture = TestFixture::new();

    //     let initial_wal_entries = vec![(
    //         TransactionId(1),
    //         vec![
    //             WalEntry {
    //                 _xact_id: TransactionId(1),
    //                 id: "row0".to_string(),
    //                 data: json!({"input":"foo"}).as_object().unwrap().clone(),
    //                 ..Default::default()
    //             },
    //             WalEntry {
    //                 _xact_id: TransactionId(1),
    //                 id: "row1".to_string(),
    //                 data: json!({"input":"bar"}).as_object().unwrap().clone(),
    //                 ..Default::default()
    //             },
    //         ],
    //     )];

    //     let segment_id = "bt_segment0";
    //     fixture.initialize_segment_metadata(segment_id).await;
    //     fixture
    //         .write_wal_to_segment(segment_id, initial_wal_entries)
    //         .await;

    //     compact_segment_wal(
    //         fixture.compact_segment_wal_input(segment_id),
    //         Default::default(),
    //         BASIC_COMPACT_SEGMENT_WAL_OPTIONS,
    //     )
    //     .await
    //     .unwrap();

    //     let docs = fixture.read_segment_docs(segment_id).await;
    //     assert_eq!(docs.len(), 2);

    //     // Now, insert a couple WAL entries
    //     let new_wal_entries = vec![
    //         (
    //             TransactionId(2),
    //             vec![
    //                 WalEntry {
    //                     _xact_id: TransactionId(2),
    //                     id: "row0".to_string(),
    //                     data: json!({"input":"foo_updated"}).as_object().unwrap().clone(),
    //                     ..Default::default()
    //                 },
    //                 WalEntry {
    //                     _xact_id: TransactionId(2),
    //                     id: "row2".to_string(),
    //                     data: json!({"input":"baz"}).as_object().unwrap().clone(),
    //                     ..Default::default()
    //                 },
    //             ],
    //         ),
    //         (
    //             TransactionId(3),
    //             vec![
    //                 WalEntry {
    //                     _xact_id: TransactionId(3),
    //                     id: "row0".to_string(),
    //                     data: json!({"input":"foo_updated_again"})
    //                         .as_object()
    //                         .unwrap()
    //                         .clone(),
    //                     ..Default::default()
    //                 },
    //                 WalEntry {
    //                     _xact_id: TransactionId(3),
    //                     id: "row3".to_string(),
    //                     data: json!({"input":"qux"}).as_object().unwrap().clone(),
    //                     ..Default::default()
    //                 },
    //             ],
    //         ),
    //     ];

    //     fixture
    //         .write_wal_to_segment(segment_id, new_wal_entries)
    //         .await;

    //     // The new WAL entries should not be visible yet.
    //     let docs = fixture.read_segment_docs(segment_id).await;
    //     assert_eq!(docs.len(), 2);

    //     let segment_wal = fixture.make_segment_wal();

    //     let last_compacted_xact_id = fixture
    //         .config
    //         .global_store
    //         .query_segment_metadatas(&[segment_id])
    //         .await
    //         .unwrap()
    //         .remove(0)
    //         .last_compacted_index_meta
    //         .map(|x| x.xact_id)
    //         .unwrap();
    //     assert_eq!(last_compacted_xact_id, TransactionId(1));
    //     let wal_entries: Vec<(TransactionId, Vec<WalEntry>)> = segment_wal
    //         .wal_stream(
    //             WALScope::Segment(segment_id),
    //             Some(TransactionId(last_compacted_xact_id.0 + 1)),
    //         )
    //         .await
    //         .unwrap()
    //         .try_collect()
    //         .await
    //         .unwrap();
    //     assert_eq!(wal_entries.len(), 2); // Two get combined
    //     eprintln!("{:?}", wal_entries);

    //     // ----
    //     // First, find the addresses of the docs in the WAL
    //     // ----
    //     let tantivy_index_wrapper = fixture.make_tantivy_index_wrapper(segment_id).await;
    //     let wal_entries_clone = wal_entries.clone();
    //     let excluded_records = await_spawn_blocking!(move || {
    //         let reader = tantivy_index_wrapper.make_reader_blocking().unwrap();
    //         let searcher = reader.searcher();
    //         let collector = tantivy::collector::DocSetCollector {};

    //         let tantivy_schema = tantivy_index_wrapper.index.schema();

    //         let ids: HashSet<String> = wal_entries_clone
    //             .iter()
    //             .flat_map(|(_, entries)| entries.iter().map(|e| e.id.as_str().to_owned()))
    //             .collect();

    //         let id_field = tantivy_index_wrapper
    //             .schema
    //             .find_field("id")
    //             .unwrap()
    //             .find_tantivy_field(TantivyFieldFilterOpts::default())
    //             .unwrap();
    //         let id_field_tantivy = tantivy_schema.get_field(&id_field.name).unwrap();

    //         let terms = ids.iter().map(|id| {
    //             TermQuery::new(
    //                 Term::from_field_text(id_field_tantivy, id),
    //                 IndexRecordOption::default(),
    //             )
    //         });
    //         let term_query = BooleanQuery::new(
    //             terms
    //                 .into_iter()
    //                 .map(|t| (Occur::Should, Box::new(t) as Box<dyn Query>))
    //                 .collect::<Vec<_>>(),
    //         );

    //         let excluded_records = searcher.search(&term_query, &collector).unwrap();

    //         assert_eq!(excluded_records.len(), 1);
    //         eprintln!("excluded_records: {:?}", excluded_records);

    //         let mut excluded_segment_records: HashMap<SegmentId, Vec<DocId>> = HashMap::new();
    //         let segment_readers = searcher.segment_readers();
    //         for record in excluded_records {
    //             excluded_segment_records
    //                 .entry(segment_readers[record.segment_ord as usize].segment_id())
    //                 .or_default()
    //                 .push(record.doc_id);
    //         }
    //         excluded_segment_records
    //     })
    //     .unwrap();

    //     // ----
    //     // Next, create a custom query that excludes the excluded records
    //     // ----
    //     let tantivy_index_wrapper = fixture.make_tantivy_index_wrapper(segment_id).await;
    //     let excluded_records_ref = &excluded_records;
    //     await_spawn_blocking!(
    //         move |excluded_records: &HashMap<SegmentId, Vec<DocId>>| {
    //             let reader = tantivy_index_wrapper.make_reader_blocking().unwrap();
    //             let searcher = reader.searcher();
    //             let collector = tantivy::collector::DocSetCollector {};

    //             let excluded_doc_query =
    //                 ExcludedDocQuery::new(Box::new(AllQuery {}), excluded_records.clone());

    //             let addresses = searcher.search(&excluded_doc_query, &collector).unwrap();

    //             // We should only have row1
    //             assert_eq!(addresses.len(), 1);
    //             eprintln!("addresses: {:?}", addresses);

    //             let mut row_ids: Vec<FullRowIdOwned> = Vec::new();
    //             for address in addresses {
    //                 let doc: tantivy::schema::document::TantivyDocument =
    //                     searcher.doc(address).unwrap();
    //                 let index_doc = IndexDocument::from_tantivy_document(
    //                     &doc,
    //                     &tantivy_index_wrapper.writable_schema.invert_fields,
    //                 )
    //                 .unwrap();
    //                 row_ids.push(index_doc.wal_entry.full_row_id().to_owned());
    //             }
    //             assert_eq!(row_ids.len(), 1);
    //             assert_eq!(row_ids[0].id, "row1");
    //         },
    //         excluded_records_ref
    //     )
    //     .unwrap();

    //     // ----
    //     // Merge the WAL entries
    //     // ----
    //     let mut merged_wal_entries: HashMap<FullRowIdOwned, WalEntry> = HashMap::new();
    //     for (_, entries) in wal_entries.iter() {
    //         for entry in entries {
    //             merged_wal_entries
    //                 .entry(entry.full_row_id().to_owned())
    //                 .and_modify(|existing_entry| existing_entry.merge(entry.clone()).unwrap())
    //                 .or_insert(entry.clone());
    //         }
    //     }

    //     // There should be 3 merged WAL entries
    //     assert_eq!(merged_wal_entries.len(), 3);

    //     // ----
    //     // Now, create a separate tantivy index with just the merged WAL entries
    //     // ----
    //     let in_memory_segment_id = "in_memory_segment0";
    //     let in_mem_index_prefix = Path::new("in_memory_index");

    //     let start = std::time::Instant::now();
    //     let mmap_directory = Arc::new(RamDirectory::create());
    //     let tantivy_index_wrapper = TantivyIndexWrapper::new(
    //         mmap_directory.clone(),
    //         make_full_schema(&fixture.schema).unwrap(),
    //         in_mem_index_prefix,
    //         &TantivyIndexScope::Segment(in_memory_segment_id),
    //     )
    //     .await
    //     .unwrap();
    //     eprintln!("TantivyIndexWrapper took {:?}", start.elapsed());

    //     let full_row_id_field = tantivy_index_wrapper
    //         .tantivy_schema()
    //         .get_field("_full_row_id")
    //         .unwrap();

    //     {
    //         let tantivy_index_wrapper = &tantivy_index_wrapper;
    //         let writer_opts = &BASIC_COMPACT_SEGMENT_WAL_OPTIONS.writer_opts;
    //         await_spawn_blocking!(
    //             move |tantivy_index_wrapper: &TantivyIndexWrapper,
    //                   writer_opts: &TantivyIndexWriterOpts| {
    //                 let start = std::time::Instant::now();
    //                 merge_wal_entries_into_segment(
    //                     tantivy_index_wrapper,
    //                     merged_wal_entries,
    //                     full_row_id_field,
    //                     writer_opts,
    //                 )
    //                 .unwrap();
    //                 eprintln!("merge_wal_entries_into_segment done {:?}", start.elapsed());
    //                 eprintln!(
    //                     "New segments: {:?}",
    //                     tantivy_index_wrapper.index.searchable_segment_ids()
    //                 );
    //             },
    //             tantivy_index_wrapper,
    //             writer_opts
    //         )
    //         .unwrap();
    //     }

    //     eprintln!("merge_wal_entries_into_segment took {:?}", start.elapsed());

    //     // ----
    //     // The new index should have 3 rows
    //     // ----
    //     let tantivy_index_wrapper_ref = &tantivy_index_wrapper;
    //     await_spawn_blocking!(
    //         move |tantivy_index_wrapper: &TantivyIndexWrapper| {
    //             let reader = tantivy_index_wrapper.make_reader_blocking().unwrap();
    //             let searcher = reader.searcher();
    //             let collector = tantivy::collector::DocSetCollector {};

    //             let addresses = searcher.search(&AllQuery {}, &collector).unwrap();

    //             // We should have all 3 rows
    //             assert_eq!(addresses.len(), 3);
    //             eprintln!("addresses: {:?}", addresses);

    //             let mut row_ids: HashSet<String> = HashSet::new();
    //             for address in addresses {
    //                 let doc: tantivy::schema::document::TantivyDocument =
    //                     searcher.doc(address).unwrap();
    //                 let index_doc = IndexDocument::from_tantivy_document(
    //                     &doc,
    //                     &tantivy_index_wrapper.writable_schema.invert_fields,
    //                 )
    //                 .unwrap();
    //                 row_ids.insert(index_doc.wal_entry.full_row_id().to_owned().id);
    //             }
    //             assert_eq!(row_ids.len(), 3);
    //             assert!(row_ids.contains("row0"));
    //             assert!(row_ids.contains("row2"));
    //             assert!(row_ids.contains("row3"));
    //         },
    //         tantivy_index_wrapper_ref
    //     )
    //     .unwrap();

    //     // Create a dynamic directory which includes the new index
    //     let segment_paths = vec![
    //         TantivyIndexScope::Segment(in_memory_segment_id).path(in_mem_index_prefix),
    //         TantivyIndexScope::Segment(segment_id).path(&fixture.config.index.prefix),
    //     ];

    //     let dynamic_directory = Box::new(
    //         DynamicReadonlyDirectory::from_directory_and_paths(
    //             tantivy_index_wrapper.tantivy_schema().clone(),
    //             Arc::new(FallbackDirectory::new(vec![
    //                 mmap_directory.clone(),
    //                 fixture.config.index.directory.clone(),
    //             ])),
    //             segment_paths.clone(),
    //         )
    //         .await
    //         .unwrap(),
    //     );
    //     let dynamic_directory_ref = dynamic_directory.as_ref();

    //     let row_values = await_spawn_blocking!(
    //         move |dynamic_directory: &DynamicReadonlyDirectory,
    //               excluded_records: &HashMap<SegmentId, Vec<DocId>>| {
    //             let tantivy_index = tantivy::Index::open(dynamic_directory.box_clone()).unwrap();
    //             let reader = tantivy_index.reader().unwrap();
    //             let searcher = reader.searcher();
    //             let collector = tantivy::collector::DocSetCollector {};

    //             let excluded_doc_query =
    //                 ExcludedDocQuery::new(Box::new(AllQuery {}), excluded_records.clone());

    //             let addresses = searcher.search(&excluded_doc_query, &collector).unwrap();

    //             // We should have all 4 rows
    //             assert_eq!(addresses.len(), 4);
    //             eprintln!("addresses: {:?}", addresses);

    //             let mut row_values: HashMap<String, TransactionId> = HashMap::new();
    //             for address in addresses {
    //                 let doc: tantivy::schema::document::TantivyDocument =
    //                     searcher.doc(address).unwrap();
    //                 let index_doc = IndexDocument::from_tantivy_document(
    //                     &doc,
    //                     &tantivy_index_wrapper.writable_schema.invert_fields,
    //                 )
    //                 .unwrap();
    //                 eprintln!(
    //                     "index_doc: {:?}. Full: {:?}",
    //                     index_doc.wal_entry.full_row_id().to_owned().id,
    //                     index_doc.wal_entry
    //                 );
    //                 row_values.insert(
    //                     index_doc.wal_entry.full_row_id().to_owned().id,
    //                     index_doc.wal_entry._xact_id,
    //                 );
    //             }
    //             row_values
    //         },
    //         dynamic_directory_ref,
    //         excluded_records_ref
    //     )
    //     .unwrap();

    //     eprintln!("row_values: {:?}", row_values);

    //     assert_eq!(row_values.len(), 4);
    //     assert_eq!(row_values.get("row0").unwrap(), &TransactionId(3));
    //     assert_eq!(row_values.get("row1").unwrap(), &TransactionId(1));
    //     assert_eq!(row_values.get("row2").unwrap(), &TransactionId(2));
    //     assert_eq!(row_values.get("row3").unwrap(), &TransactionId(3));
    // }

    #[tokio::test]
    async fn test_index_wal_reader_basic() {
        env_logger::builder()
            .is_test(true)
            .filter_level(log::LevelFilter::Debug)
            .try_init()
            .unwrap();

        let tmp_dir_config = TmpDirConfigWithStore::new();
        let config = &tmp_dir_config.config;
        let initial_wal_entries = vec![
            WalEntry {
                _xact_id: TransactionId(1),
                id: "row0".to_string(),
                data: json!({"input":"foo"}).as_object().unwrap().clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(1),
                id: "row1".to_string(),
                data: json!({"input":"bar"}).as_object().unwrap().clone(),
                ..Default::default()
            },
        ];

        wal_insert_unmerged(
            config.wal.as_ref(),
            &*config.global_store,
            initial_wal_entries.clone(),
        )
        .await
        .unwrap();

        let segment_ids = config
            .global_store
            .list_segment_ids_global(None)
            .await
            .unwrap();
        eprintln!("segment_ids after insert: {:?}", segment_ids);
        assert_eq!(segment_ids.len(), 0);

        let process_wal_opts = ProcessObjectWalOptions {
            max_rows_per_segment: 10000,
            ..Default::default()
        };

        process_object_wal(
            ProcessObjectWalInput {
                object_id: FullObjectId::default(),
                config,
            },
            Default::default(),
            process_wal_opts.clone(),
        )
        .await
        .unwrap();

        let segment_ids = config
            .global_store
            .list_segment_ids_global(None)
            .await
            .unwrap();
        eprintln!("segment_ids after process_object_wal: {:?}", segment_ids);
        assert!(!segment_ids.is_empty());

        let compact_segment_wal_opts = CompactSegmentWalOptions {
            writer_opts: TantivyIndexWriterOpts::default(),
            ..Default::default()
        };

        join_all(segment_ids.iter().map(|segment_id| {
            run_compact_segment_wal(
                *segment_id,
                config,
                compact_segment_wal_opts.clone(),
                Default::default(),
            )
        }))
        .await;

        let segment_ids = config
            .global_store
            .list_segment_ids_global(None)
            .await
            .unwrap();
        eprintln!("segment_ids: {:?}", segment_ids);
        assert_eq!(segment_ids.len(), 1);
        let segment_id = segment_ids[0];

        // Now, insert a couple WAL entries
        let new_wal_entries = vec![
            WalEntry {
                _xact_id: TransactionId(2),
                id: "row0".to_string(),
                data: json!({"input":"foo_updated"}).as_object().unwrap().clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(2),
                id: "row2".to_string(),
                data: json!({"input":"baz"}).as_object().unwrap().clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(3),
                id: "row0".to_string(),
                data: json!({"input":"foo_updated_again"})
                    .as_object()
                    .unwrap()
                    .clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(3),
                id: "row3".to_string(),
                data: json!({"input":"qux"}).as_object().unwrap().clone(),
                ..Default::default()
            },
        ];

        wal_insert_unmerged(config.wal.as_ref(), &*config.global_store, new_wal_entries)
            .await
            .unwrap();

        eprintln!("Checking reader values before processing WAL");

        let check_reader_values = || async {
            let row_values = get_values(config).await;
            eprintln!("row_values: {:?}", row_values);
            assert_eq!(row_values.len(), 4);
            assert_eq!(row_values.get("row0").unwrap(), &TransactionId(3));
            assert_eq!(row_values.get("row1").unwrap(), &TransactionId(1));
            assert_eq!(row_values.get("row2").unwrap(), &TransactionId(2));
            assert_eq!(row_values.get("row3").unwrap(), &TransactionId(3));
        };

        check_reader_values().await;

        let process_output = process_object_wal(
            ProcessObjectWalInput {
                object_id: FullObjectId::default(),
                config,
            },
            Default::default(),
            process_wal_opts.clone(),
        )
        .await
        .unwrap();
        assert_eq!(process_output.modified_segment_ids.len(), 1);
        assert!(process_output.modified_segment_ids.contains(&segment_id));

        eprintln!("Checking reader values after processing WAL");
        check_reader_values().await;

        // Disable real-time and make sure we are missing un-compacted rows.

        {
            let index_wal_reader = make_index_wal_reader(
                config,
                Default::default(),
                IndexWalReaderOpts {
                    skip_realtime_wal_entries: Some(true),
                    ..Default::default()
                },
            )
            .await;

            let index_wal_reader_ref = &index_wal_reader;
            let tantivy_schema = make_tantivy_schema(index_wal_reader.schema()).unwrap();

            let row_values = await_spawn_blocking!(
                move |index_wal_reader: &IndexWalReader| {
                    let tantivy_index =
                        tantivy::Index::open(index_wal_reader.only_directory().box_clone())
                            .unwrap();

                    let reader = tantivy_index.reader().unwrap();
                    let searcher = reader.searcher();
                    let collector = tantivy::collector::DocSetCollector {};

                    let excluded_doc_query =
                        index_wal_reader.wrap_exclude_query(Box::new(AllQuery {}));

                    let addresses = searcher.search(&excluded_doc_query, &collector).unwrap();

                    // We should have all 4 rows
                    assert_eq!(addresses.len(), 2);
                    eprintln!("addresses: {:?}", addresses);

                    let mut row_values: HashMap<String, TransactionId> = HashMap::new();
                    for address in addresses {
                        let doc: tantivy::schema::document::TantivyDocument =
                            searcher.doc(address).unwrap();
                        let index_doc = IndexDocument::from_tantivy_document(
                            &doc,
                            &tantivy_schema.invert_fields,
                        )
                        .unwrap();
                        row_values.insert(
                            index_doc.wal_entry.full_row_id().to_owned().id,
                            index_doc.wal_entry._xact_id,
                        );
                    }
                    row_values
                },
                index_wal_reader_ref
            )
            .unwrap()
            .unwrap();

            assert_eq!(row_values.len(), 2);
            assert_eq!(row_values.get("row0").unwrap(), &TransactionId(1));
            assert_eq!(row_values.get("row1").unwrap(), &TransactionId(1));
        }
    }

    #[tokio::test]
    async fn test_index_wal_reader_snapshot_consistency() {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        let config = &tmp_dir_config.config;

        let entries0 = vec![
            WalEntry {
                _xact_id: TransactionId(1),
                id: "row0".to_string(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(1),
                id: "row1".to_string(),
                ..Default::default()
            },
        ];

        let add_wal_entries = |entries: Vec<WalEntry>| async {
            let ids = add_wal_entries_full(
                config,
                entries,
                ProcessObjectWalOptions::default(),
                ProcessObjectWalTestingSyncPoints::default(),
            )
            .await;
            assert_eq!(ids.len(), 1);
            ids.into_iter().next().unwrap()
        };

        let segment_id = add_wal_entries(entries0.clone()).await;

        let run_compaction =
            || run_compact_segment_wal(segment_id, config, Default::default(), Default::default());
        run_compaction().await;

        assert_hashmap_eq(
            &get_values(config).await,
            &entries0
                .iter()
                .map(|e| (e.id.clone(), e._xact_id))
                .collect(),
        );

        // Create another wal reader which reads all of the available WAL entries but pauses before
        // reading the tantivy indices.
        let sync_point = two_way_sync_point();
        let index_wal_reader_fut = {
            let config = config.clone();
            tokio::spawn(async move {
                make_index_wal_reader(
                    &config,
                    IndexWalReaderOptionalInput {
                        testing_sync_points: IndexWalReaderTestingSyncPoints {
                            after_get_all_realtime_wal_entries: Some(sync_point.0),
                            ..Default::default()
                        },
                        ..Default::default()
                    },
                    Default::default(),
                )
                .await
            })
        };

        // Wait for the reader to read all of the segment WAL entries.
        let sync_point = sync_point.1.wait().await;

        // Now before we have read the tantivy indices, add more entries and compact them.
        let entries1 = vec![WalEntry {
            _xact_id: TransactionId(2),
            id: "row0".to_string(),
            ..Default::default()
        }];
        assert_eq!(add_wal_entries(entries1.clone()).await, segment_id);
        run_compaction().await;

        // Finish creating the reader.
        sync_point.send();
        let index_wal_reader = index_wal_reader_fut.await.unwrap();
        // Since we read the segment WAL entries before adding new stuff, the entries we read
        // should not include anything from TransactionId(2).
        assert_hashmap_eq(
            &get_reader_values(&index_wal_reader).await,
            &entries0
                .iter()
                .map(|e| (e.id.clone(), e._xact_id))
                .collect(),
        );

        // But if we read from scratch, we should get the new entries.
        assert_hashmap_eq(
            &get_values(config).await,
            &[
                ("row0".to_string(), TransactionId(2)),
                ("row1".to_string(), TransactionId(1)),
            ]
            .into_iter()
            .collect(),
        );
    }

    #[tokio::test]
    async fn test_index_wal_reader_ignore_partially_processed_segment() {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        let config = &tmp_dir_config.config;

        let entries = vec![WalEntry {
            _xact_id: TransactionId(1),
            id: "row0".to_string(),
            ..Default::default()
        }];

        // Add the wal entries in the background, all the way up to adding to segments, but not
        // updating our object's last_processed_xact_id.
        let (sync_point_send, sync_point_wait) = two_way_sync_point();
        let add_wal_entries_fut = {
            let config = config.clone();
            let entries = entries.clone();
            tokio::spawn(async move {
                add_wal_entries_full(
                    &config,
                    entries,
                    ProcessObjectWalOptions::default(),
                    ProcessObjectWalTestingSyncPoints {
                        before_update_object_metadata: Some(sync_point_send),
                        ..Default::default()
                    },
                )
                .await;
            })
        };

        // Wait till the segments have finished writing but keep process_wal held up before it
        // updates the global store metadata.
        let sync_point_wait = sync_point_wait.wait().await;

        // Running a read should succeed.
        assert_hashmap_eq(
            &get_values(config).await,
            &entries.iter().map(|e| (e.id.clone(), e._xact_id)).collect(),
        );

        // After we finish processing, the read should succeed again.
        sync_point_wait.send();
        assert_hashmap_eq(
            &get_values(config).await,
            &entries.iter().map(|e| (e.id.clone(), e._xact_id)).collect(),
        );

        add_wal_entries_fut.await.unwrap();
    }

    #[tokio::test]
    async fn test_index_wal_reader_uncompacted_segment() {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        let config = &tmp_dir_config.config;

        let entries0 = vec![WalEntry {
            _xact_id: TransactionId(1),
            id: "row0".to_string(),
            root_span_id: "0".to_string(),
            ..Default::default()
        }];

        let add_wal_entries = |entries: Vec<WalEntry>| async {
            let ids = add_wal_entries_full(
                config,
                entries,
                ProcessObjectWalOptions {
                    max_rows_per_segment: 1,
                    ..Default::default()
                },
                Default::default(),
            )
            .await;
            assert_eq!(ids.len(), 1);
            ids.into_iter().next().unwrap()
        };

        let segment_id0 = add_wal_entries(entries0.clone()).await;

        // Before compacting anything, we should still be able to read.
        assert_hashmap_eq(
            &get_values(config).await,
            &[("row0".to_string(), TransactionId(1))]
                .into_iter()
                .collect(),
        );

        // After compaction should be the same.
        run_compact_segment_wal(segment_id0, config, Default::default(), Default::default()).await;
        assert_hashmap_eq(
            &get_values(config).await,
            &[("row0".to_string(), TransactionId(1))]
                .into_iter()
                .collect(),
        );

        // Now add more entries to a different segment without compacting.
        let entries1 = vec![WalEntry {
            _xact_id: TransactionId(2),
            id: "row1".to_string(),
            root_span_id: "1".to_string(),
            ..Default::default()
        }];
        let segment_id1 = add_wal_entries(entries1.clone()).await;
        assert_ne!(segment_id0, segment_id1);

        // We should recover all entries when reading from the WAL.
        assert_hashmap_eq(
            &get_values(config).await,
            &[
                ("row0".to_string(), TransactionId(1)),
                ("row1".to_string(), TransactionId(2)),
            ]
            .into_iter()
            .collect(),
        );
    }

    #[tokio::test]
    async fn test_index_wal_reader_multi_object_overlapping_xact_ids() {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        let config = &tmp_dir_config.config;

        let object_id0 = FullObjectIdOwned {
            object_id: ObjectIdOwned::new("object0".to_string()).unwrap(),
            ..Default::default()
        };
        let object_id1 = FullObjectIdOwned {
            object_id: ObjectIdOwned::new("object1".to_string()).unwrap(),
            ..Default::default()
        };

        let entries = vec![
            WalEntry {
                _xact_id: TransactionId(1),
                id: "row0".to_string(),
                _object_type: object_id0.object_type,
                _object_id: object_id0.object_id.clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(2),
                id: "row1".to_string(),
                _object_type: object_id0.object_type,
                _object_id: object_id0.object_id.clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(2),
                id: "row2".to_string(),
                _object_type: object_id1.object_type,
                _object_id: object_id1.object_id.clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(3),
                id: "row3".to_string(),
                _object_type: object_id1.object_type,
                _object_id: object_id1.object_id.clone(),
                ..Default::default()
            },
        ];

        // Insert all the WAL entries. For the object 0 only process up to xact_id 1, and for
        // object 1 process up to xact_id 2. This way, the processed xact id range for object 1
        // will overlap with the unprocessed xact id range for object 0.
        wal_insert_unmerged(config.wal.as_ref(), &*config.global_store, entries.clone())
            .await
            .unwrap();
        process_object_wal(
            ProcessObjectWalInput {
                object_id: object_id0.as_ref(),
                config,
            },
            ProcessObjectWalOptionalInput {
                end_xact_id: Some(TransactionId(1)),
                ..Default::default()
            },
            ProcessObjectWalOptions::default(),
        )
        .await
        .unwrap();
        process_object_wal(
            ProcessObjectWalInput {
                object_id: object_id1.as_ref(),
                config,
            },
            ProcessObjectWalOptionalInput {
                end_xact_id: Some(TransactionId(2)),
                ..Default::default()
            },
            ProcessObjectWalOptions::default(),
        )
        .await
        .unwrap();

        let index_wal_reader = IndexWalReader::new(
            IndexWalReaderInput {
                config_with_store: config,
                full_schema: make_default_full_schema(),
                object_ids: &[object_id0, object_id1],
                filters: &[],
                sort: &None,
            },
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap();

        assert_hashmap_eq(
            &get_reader_values(&index_wal_reader).await,
            &[
                ("row0".to_string(), TransactionId(1)),
                ("row1".to_string(), TransactionId(2)),
                ("row2".to_string(), TransactionId(2)),
                ("row3".to_string(), TransactionId(3)),
            ]
            .into_iter()
            .collect(),
        );
    }

    #[tokio::test]
    async fn test_index_wal_reader_wal_merge() {
        // Try all combinations of MergeControlFields and make sure the result is what we expect.
        let true_false_arr = [true, false];
        for ((&is_merge, &replace_sticky_system_fields), &object_delete) in true_false_arr
            .iter()
            .cartesian_product(true_false_arr.iter())
            .cartesian_product(true_false_arr.iter())
        {
            let tmp_dir_config = TmpDirConfigWithStore::new();
            let config = &tmp_dir_config.config;
            let add_wal_entries = |entries: Vec<WalEntry>| async {
                add_wal_entries_full(config, entries, Default::default(), Default::default())
                    .await
                    .into_iter()
                    .next()
                    .unwrap()
            };

            // Add and compact a row.
            let entries0 = vec![WalEntry {
                _pagination_key: PaginationKey(10),
                created: DateTime::from_timestamp_millis(100).unwrap(),
                _xact_id: TransactionId(1),
                id: "row0".to_string(),
                data: json!({"input":"foo"}).as_object().unwrap().clone(),
                ..Default::default()
            }];
            let segment_id = add_wal_entries(entries0.clone()).await;
            run_compact_segment_wal(segment_id, config, Default::default(), Default::default())
                .await;
            let entries1 = vec![WalEntry {
                _pagination_key: PaginationKey(20),
                created: DateTime::from_timestamp_millis(200).unwrap(),
                _xact_id: TransactionId(2),
                id: "row0".to_string(),
                root_span_id: "1".to_string(),
                data: json!({"input":"bar"}).as_object().unwrap().clone(),
                _is_merge: Some(is_merge),
                _replace_sticky_system_fields: Some(replace_sticky_system_fields),
                _object_delete: Some(object_delete),
                ..Default::default()
            }];
            assert_eq!(add_wal_entries(entries1.clone()).await, segment_id);

            // We should have one entry whose data is the merge of the two entries.
            let values = get_reader_full_docs(&make_default_reader(config).await).await;
            if object_delete {
                assert_eq!(values.len(), 0);
            } else {
                let id_to_data = values
                    .into_iter()
                    .map(|(id, doc)| (id, sanitize_reader_doc(doc)))
                    .collect::<HashMap<_, _>>();
                // We should recover one entry when reading from the WAL.
                let expected = sanitize_reader_doc({
                    let mut wal_entry = entries0[0].clone();
                    wal_entry.merge(entries1[0].clone()).unwrap();
                    // Clear out any audit entries prior to the last one.
                    wal_entry
                        .audit_data
                        .0
                        .retain(|e| e._xact_id >= wal_entry._xact_id);
                    IndexDocument { wal_entry }
                });
                eprintln!(
                    "is_merge: {}, replace_sticky_system_fields: {}, object_delete: {}",
                    is_merge, replace_sticky_system_fields, object_delete
                );
                assert_hashmap_eq(
                    &id_to_data,
                    &[("row0".to_string(), expected)].into_iter().collect(),
                );
            }
        }
    }

    #[tokio::test]
    async fn test_index_wal_reader_realtime_memory_limit_single_segment() {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        let config = &tmp_dir_config.config;
        static TEST_LIMIT: Lazy<AsyncLimit> = make_lazy_realtime_memory_limit();

        // Create initial entry and compact it
        let entries0 = vec![WalEntry {
            _xact_id: TransactionId(1),
            id: "row0".to_string(),
            data: json!({"input":"foo"}).as_object().unwrap().clone(),
            ..Default::default()
        }];

        let segment_id = add_wal_entries_full(
            config,
            entries0.clone(),
            ProcessObjectWalOptions::default(),
            Default::default(),
        )
        .await
        .into_iter()
        .next()
        .unwrap();

        run_compact_segment_wal(segment_id, config, Default::default(), Default::default()).await;

        // Create a large entry at transaction 2
        let entries1 = vec![WalEntry {
            _xact_id: TransactionId(2),
            id: "row0".to_string(),
            data: generate_large_data(),
            ..Default::default()
        }];

        // Add another large entry at transaction 3
        let entries2 = vec![WalEntry {
            _xact_id: TransactionId(3),
            id: "row1".to_string(),
            data: generate_large_data(),
            ..Default::default()
        }];

        wal_insert_unmerged(config.wal.as_ref(), &*config.global_store, entries1.clone())
            .await
            .unwrap();
        wal_insert_unmerged(config.wal.as_ref(), &*config.global_store, entries2.clone())
            .await
            .unwrap();
        // Calculate number of readers we can create before hitting global limit.
        let num_readers = (TEST_LIMIT.capacity() as usize) / large_data_size_bytes();

        // Create readers up to the limit
        let mut readers = Vec::new();
        for _ in 0..num_readers {
            let reader = make_index_wal_reader(
                config,
                IndexWalReaderOptionalInput {
                    testing_global_realtime_memory_bytes_limit: Some(&TEST_LIMIT),
                    ..Default::default()
                },
                IndexWalReaderOpts {
                    skip_realtime_wal_memory_limit: Some(false),
                    ..Default::default()
                },
            )
            .await;

            // Check realtime state
            match reader.realtime_state() {
                IndexWalReaderRealtimeState::ExhaustedMemoryBudget {
                    minimum_xact_id,
                    actual_xact_id,
                    read_bytes,
                    ..
                } => {
                    assert_eq!(*minimum_xact_id, Some(TransactionId(1)));
                    assert_eq!(*actual_xact_id, Some(TransactionId(2)));
                    assert_eq!(*read_bytes / large_data_size_bytes(), 1);
                }
                _ => panic!(
                    "Expected ExhaustedMemoryBudget state. Instead got {:?}. On reader {}",
                    reader.realtime_state(),
                    readers.len(),
                ),
            }

            // Verify we only see entries up to transaction 2
            let row_values = get_reader_values(&reader).await;
            assert_eq!(row_values.len(), 1);
            assert_eq!(row_values.get("row0").unwrap(), &TransactionId(2));

            readers.push(reader);
        }

        // Create one more reader - this should hit the global limit
        let final_reader = make_index_wal_reader(
            config,
            IndexWalReaderOptionalInput {
                testing_global_realtime_memory_bytes_limit: Some(&TEST_LIMIT),
                ..Default::default()
            },
            IndexWalReaderOpts {
                skip_realtime_wal_memory_limit: Some(false),
                ..Default::default()
            },
        )
        .await;

        // Check realtime state.
        match final_reader.realtime_state() {
            IndexWalReaderRealtimeState::ExhaustedMemoryBudget {
                minimum_xact_id,
                actual_xact_id,
                read_bytes,
                ..
            } => {
                assert_eq!(*minimum_xact_id, Some(TransactionId(1)));
                assert_eq!(*actual_xact_id, None);
                assert_eq!(*read_bytes, 0);
            }
            _ => panic!(
                "Expected ExhaustedMemoryBudget state. Instead got {:?}",
                final_reader.realtime_state()
            ),
        }

        // Verify we only see the compacted entries
        let row_values = get_reader_values(&final_reader).await;
        assert_eq!(row_values.len(), 1);
        assert_eq!(row_values.get("row0").unwrap(), &TransactionId(1));
    }

    #[tokio::test]
    async fn test_index_wal_reader_realtime_memory_limit_multi_segment_exhausted() {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        let config = &tmp_dir_config.config;
        static TEST_LIMIT: Lazy<AsyncLimit> = make_lazy_realtime_memory_limit();

        // Create first segment compacted up to transaction 1
        let entries0 = vec![
            WalEntry {
                _xact_id: TransactionId(1),
                id: "row0".to_string(),
                data: json!({"input":"foo"}).as_object().unwrap().clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(1),
                id: "row1".to_string(),
                data: json!({"input":"bar"}).as_object().unwrap().clone(),
                ..Default::default()
            },
        ];

        let segment_id0 = add_wal_entries_full(
            config,
            entries0.clone(),
            ProcessObjectWalOptions {
                max_rows_per_segment: 1,
                ..Default::default()
            },
            Default::default(),
        )
        .await
        .into_iter()
        .next()
        .unwrap();

        run_compact_segment_wal(segment_id0, config, Default::default(), Default::default()).await;

        // Add two large entries to the first segment and create a second segment compacted up to transaction 3
        let entries1 = vec![
            WalEntry {
                _xact_id: TransactionId(2),
                id: "row0".to_string(),
                data: generate_large_data(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(3),
                id: "row1".to_string(),
                data: generate_large_data(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(2),
                id: "row2".to_string(),
                root_span_id: "1".to_string(),
                data: json!({"input":"baz"}).as_object().unwrap().clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(3),
                id: "row3".to_string(),
                root_span_id: "1".to_string(),
                data: json!({"input":"qux"}).as_object().unwrap().clone(),
                ..Default::default()
            },
        ];

        let all_segment_ids = add_wal_entries_full(
            config,
            entries1.clone(),
            ProcessObjectWalOptions {
                max_rows_per_segment: 1,
                ..Default::default()
            },
            Default::default(),
        )
        .await;
        assert!(all_segment_ids.len() == 2);
        assert!(all_segment_ids.contains(&segment_id0));
        let segment_id1 = all_segment_ids
            .into_iter()
            .find(|id| *id != segment_id0)
            .unwrap();

        run_compact_segment_wal(segment_id1, config, Default::default(), Default::default()).await;

        // Create reader with memory limit enabled
        let index_wal_reader = make_index_wal_reader(
            config,
            IndexWalReaderOptionalInput {
                testing_global_realtime_memory_bytes_limit: Some(&TEST_LIMIT),
                ..Default::default()
            },
            IndexWalReaderOpts {
                skip_realtime_wal_memory_limit: Some(false),
                ..Default::default()
            },
        )
        .await;

        // Check realtime state
        match index_wal_reader.realtime_state() {
            IndexWalReaderRealtimeState::ExhaustedMemoryBudget {
                minimum_xact_id,
                actual_xact_id,
                read_bytes,
                ..
            } => {
                assert_eq!(*minimum_xact_id, Some(TransactionId(3)));
                assert_eq!(*actual_xact_id, Some(TransactionId(2)));
                assert_eq!(*read_bytes / large_data_size_bytes(), 1);
            }
            _ => panic!(
                "Expected ExhaustedMemoryBudget state. Instead got {:?}",
                index_wal_reader.realtime_state()
            ),
        }

        // Verify we only see entries from compacted segments plus the realtime
        // entries we managed to read.
        let row_values = get_reader_values(&index_wal_reader).await;
        assert_eq!(row_values.len(), 4);
        assert_eq!(row_values.get("row0").unwrap(), &TransactionId(2));
        assert_eq!(row_values.get("row1").unwrap(), &TransactionId(1));
        assert_eq!(row_values.get("row2").unwrap(), &TransactionId(2));
        assert_eq!(row_values.get("row3").unwrap(), &TransactionId(3));
    }

    #[tokio::test]
    async fn test_index_wal_reader_realtime_memory_limit_multi_segment_partial() {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        let config = &tmp_dir_config.config;
        static TEST_LIMIT: Lazy<AsyncLimit> = make_lazy_realtime_memory_limit();

        // Create first segment with entries up to transaction 3
        let entries0 = vec![
            WalEntry {
                _xact_id: TransactionId(1),
                id: "row0".to_string(),
                data: json!({"input":"foo"}).as_object().unwrap().clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(2),
                id: "row1".to_string(),
                data: json!({"input":"bar"}).as_object().unwrap().clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(3),
                id: "row2".to_string(),
                data: json!({"input":"baz"}).as_object().unwrap().clone(),
                ..Default::default()
            },
        ];

        let segment_id0 = add_wal_entries_full(
            config,
            entries0.clone(),
            ProcessObjectWalOptions {
                max_rows_per_segment: 1,
                ..Default::default()
            },
            Default::default(),
        )
        .await
        .into_iter()
        .next()
        .unwrap();

        run_compact_segment_wal(segment_id0, config, Default::default(), Default::default()).await;

        // Create second segment with entries up to transaction 6
        let entries1 = vec![
            // This one hits segment0, but will not be compacted.
            WalEntry {
                _xact_id: TransactionId(4),
                id: "row0".to_string(),
                data: json!({"input":"boop"}).as_object().unwrap().clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(4),
                id: "row3".to_string(),
                root_span_id: "1".to_string(),
                data: json!({"input":"qux"}).as_object().unwrap().clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(5),
                id: "row4".to_string(),
                root_span_id: "1".to_string(),
                data: json!({"input":"quux"}).as_object().unwrap().clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(6),
                id: "row5".to_string(),
                root_span_id: "1".to_string(),
                data: json!({"input":"corge"}).as_object().unwrap().clone(),
                ..Default::default()
            },
        ];

        let all_segment_ids = add_wal_entries_full(
            config,
            entries1.clone(),
            ProcessObjectWalOptions {
                max_rows_per_segment: 1,
                ..Default::default()
            },
            Default::default(),
        )
        .await;
        assert!(all_segment_ids.len() == 2);
        assert!(all_segment_ids.contains(&segment_id0));
        let segment_id1 = all_segment_ids
            .into_iter()
            .find(|id| *id != segment_id0)
            .unwrap();

        assert_ne!(segment_id0, segment_id1);

        run_compact_segment_wal(segment_id1, config, Default::default(), Default::default()).await;

        {
            // Create reader with memory limit enabled
            let index_wal_reader = make_index_wal_reader(
                config,
                IndexWalReaderOptionalInput {
                    testing_global_realtime_memory_bytes_limit: Some(&TEST_LIMIT),
                    ..Default::default()
                },
                IndexWalReaderOpts {
                    skip_realtime_wal_memory_limit: Some(false),
                    ..Default::default()
                },
            )
            .await;

            // Check realtime state
            match index_wal_reader.realtime_state() {
                IndexWalReaderRealtimeState::On {
                    minimum_xact_id,
                    actual_xact_id,
                    read_bytes,
                    ..
                } => {
                    assert_eq!(*minimum_xact_id, Some(TransactionId(6)));
                    assert_eq!(*actual_xact_id, Some(TransactionId(6)));
                    assert_eq!(*read_bytes / large_data_size_bytes(), 0);
                }
                _ => panic!("Expected On state"),
            }
        }

        // Add a large entry at transaction 7
        let entries2 = vec![WalEntry {
            _xact_id: TransactionId(7),
            id: "row0".to_string(),
            data: generate_large_data(),
            ..Default::default()
        }];

        // Add another large entry at transaction 8
        let entries3 = vec![WalEntry {
            _xact_id: TransactionId(8),
            id: "row6".to_string(),
            data: generate_large_data(),
            ..Default::default()
        }];

        wal_insert_unmerged(config.wal.as_ref(), &*config.global_store, entries2.clone())
            .await
            .unwrap();
        wal_insert_unmerged(config.wal.as_ref(), &*config.global_store, entries3.clone())
            .await
            .unwrap();

        // Create reader with memory limit enabled
        let index_wal_reader = make_index_wal_reader(
            config,
            IndexWalReaderOptionalInput {
                testing_global_realtime_memory_bytes_limit: Some(&TEST_LIMIT),
                ..Default::default()
            },
            IndexWalReaderOpts {
                skip_realtime_wal_memory_limit: Some(false),
                ..Default::default()
            },
        )
        .await;

        // Check realtime state
        match index_wal_reader.realtime_state() {
            IndexWalReaderRealtimeState::ExhaustedMemoryBudget {
                minimum_xact_id,
                actual_xact_id,
                read_bytes,
                ..
            } => {
                assert_eq!(*minimum_xact_id, Some(TransactionId(6)));
                assert_eq!(*actual_xact_id, Some(TransactionId(7)));
                assert_eq!(*read_bytes / large_data_size_bytes(), 1);
            }
            _ => panic!("Expected ExhaustedMemoryBudget state"),
        }

        // Verify we see entries up to the actual_xact_id.
        let row_values = get_reader_values(&index_wal_reader).await;
        assert_eq!(row_values.len(), 6);
        assert_eq!(row_values.get("row0").unwrap(), &TransactionId(7));
        assert_eq!(row_values.get("row1").unwrap(), &TransactionId(2));
        assert_eq!(row_values.get("row2").unwrap(), &TransactionId(3));
        assert_eq!(row_values.get("row3").unwrap(), &TransactionId(4));
        assert_eq!(row_values.get("row4").unwrap(), &TransactionId(5));
        assert_eq!(row_values.get("row5").unwrap(), &TransactionId(6));
    }

    #[tokio::test]
    async fn test_index_wal_reader_realtime_timeout() {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        let config = &tmp_dir_config.config;

        // Create initial entry and compact it
        let entries0 = vec![WalEntry {
            _xact_id: TransactionId(1),
            id: "row0".to_string(),
            data: json!({"input":"foo"}).as_object().unwrap().clone(),
            ..Default::default()
        }];

        let segment_id = add_wal_entries_full(
            config,
            entries0.clone(),
            ProcessObjectWalOptions::default(),
            Default::default(),
        )
        .await
        .into_iter()
        .next()
        .unwrap();

        run_compact_segment_wal(segment_id, config, Default::default(), Default::default()).await;

        // Add multiple WAL entries that will be read in realtime
        let entries1 = vec![
            WalEntry {
                _xact_id: TransactionId(2),
                id: "row0".to_string(),
                data: json!({"input":"bar"}).as_object().unwrap().clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(3),
                id: "row1".to_string(),
                data: json!({"input":"baz"}).as_object().unwrap().clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(4),
                id: "row2".to_string(),
                data: json!({"input":"qux"}).as_object().unwrap().clone(),
                ..Default::default()
            },
        ];

        wal_insert_unmerged(config.wal.as_ref(), &*config.global_store, entries1.clone())
            .await
            .unwrap();

        // Create a sync point to control when the realtime WAL reading starts
        let (sync_point_send, sync_point_wait) = two_way_sync_point();

        // Spawn a background task that creates an index wal reader with a 10ms timeout
        let index_wal_reader_fut = {
            let config = config.clone();
            tokio::spawn(async move {
                make_index_wal_reader(
                    &config,
                    IndexWalReaderOptionalInput {
                        testing_sync_points: IndexWalReaderTestingSyncPoints {
                            before_stream_first_realtime_wal_entry: Some(sync_point_send),
                            ..Default::default()
                        },
                        ..Default::default()
                    },
                    IndexWalReaderOpts {
                        realtime_read_timeout_ms: Some(10),
                        ..Default::default()
                    },
                )
                .await
            })
        };

        // Wait for the reader to reach the sync point (before reading realtime WAL entries)
        let sync_point_wait = sync_point_wait.wait().await;

        // Sleep for 20ms to ensure we exceed the 10ms timeout
        tokio::time::sleep(std::time::Duration::from_millis(20)).await;

        // Continue the sync point to start reading realtime WAL entries. Ignore
        // errors, which mean the sync point was cleaned up.
        sync_point_wait.send_err().unwrap_or_default();

        // Wait for the reader to complete
        let index_wal_reader = index_wal_reader_fut.await.unwrap();

        // Check that we hit the timeout state
        match index_wal_reader.realtime_state() {
            IndexWalReaderRealtimeState::ExhaustedTimeout { timeout_ms } => {
                assert_eq!(*timeout_ms, 10);
            }
            _ => panic!(
                "Expected ExhaustedTimeout state. Instead got {:?}",
                index_wal_reader.realtime_state()
            ),
        }

        // Verify we only see the compacted entries (no realtime entries due to timeout)
        let row_values = get_reader_values(&index_wal_reader).await;
        assert_eq!(row_values.len(), 1);
        assert_eq!(row_values.get("row0").unwrap(), &TransactionId(1));
    }

    #[tokio::test]
    async fn test_root_span_id_filtering() {
        let tmp_dir_config = TmpDirConfigWithStore::new();
        let config = &tmp_dir_config.config;

        // Create WAL entries with different root_span_ids
        let initial_wal_entries = vec![
            WalEntry {
                _xact_id: TransactionId(1),
                id: "row0".to_string(),
                root_span_id: "root_span_1".to_string(),
                data: json!({"input":"data_for_root_span_1"})
                    .as_object()
                    .unwrap()
                    .clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(1),
                id: "row1".to_string(),
                root_span_id: "root_span_2".to_string(),
                data: json!({"input":"data_for_root_span_2"})
                    .as_object()
                    .unwrap()
                    .clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(1),
                id: "row2".to_string(),
                root_span_id: "root_span_3".to_string(),
                data: json!({"input":"data_for_root_span_3"})
                    .as_object()
                    .unwrap()
                    .clone(),
                ..Default::default()
            },
            WalEntry {
                _xact_id: TransactionId(1),
                id: "row3".to_string(),
                root_span_id: "root_span_1".to_string(),
                data: json!({"input":"another_data_for_root_span_1"})
                    .as_object()
                    .unwrap()
                    .clone(),
                ..Default::default()
            },
        ];
        wal_insert_unmerged(
            config.wal.as_ref(),
            &*config.global_store,
            initial_wal_entries,
        )
        .await
        .unwrap();
        process_object_wal(
            ProcessObjectWalInput {
                object_id: FullObjectId::default(),
                config,
            },
            ProcessObjectWalOptionalInput::default(),
            ProcessObjectWalOptions::default(),
        )
        .await
        .unwrap();

        // Test 1: No filter - should see all entries in merged_wal_docs
        let reader_no_filter =
            make_index_wal_reader(config, Default::default(), Default::default()).await;
        let in_memory_docs_no_filter = reader_no_filter.in_memory_docs();
        assert_eq!(in_memory_docs_no_filter.len(), 4);

        // Verify all entries are present
        let ids_no_filter: HashSet<String> = in_memory_docs_no_filter
            .iter()
            .map(|doc| doc.get("id").unwrap().as_str().unwrap().to_string())
            .collect();
        assert_eq!(
            ids_no_filter,
            HashSet::from([
                "row0".to_string(),
                "row1".to_string(),
                "row2".to_string(),
                "row3".to_string()
            ])
        );

        // Test 2: Filter by root_span_1 - merged_wal_docs should only contain row0 and row3
        let filter_root_span_1 = SegmentEliminationFilterSpec::IdFilter {
            ids: HashSet::new(),
            root_span_ids: HashSet::from(["root_span_1".to_string()]),
        };
        let reader_filtered_1 = IndexWalReader::new(
            IndexWalReaderInput {
                config_with_store: config,
                full_schema: make_default_full_schema(),
                object_ids: &[FullObjectId::default().to_owned()],
                filters: &[filter_root_span_1],
                sort: &None,
            },
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap();

        // Check that merged_wal_docs is pre-filtered
        let in_memory_docs_filtered_1 = reader_filtered_1.in_memory_docs();
        assert_eq!(in_memory_docs_filtered_1.len(), 2);

        let ids_filtered_1: HashSet<String> = in_memory_docs_filtered_1
            .iter()
            .map(|doc| doc.get("id").unwrap().as_str().unwrap().to_string())
            .collect();
        assert_eq!(
            ids_filtered_1,
            HashSet::from(["row0".to_string(), "row3".to_string()])
        );

        // Test 3: Filter by multiple root_span_ids - merged_wal_docs should only contain row0, row1, and row3
        let filter_multiple = SegmentEliminationFilterSpec::IdFilter {
            ids: HashSet::new(),
            root_span_ids: HashSet::from(["root_span_1".to_string(), "root_span_2".to_string()]),
        };
        let reader_filtered_multiple = IndexWalReader::new(
            IndexWalReaderInput {
                config_with_store: config,
                full_schema: make_default_full_schema(),
                object_ids: &[FullObjectId::default().to_owned()],
                filters: &[filter_multiple],
                sort: &None,
            },
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap();

        // Check that merged_wal_docs is pre-filtered
        let in_memory_docs_filtered_multiple = reader_filtered_multiple.in_memory_docs();
        assert_eq!(in_memory_docs_filtered_multiple.len(), 3);

        let ids_filtered_multiple: HashSet<String> = in_memory_docs_filtered_multiple
            .iter()
            .map(|doc| doc.get("id").unwrap().as_str().unwrap().to_string())
            .collect();
        assert_eq!(
            ids_filtered_multiple,
            HashSet::from(["row0".to_string(), "row1".to_string(), "row3".to_string()])
        );

        // Test 4: Filter by non-existent root_span_id - should see no entries
        let filter_non_existent = SegmentEliminationFilterSpec::IdFilter {
            ids: HashSet::new(),
            root_span_ids: HashSet::from(["non_existent_root_span".to_string()]),
        };
        let reader_filtered_none = IndexWalReader::new(
            IndexWalReaderInput {
                config_with_store: config,
                full_schema: make_default_full_schema(),
                object_ids: &[FullObjectId::default().to_owned()],
                filters: &[filter_non_existent],
                sort: &None,
            },
            Default::default(),
            Default::default(),
        )
        .await
        .unwrap();

        // Check that merged_wal_docs is empty when filter matches nothing
        let in_memory_docs_filtered_none = reader_filtered_none.in_memory_docs();
        assert_eq!(in_memory_docs_filtered_none.len(), 0);
    }
}
