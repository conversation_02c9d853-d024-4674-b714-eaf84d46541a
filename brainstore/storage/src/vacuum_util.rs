use serde::{Deserialize, Serialize};

#[derive(<PERSON><PERSON>, <PERSON><PERSON>, <PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON>ialEq, Serialize, Deserialize)]
pub enum VacuumType {
    #[serde(rename = "vacuum_index")]
    VacuumIndex,
}

impl VacuumType {
    pub fn lock_name(&self) -> String {
        match self {
            VacuumType::VacuumIndex => "vacuum_index".to_string(),
        }
    }
}

impl std::fmt::Display for VacuumType {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        let s = match self {
            VacuumType::VacuumIndex => "vacuum_index",
        };
        write!(f, "{}", s)
    }
}
