use clap::Parser;
use object_store::ObjectMeta;
use serde::{Deserialize, Serialize};
use std::{path::PathBuf, sync::Arc};
use tantivy::SegmentId;
use util::{
    anyhow::{anyhow, Result},
    async_stream::try_stream,
    chrono::{DateTime, Duration, Utc},
    futures::stream::{BoxStream, StreamExt, TryStreamExt},
    schema::Schema,
    system_types::FullObjectId,
    uuid::Uuid,
};

use crate::{
    config_with_store::StoreInfo,
    global_locks_manager::GlobalLocksManager,
    global_store::{
        GlobalStore, ListSegmentIdsGlobalOptionalInput, SegmentIdPaginationArgs, SegmentMetadata,
        TaskInfo, VacuumIndexInfo,
    },
    tantivy_footer::make_footer_fname,
    tantivy_index::{extract_segment_id, TantivyIndexScope},
    vacuum_util::VacuumType,
};

type Error = util::anyhow::Error;

#[derive(Clone, Debug)]
pub struct VacuumIndexInput<'a> {
    // If object_ids is None, all objects will be vacuumed.
    pub object_ids: Option<&'a [FullObjectId<'a>]>,
    pub global_store: Arc<dyn GlobalStore>,
    pub index_store: StoreInfo,
    pub locks_manager: &'a dyn GlobalLocksManager,
    pub config_file_schema: Option<Schema>,
    pub dry_run: bool,
}

#[derive(Clone, Debug, Default)]
pub struct VacuumIndexOptionalInput {
    pub segment_id_cursor: Option<Uuid>,
    pub testing_force_query_segment_metadatas_error_for_segment_id: Option<Uuid>,
    pub testing_force_list_error_for_segment_id: Option<Uuid>,
}

#[derive(Clone, Debug, Parser, Serialize, Deserialize)]
pub struct VacuumIndexOptions {
    #[arg(
        long,
        help = "Maximum number of segments to vacuum. If not provided, vacuum all segments.",
        env = "BRAINSTORE_VACUUM_INDEX_MAX_SEGMENTS"
    )]
    #[serde(default)]
    pub vacuum_index_max_segments: Option<usize>,
    // Query batch size is currently only respected when listing global segment_ids,
    // i.e. when `object_ids` is None.
    #[arg(
        long,
        default_value_t = default_vacuum_index_query_batch_size(),
        help = "Batch size for listing segment IDs to vacuum.",
        env = "BRAINSTORE_VACUUM_INDEX_QUERY_BATCH_SIZE",
    )]
    #[serde(default = "default_vacuum_index_query_batch_size")]
    pub vacuum_index_query_batch_size: usize,
    #[arg(
        long,
        default_value_t = default_vacuum_index_batch_size(),
        help = "Segment batch size for vacuuming index files.",
        env = "BRAINSTORE_VACUUM_INDEX_BATCH_SIZE",
    )]
    #[serde(default = "default_vacuum_index_batch_size")]
    pub vacuum_index_batch_size: usize,
    // To avoid interfering with in-progress read/write operations, don't delete files
    // last modified within the last `deletion_grace_period_seconds`.
    #[arg(
        long,
        default_value_t = default_vacuum_index_deletion_grace_period_seconds(),
        help = "Grace period for deleting index files.",
        env = "BRAINSTORE_VACUUM_INDEX_DELETION_GRACE_PERIOD_SECONDS",
    )]
    #[serde(default = "default_vacuum_index_deletion_grace_period_seconds")]
    pub vacuum_index_deletion_grace_period_seconds: i64,
    // This accounts for the fact that SegmentVacuumState::last_written_ts isn't a precise
    // "commit timestamp", since index writes can continue briefly after an index operation
    // writes its metadata update.
    #[arg(
        long,
        default_value_t = default_vacuum_index_last_written_slop_seconds(),
        help = "Buffer period to account for index writes that continue after setting the segment's last_written timestamp.",
        env = "BRAINSTORE_VACUUM_INDEX_LAST_WRITTEN_SLOP_SECONDS",
    )]
    #[serde(default = "default_vacuum_index_last_written_slop_seconds")]
    pub vacuum_index_last_written_slop_seconds: i64,
    // Minimum wait between vacuum operations on a segment.
    #[arg(
        long,
        default_value_t = default_vacuum_index_period_seconds(),
        help = "Minimum wait between vacuum operations on a segment.",
        env = "BRAINSTORE_VACUUM_INDEX_PERIOD_SECONDS",
    )]
    #[serde(default = "default_vacuum_index_period_seconds")]
    pub vacuum_index_period_seconds: i64,
    // If false, only vacuum files that match the tantivy index filename format. Otherwise,
    // delete unrecognized in the index directory too.
    #[arg(
        long,
        default_value_t = false,
        help = "Delete unrecognized files.",
        env = "BRAINSTORE_VACUUM_INDEX_DELETE_UNRECOGNIZED_FILES"
    )]
    #[serde(default)]
    pub vacuum_index_delete_unrecognized_files: bool,
    #[arg(
        long,
        default_value_t = default_delete_ops_batch_size(),
        help = "Batch size for delete ops files.",
        env = "BRAINSTORE_VACUUM_INDEX_DELETE_OPS_BATCH_SIZE",
    )]
    #[serde(default = "default_delete_ops_batch_size")]
    pub vacuum_index_delete_ops_batch_size: usize,
}

impl Default for VacuumIndexOptions {
    fn default() -> Self {
        Self {
            vacuum_index_max_segments: None,
            vacuum_index_query_batch_size: default_vacuum_index_query_batch_size(),
            vacuum_index_batch_size: default_vacuum_index_batch_size(),
            vacuum_index_deletion_grace_period_seconds:
                default_vacuum_index_deletion_grace_period_seconds(),
            vacuum_index_last_written_slop_seconds: default_vacuum_index_last_written_slop_seconds(
            ),
            vacuum_index_period_seconds: default_vacuum_index_period_seconds(),
            vacuum_index_delete_unrecognized_files: false,
            vacuum_index_delete_ops_batch_size: default_delete_ops_batch_size(),
        }
    }
}

fn default_vacuum_index_query_batch_size() -> usize {
    1000
}

fn default_vacuum_index_batch_size() -> usize {
    20
}

// This plus `default_vacuum_index_last_written_slop_seconds` should sum to 1 day to make the resulting
// query filter easier to reason about. (See `query_vacuum_index_segment_ids` in the global store.)
pub fn default_vacuum_index_deletion_grace_period_seconds() -> i64 {
    23 * 60 * 60 // 23 hours
}

pub fn default_vacuum_index_last_written_slop_seconds() -> i64 {
    60 * 60 // 1 hour
}

pub fn default_vacuum_index_period_seconds() -> i64 {
    2 * 24 * 60 * 60 // 2 days
}

fn default_delete_ops_batch_size() -> usize {
    1000
}

#[derive(Serialize)]
pub struct VacuumIndexStatelessOutput {
    pub success: bool,
    pub segment_id_cursor: Option<Uuid>,
    pub num_processed_segments: usize,
    pub planned_num_deletes: usize,
    pub planned_total_bytes: u64,
    pub num_deleted_files: usize,
    pub error: Option<String>,
}

#[tracing::instrument(skip(input, optional_input))]
pub async fn vacuum_index_stateless<'a>(
    input: VacuumIndexInput<'a>,
    optional_input: VacuumIndexOptionalInput,
    options: VacuumIndexOptions,
) -> VacuumIndexStatelessOutput {
    let input = &input;
    let optional_input = &optional_input;

    let initial_cursor = optional_input.segment_id_cursor;

    let segment_ids_stream = try_stream! {
        let mut cursor = initial_cursor;

        loop {
            match input.object_ids {
                Some(object_ids) => {
                    let mut segment_ids = input
                        .global_store
                        .list_segment_ids(object_ids, None)
                        .await?
                        .into_iter()
                        .flatten()
                        .collect::<Vec<Uuid>>();

                    // Apply a segment_id filter if we have an active cursor, then sort
                    // by ascending segment ID.
                    if let Some(cursor_id) = cursor {
                        segment_ids = segment_ids
                            .into_iter()
                            .filter(|id| id > &cursor_id)
                            .collect();
                    }
                    segment_ids.sort();

                    for segment_id in segment_ids {
                        yield segment_id;
                    }

                    // Currently `list_segment_ids` returns the entire result set,
                    // so we're done after one iteration. Once we implement pagination
                    // for this query, we'll be able to run this query in batches like
                    // we do for `list_segment_id_global`.
                    break;
                },
                None => {
                    let segment_ids = input
                        .global_store
                        .list_segment_ids_global(Some(ListSegmentIdsGlobalOptionalInput {
                            pagination_args: Some(SegmentIdPaginationArgs {
                                segment_id_cursor: cursor,
                                limit: options.vacuum_index_query_batch_size,
                            }),
                            ..Default::default()
                        }))
                        .await?;

                    if segment_ids.is_empty() {
                        break;
                    }

                    cursor = segment_ids.last().copied();

                    for segment_id in segment_ids {
                        yield segment_id;
                    }
                }
            };
        }
    };

    let limited_segment_ids_stream: BoxStream<'_, Result<Uuid, Error>> =
        if let Some(max) = options.vacuum_index_max_segments {
            segment_ids_stream.take(max).boxed()
        } else {
            segment_ids_stream.boxed()
        };

    let mut chunked_stream = limited_segment_ids_stream.try_chunks(options.vacuum_index_batch_size);

    let mut num_processed_segments = 0;
    let mut planned_num_deletes = 0;
    let mut planned_total_bytes = 0;
    let mut num_deleted_files = 0;
    let mut latest_cursor = initial_cursor;
    let mut iteration = 0;

    while let Some(chunk_result) = chunked_stream.next().await {
        match chunk_result {
            Ok(batch) => {
                if batch.is_empty() {
                    continue;
                }

                let batch_size = batch.len();
                let next_cursor = batch.last().copied();
                tracing::info!("[i={}] Vacuuming {} segments...", iteration, batch_size);

                let result = vacuum_index_batch(
                    batch,
                    Utc::now()
                        - Duration::seconds(
                            options.vacuum_index_deletion_grace_period_seconds as i64,
                        ),
                    input,
                    optional_input,
                    options.vacuum_index_delete_unrecognized_files,
                    options.vacuum_index_delete_ops_batch_size,
                )
                .await;
                if let Err(e) = result {
                    tracing::error!("Error vacuuming index segments: {}", e);
                    return VacuumIndexStatelessOutput {
                        success: false,
                        segment_id_cursor: latest_cursor,
                        num_processed_segments,
                        planned_num_deletes,
                        planned_total_bytes,
                        num_deleted_files,
                        error: Some(e.to_string()),
                    };
                }

                let output = result.unwrap();
                num_processed_segments += batch_size;
                planned_num_deletes += output.planned_num_deletes;
                planned_total_bytes += output.planned_total_bytes;
                num_deleted_files += output.num_deleted_files;
                tracing::info!(
                    "[i={}] Processed {} segments and deleted {} files (total segments: {}, total deleted files: {})",
                    iteration,
                    batch_size,
                    output.num_deleted_files,
                    num_processed_segments,
                    num_deleted_files,
                );

                latest_cursor = next_cursor;
            }
            Err(e) => {
                tracing::error!("Error processing segment batch: {}", e);
                return VacuumIndexStatelessOutput {
                    success: false,
                    segment_id_cursor: latest_cursor,
                    num_processed_segments,
                    planned_num_deletes,
                    planned_total_bytes,
                    num_deleted_files,
                    error: Some(e.to_string()),
                };
            }
        }

        iteration += 1;
    }

    if input.dry_run {
        tracing::info!(
            "Dry run: found a total of {} files totaling {} bytes across {} segments that would be deleted.",
            planned_num_deletes,
            planned_total_bytes,
            num_processed_segments,
        );
    } else {
        tracing::info!(
            "Vacuum complete. In total, processed {} segments and deleted {} files.",
            num_processed_segments,
            num_deleted_files
        );
    }

    VacuumIndexStatelessOutput {
        success: true,
        segment_id_cursor: latest_cursor,
        num_processed_segments,
        planned_num_deletes,
        planned_total_bytes,
        num_deleted_files,
        error: None,
    }
}

#[derive(Serialize)]
pub struct VacuumIndexOutput {
    pub num_processed_segments: usize,
    pub planned_num_deletes: usize,
    pub planned_total_bytes: u64,
    pub num_deleted_files: usize,
}

/// Vacuum stale index files, starting with the segments whose last successful vacuum is most behind
/// the last write to the segment. Segments that have never been vacuumed are prioritized first.
///
/// We skip a segment if either of the following is true:
///
///  1. We haven't waited long enough (vacuum_period_seconds) since this segment's last vacuum operation.
///  2. The last successful vacuum occurred enough time after the last write to the segment that
///     we can assume no additional index files were written to the segment. This is guaranteed if:
///
///     last_successful_start_ts - last_written_ts > deletion_grace_period_seconds + last_written_slop_seconds
///
/// Eligible segments are processed in batches. If a segment errors, we log the error to the global
/// store. Otherwise, we update `last_successful_start_ts` for every segment in the batch.
pub async fn vacuum_index<'a>(
    input: VacuumIndexInput<'a>,
    optional_input: VacuumIndexOptionalInput,
    options: VacuumIndexOptions,
) -> Result<VacuumIndexOutput> {
    if let Some(object_ids) = input.object_ids {
        if object_ids.is_empty() {
            tracing::debug!("No object IDs provided, skipping vacuum_index");
            return Ok(VacuumIndexOutput {
                num_processed_segments: 0,
                planned_num_deletes: 0,
                planned_total_bytes: 0,
                num_deleted_files: 0,
            });
        }
    }

    let vacuum_type = VacuumType::VacuumIndex;
    let _lock = input.locks_manager.write(&vacuum_type.lock_name()).await?;

    let input = &input;
    let optional_input = &optional_input;

    let max_last_successful_start_minus_last_written_seconds = options
        .vacuum_index_deletion_grace_period_seconds
        + options.vacuum_index_last_written_slop_seconds;

    let start_ts = Utc::now();

    let mut i = 0;
    let mut num_processed_segments = 0;
    let mut planned_num_deletes = 0;
    let mut planned_total_bytes = 0;
    let mut num_deleted_files = 0;
    loop {
        let batch_size = match options.vacuum_index_max_segments {
            None => options.vacuum_index_query_batch_size,
            Some(max_segments) => std::cmp::min(
                options.vacuum_index_query_batch_size,
                max_segments - num_processed_segments,
            ),
        };

        let segment_ids = input
            .global_store
            .query_vacuum_index_segment_ids(
                batch_size,
                max_last_successful_start_minus_last_written_seconds,
                options.vacuum_index_period_seconds,
                start_ts,
                input.object_ids,
            )
            .await?;
        if segment_ids.is_empty() {
            tracing::debug!("No segments are eligible for vacuum_index, exiting loop after running vacuum_index on {} segments (deleted {} files)", num_processed_segments, num_deleted_files);
            break;
        }

        let num_segment_ids = segment_ids.len();
        tracing::info!("[i={}] Found {} segment_ids to vacuum", i, num_segment_ids,);

        let max_file_last_modified =
            start_ts - Duration::seconds(options.vacuum_index_deletion_grace_period_seconds as i64);

        let batch_result = vacuum_index_batch(
            segment_ids.clone(),
            max_file_last_modified,
            input,
            optional_input,
            options.vacuum_index_delete_unrecognized_files,
            options.vacuum_index_delete_ops_batch_size,
        )
        .await;
        let batch_output = match batch_result {
            Ok(output) => output,
            Err(e) => {
                input
                    .global_store
                    .upsert_segment_task_info(
                        &segment_ids,
                        &TaskInfo::VacuumIndex(VacuumIndexInfo {
                            start_ts: Some(start_ts),
                            error: Some(serde_json::Value::String(e.to_string())),
                            ..Default::default()
                        }),
                    )
                    .await?;
                tracing::error!("[i={}] Error vacuuming index segments: {}", i, e);
                return Err(e);
            }
        };

        num_processed_segments += num_segment_ids;
        planned_num_deletes += batch_output.planned_num_deletes;
        planned_total_bytes += batch_output.planned_total_bytes;
        num_deleted_files += batch_output.num_deleted_files;
        tracing::info!(
            "[i={}] Vacuumed {} index files belonging to {} segments",
            i,
            num_deleted_files,
            num_segment_ids
        );

        // The batch succeeded, so bump `last_successful_start_ts` for the included segments
        // and update the task info.
        input
            .global_store
            .upsert_segment_vacuum_last_successful_start_ts(
                &segment_ids,
                VacuumType::VacuumIndex,
                start_ts,
            )
            .await?;
        input
            .global_store
            .upsert_segment_task_info(
                &segment_ids,
                &TaskInfo::VacuumIndex(VacuumIndexInfo {
                    start_ts: Some(start_ts),
                    completed_ts: Some(Utc::now()),
                    num_deleted_files_batch: Some(batch_output.num_deleted_files),
                    ..Default::default()
                }),
            )
            .await?;

        i += 1;
        if let Some(max_segments) = options.vacuum_index_max_segments {
            if num_processed_segments >= max_segments {
                tracing::info!(
                    "Reached maximum number of segments ({}), exiting vacuum_index loop",
                    max_segments
                );
                break;
            }
        }
    }

    Ok(VacuumIndexOutput {
        num_processed_segments,
        planned_num_deletes,
        planned_total_bytes,
        num_deleted_files,
    })
}

struct VacuumIndexBatchOutput {
    planned_num_deletes: usize,
    planned_total_bytes: u64,
    num_deleted_files: usize,
}

pub const DELETE_OPS_DIR: &str = "delete_ops";
pub const DRY_RUN_DELETE_OPS_DIR: &str = "dry_run_delete_ops";

#[tracing::instrument(skip(input, optional_input))]
async fn vacuum_index_batch<'a>(
    segment_ids: Vec<Uuid>,
    max_file_last_modified: DateTime<Utc>,
    input: &VacuumIndexInput<'a>,
    optional_input: &VacuumIndexOptionalInput,
    delete_unrecognized_files: bool,
    delete_ops_batch_size: usize,
) -> Result<VacuumIndexBatchOutput, Error> {
    let dry_run = input.dry_run;

    let active_index_chunk_uuids = input
        .global_store
        .query_segment_metadatas(&segment_ids)
        .await?
        .into_iter()
        .map(|segment_metadata| get_active_index_info(segment_metadata))
        .collect::<Result<Vec<Option<ActiveIndexInfo>>>>()?;

    if let Some(segment_id) =
        optional_input.testing_force_query_segment_metadatas_error_for_segment_id
    {
        if segment_ids.contains(&segment_id) {
            return Err(object_store::Error::Generic {
                store: "testing_force_query_segment_metadatas_error",
                source: anyhow!("Forced error for testing purposes").into(),
            })?;
        }
    }

    let segment_ids_and_active_index_chunk_uuids = segment_ids
        .into_iter()
        .zip(active_index_chunk_uuids)
        .filter_map(|(segment_id, active_uuids)| active_uuids.map(|uuids| (segment_id, uuids)));

    let index_store = &input.index_store;

    let mut planned_num_deletes = 0;
    let mut planned_total_bytes = 0;

    let vacuumable_index_files_stream: BoxStream<'_, Result<PathBuf, object_store::Error>> = {
        let planned_num_deletes = &mut planned_num_deletes;
        let planned_total_bytes = &mut planned_total_bytes;

        try_stream! {
            for (segment_id, active_index_chunk_uuids) in segment_ids_and_active_index_chunk_uuids {
                let index_scope = TantivyIndexScope::Segment(segment_id);
                let index_path = index_scope.path(&index_store.prefix);

                let testing_force_list_error = optional_input.testing_force_list_error_for_segment_id == Some(segment_id);

                let index_path_str = index_path
                    .to_str()
                    .ok_or(object_store::Error::Generic {
                        store: "index_path",
                        source: anyhow!("Invalid index path").into(),
                    })?
                    .to_string();
                let index_prefix = object_store::path::Path::from(index_path_str);

                let mut listing = index_store.store.list(Some(&index_prefix));
                while let Some(object_meta) = listing.try_next().await? {
                    if should_vacuum_index_object(max_file_last_modified, &object_meta, &active_index_chunk_uuids, delete_unrecognized_files) {
                        *planned_num_deletes += 1;
                        *planned_total_bytes += object_meta.size as u64;
                        yield PathBuf::from(object_meta.location.to_string());
                    }
                }

                if testing_force_list_error {
                    Err(object_store::Error::Generic {
                        store: "testing_force_list_error",
                        source: anyhow!("Forced listing error for testing purposes").into(),
                    })?;
                }
            }
        }
        .boxed()
    };

    let paths_to_delete_stream = try_stream! {
        let mut chunked_stream = vacuumable_index_files_stream.try_chunks(delete_ops_batch_size);
        while let Some(chunk_result) = chunked_stream.next().await {
            let paths = chunk_result.map_err(|e| object_store::Error::Generic {
                store: "delete_ops",
                source: anyhow!("Failed to write delete ops file: {}", e).into(),
            })?;
            assert!(!paths.is_empty(), "Impossible: empty chunk");

            let fname = generate_delete_ops_id(std::time::SystemTime::now());
            let file_content = paths.iter()
                .map(|p| match p.to_str() {
                    Some(s) => Ok(s.to_string()),
                    None => Err(object_store::Error::Generic {
                        store: "delete_ops",
                        source: anyhow!("Failed to convert path to string").into(),
                    })
                })
                .collect::<Result<Vec<_>, _>>()?
                .join("\n");

            // If this is a dry run, write the delete log to a separate directory for
            // debugging purposes.
            let delete_ops_dir_name = if dry_run {
                DRY_RUN_DELETE_OPS_DIR
            } else {
                DELETE_OPS_DIR
            };

            let delete_ops_path = input.index_store.prefix.join(delete_ops_dir_name).join(&fname);
            let delete_ops_path_str = delete_ops_path
                .to_str()
                .ok_or(object_store::Error::Generic {
                    store: "delete_ops",
                    source: anyhow!("Failed to convert path to string").into(),
                })?;
            let object_path = object_store::path::Path::from(delete_ops_path_str);

            input.index_store.store
                .put(&object_path, file_content.into())
                .await
                .map_err(|e| object_store::Error::Generic {
                    store: "delete_ops",
                    source: anyhow!("Failed to write delete ops file: {}", e).into(),
                })?;

            for path in paths {
                yield path;
            }
        }
    }
    .boxed();

    if dry_run {
        let _ = paths_to_delete_stream.try_collect::<Vec<_>>().await?;
        tracing::info!(
            "Dry run: would delete {} inactive index files totaling {} bytes",
            planned_num_deletes,
            planned_total_bytes,
        );
        return Ok(VacuumIndexBatchOutput {
            planned_num_deletes,
            planned_total_bytes,
            num_deleted_files: 0,
        });
    }

    let delete_stream = input
        .index_store
        .directory
        .delete_stream(paths_to_delete_stream);

    let num_deleted_files = delete_stream
        .try_fold(0, |count, _| async move { Ok(count + 1) })
        .await?;

    tracing::debug!(
        "Vacuumed {} index segment files totaling {} bytes",
        num_deleted_files,
        planned_total_bytes,
    );

    Ok(VacuumIndexBatchOutput {
        planned_num_deletes,
        planned_total_bytes,
        num_deleted_files,
    })
}

struct ActiveIndexInfo {
    active_index_chunk_uuids: Vec<SegmentId>,
    footer_file_path: String,
}

fn get_active_index_info(segment_metadata: SegmentMetadata) -> Result<Option<ActiveIndexInfo>> {
    let index_meta = segment_metadata.last_compacted_index_meta;
    if let Some(index_meta) = index_meta {
        let active_index_chunk_uuids: Vec<SegmentId> = index_meta
            .tantivy_meta
            .segments
            .iter()
            .map(|segment_meta| segment_meta.segment_id)
            .collect();
        let footer_file_path = make_footer_fname(&index_meta.tantivy_meta).and_then(|path| {
            path.as_os_str()
                .to_str()
                .map(|s| s.to_string())
                .ok_or(util::anyhow::anyhow!("Failed to make footer fname"))
        })?;
        Ok(Some(ActiveIndexInfo {
            active_index_chunk_uuids,
            footer_file_path,
        }))
    } else {
        Ok(None)
    }
}

fn should_vacuum_index_object(
    max_file_last_modified: DateTime<Utc>,
    object_meta: &ObjectMeta,
    active_index_info: &ActiveIndexInfo,
    delete_unrecognized_files: bool,
) -> bool {
    let filename = match object_meta.location.filename() {
        None => return false,
        Some("meta.json") => return false,
        Some(".managed.json") => return false,
        Some(filename) if filename == active_index_info.footer_file_path => return false,
        Some(filename) => filename,
    };

    let chunk_uuid_opt = extract_segment_id(&std::path::Path::new(filename));

    // As a safety measure, if `delete_unrecognized_files` is false, only delete files that appear
    // to be valid tantivy index files.
    if !delete_unrecognized_files {
        if chunk_uuid_opt.is_none() && !filename.ends_with(".footer") {
            tracing::debug!("Skipping unrecognized file: {}", filename);
            return false;
        }
    }

    // Only vacuum files that don't appear in the index meta and were last
    // modified before the file deletion cutoff (grace period).
    if object_meta.last_modified > max_file_last_modified
        || chunk_uuid_opt
            .map(|chunk_uuid| {
                active_index_info
                    .active_index_chunk_uuids
                    .contains(&chunk_uuid)
            })
            .unwrap_or(false)
    {
        tracing::debug!("Skipping index file {} because it's referenced in the index meta or was modified after the cutoff for file deletion", filename);
        return false;
    }

    tracing::debug!(
        "Found vacuum-eligible index file: {}",
        object_meta.location.to_string()
    );

    true
}

pub fn generate_delete_ops_id(time: std::time::SystemTime) -> String {
    let timestamp = time
        .duration_since(std::time::UNIX_EPOCH)
        .unwrap()
        .as_secs();
    let uuid = Uuid::new_v4();
    format!("{:012}.{}", timestamp, uuid)
}
