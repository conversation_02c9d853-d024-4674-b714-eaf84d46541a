use btql::{binder::ast::Ali<PERSON>, schema::ScalarType};
use itertools::izip;
use lazy_static::lazy_static;
use serde::Serialize;
use serde_json::{json, Map};
use std::{
    borrow::Cow,
    collections::{HashMap, HashSet},
    io,
    ops::Add,
    str::FromStr,
    sync::Arc,
};

use storage::{
    process_wal::{PAGINATION_KEY_FIELD, XACT_ID_FIELD},
    tantivy_index::JSON_ROOT_FIELD,
};
use tantivy::{
    collector::{Collector, SegmentCollector},
    columnar::{
        column_values::{monotonic_map_column, StrictlyMonotonicFn},
        Column, ColumnType, ColumnarReader, DynamicColumn, DynamicColumnHandle, NumericalType,
        StrColumn,
    },
    schema::OwnedValue,
    DateTime, DocAddress, SegmentOrdinal, SegmentReader,
};
use util::{
    tracer::{trace_if, TracedNode},
    xact::{PaginationKey, TransactionId},
    Value,
};

use crate::{
    interpreter::{
        aggregate::{Aggregator, FloatAvg, FloatSum},
        context::ModelCostsMap,
        error::InterpreterError,
        local::interpret_expr_value,
    },
    planner::{tantivy::make_json_path_name, JSON_PATH_SEGMENT_SEP},
};
use btql::{interpreter::context::ExprContext, typesystem::CastInto};

use crate::interpreter::error::Result;

use super::{
    aggregate::get_fast_fields_impl,
    columnstore::{
        collect_column_for_doc_batch_primitive, collect_column_for_doc_batch_str,
        get_columns_matching_prefix,
    },
    expand::{CREATED_FIELD, ID_FIELD, IS_ROOT_FIELD, ROOT_SPAN_ID_FIELD, SPAN_ID_FIELD},
};

lazy_static! {
    // Annoyingly, make_json_path_name doesn't add the path segment separator to the root
    static ref SCORE_FIELD_PREFIX: String = (|| {
        let mut full_name = "scores".to_string();
        full_name.push(JSON_PATH_SEGMENT_SEP as char);
        full_name.push_str(JSON_ROOT_FIELD);
        full_name
    })();

    static ref SPAN_TYPE_FIELD: String = make_json_path_name("span_attributes_json", &["type".to_string()], false);
    static ref SPAN_PURPOSE_FIELD: String = make_json_path_name("span_attributes_json", &["purpose".to_string()], false);
    static ref SPAN_NAME_FIELD: String = make_json_path_name("span_attributes_json", &["name".to_string()], false);
    static ref SPAN_GENERATION_FIELD: String = make_json_path_name("span_attributes_json", &["generation".to_string()], false);

    static ref MODEL_FIELD: String = make_json_path_name("metadata_json", &["model".to_string()], false);

    static ref PROMPT_TOKENS_FIELD: String = make_json_path_name("metrics", &["prompt_tokens".to_string()], false);
    static ref COMPLETION_TOKENS_FIELD: String = make_json_path_name("metrics", &["completion_tokens".to_string()], false);
    static ref PROMPT_CACHED_TOKENS_FIELD: String = make_json_path_name("metrics", &["prompt_cached_tokens".to_string()], false);
    static ref PROMPT_CACHE_CREATION_TOKENS_FIELD: String = make_json_path_name("metrics", &["prompt_cache_creation_tokens".to_string()], false);
    static ref CACHED_FIELD: String = make_json_path_name("metrics", &["cached".to_string()], false);
    static ref REMOTE_FIELD: String = make_json_path_name("metrics", &["remote".to_string()], false);
    static ref START_FIELD: String = make_json_path_name("metrics", &["start".to_string()], false);
    static ref END_FIELD: String = make_json_path_name("metrics", &["end".to_string()], false);
}

pub const PREVIEW_FIELD_LENGTH: usize = 125;

pub struct SummaryCollector {
    pub model_costs: Arc<Option<ModelCostsMap>>,
}

#[derive(Debug)]
pub struct SummaryFruit {
    pub scores: HashMap<String, FloatAvg>,
    pub root_doc: Option<RootDoc>,
    pub root_fields: Option<RootFields>,
    pub metrics: Metrics,
}

#[derive(Debug)]
pub struct Metrics {
    total_prompt_tokens: Option<u64>,
    total_completion_tokens: Option<u64>,
    total_prompt_cached_tokens: Option<u64>,
    total_prompt_cache_creation_tokens: Option<u64>,
    total_prompt_cost: Option<f64>,
    total_completion_cost: Option<f64>,
    all_cached: Option<bool>,
    first_start: Option<f64>,
    last_end: Option<f64>,
    max_duration: Option<f64>,
    max_task_duration: Option<f64>,
    sum_llm_duration: FloatSum,
}

impl Metrics {
    fn combine(&mut self, other: Metrics) -> Result<()> {
        self.total_prompt_tokens =
            coalesce_add(self.total_prompt_tokens, other.total_prompt_tokens);
        self.total_completion_tokens =
            coalesce_add(self.total_completion_tokens, other.total_completion_tokens);
        self.total_prompt_cached_tokens = coalesce_add(
            self.total_prompt_cached_tokens,
            other.total_prompt_cached_tokens,
        );
        self.total_prompt_cache_creation_tokens = coalesce_add(
            self.total_prompt_cache_creation_tokens,
            other.total_prompt_cache_creation_tokens,
        );

        self.total_prompt_cost = coalesce_add(self.total_prompt_cost, other.total_prompt_cost);
        self.total_completion_cost =
            coalesce_add(self.total_completion_cost, other.total_completion_cost);

        self.all_cached = match (self.all_cached, other.all_cached) {
            (Some(a), Some(b)) => Some(a && b),
            (Some(a), None) => Some(a),
            (None, Some(b)) => Some(b),
            (None, None) => None,
        };
        self.first_start = match (self.first_start, other.first_start) {
            (Some(a), Some(b)) => Some(a.min(b)),
            (Some(a), None) => Some(a),
            (None, Some(b)) => Some(b),
            (None, None) => None,
        };
        self.last_end = match (self.last_end, other.last_end) {
            (Some(a), Some(b)) => Some(a.max(b)),
            (Some(a), None) => Some(a),
            (None, Some(b)) => Some(b),
            (None, None) => None,
        };
        self.max_duration = match (self.max_duration, other.max_duration) {
            (Some(a), Some(b)) => Some(a.max(b)),
            (Some(a), None) => Some(a),
            (None, Some(b)) => Some(b),
            (None, None) => None,
        };
        self.max_task_duration = match (self.max_task_duration, other.max_task_duration) {
            (Some(a), Some(b)) => Some(a.max(b)),
            (Some(a), None) => Some(a),
            (None, Some(b)) => Some(b),
            (None, None) => None,
        };
        self.sum_llm_duration.combine(other.sum_llm_duration)?;
        Ok(())
    }
}
impl Default for Metrics {
    fn default() -> Self {
        Self {
            total_prompt_tokens: None,
            total_completion_tokens: None,
            total_prompt_cached_tokens: None,
            total_prompt_cache_creation_tokens: None,
            total_prompt_cost: None,
            total_completion_cost: None,
            all_cached: None,
            first_start: None,
            last_end: None,
            max_duration: None,
            max_task_duration: None,
            sum_llm_duration: FloatSum::new(&btql::binder::ast::Expr::Literal(
                btql::binder::ast::Literal {
                    value: Value::Null,
                    expr_type: ScalarType::Null,
                },
            )),
        }
    }
}

#[derive(Debug)]
pub enum RootDoc {
    Address(DocAddress),
    Doc(Value),
}

#[derive(Debug)]
pub struct RootFields {
    pub id: String,
    pub span_id: String,
    pub root_span_id: String,
    pub span_name: Option<String>,
    pub span_type: Option<String>,
    pub _xact_id: u64,
    pub _pagination_key: u64,
    pub created: String,
    pub remote: Option<u64>,
    pub generation: Option<String>,
}

pub const SUMMARY_PREVIEW_FIELDS: [&str; 5] = ["input", "output", "expected", "error", "metadata"];
pub const SUMMARY_FULL_FIELDS: [&str; 2] = ["tags", "origin"];

pub struct SummaryIntoRowInput<'a> {
    pub expr_ctx: &'a ExprContext,
    pub comparison_key: String,
    pub has_error: bool,
    pub root_doc: Value,
    pub weighted_scores: &'a Vec<Alias>,
    pub custom_columns: &'a Vec<Alias>,
    pub preview_length: Option<usize>,
}

impl SummaryFruit {
    pub fn into_row(
        self,
        tracer: Option<Arc<TracedNode>>,
        args: SummaryIntoRowInput,
    ) -> Result<(Option<Value>, Vec<Option<Value>>)> {
        // Very large fields can have super expensive destructors. So instead of dropping them in this function,
        // we collect them and drop them in a background thread.
        let mut trash_can = Vec::new();

        let SummaryIntoRowInput {
            expr_ctx,
            comparison_key,
            has_error,
            mut root_doc,
            weighted_scores,
            custom_columns,
            preview_length,
        } = args;
        let root_fields = match self.root_fields {
            Some(f) => f,
            None => {
                log::debug!("No root fields found. Will skip row");
                return Ok((None, trash_can));
            }
        };

        let cached = match self.metrics.all_cached {
            Some(true) => Some(1),
            Some(false) => Some(0),
            None => None,
        };

        let mut scores = Value::Object(
            self.scores
                .into_iter()
                .map(|(name, agg)| {
                    (
                        name,
                        agg.collect()
                            .expect("failed to collect float avg for score"),
                    )
                })
                .collect(),
        );
        for weighted_score in weighted_scores {
            let v = interpret_expr_value(expr_ctx, &weighted_score.expr, &scores)?.into_owned();
            scores
                .as_object_mut()
                .unwrap()
                .insert(weighted_score.alias.clone(), v);
        }

        let mut base_row = json!({
            "id": root_fields.id,
            "comparison_key": comparison_key,
            "root_span_id": root_fields.root_span_id,
            "span_id": root_fields.span_id,
            "_xact_id": TransactionId(root_fields._xact_id).to_string(),
            "_pagination_key": PaginationKey(root_fields._pagination_key).to_string(),
            "created": root_fields.created,
            "span_type_info": json!({
                "name": root_fields.span_name,
                "type": root_fields.span_type,
                "cached": cached.unwrap_or(0),
                "remote": root_fields.remote.unwrap_or(0),
                "has_error": has_error,
                "in_progress": self.metrics.last_end.is_none() && self.metrics.first_start.is_some(),
                "generation": root_fields.generation,
            }),
            "span_attributes": json!({
                "name": root_fields.span_name,
                "type": root_fields.span_type,
            }),
            "scores": scores,
            "metrics": json!({
                "cached": cached,
                "start": self.metrics.first_start,
                "end": self.metrics.last_end,
                "duration": self.metrics.max_task_duration.map_or(self.metrics.max_duration, |d| Some(d)),
                "llm_duration": self.metrics.sum_llm_duration.collect().expect("failed to collect float sum for llm duration"),
                "prompt_tokens": self.metrics.total_prompt_tokens.unwrap_or(0),
                "completion_tokens": self.metrics.total_completion_tokens.unwrap_or(0),
                "prompt_cached_tokens": self.metrics.total_prompt_cached_tokens.unwrap_or(0),
                "prompt_cache_creation_tokens": self.metrics.total_prompt_cache_creation_tokens.unwrap_or(0),
                "total_tokens": coalesce_add(self.metrics.total_prompt_tokens, self.metrics.total_completion_tokens).unwrap_or(0),
                "estimated_cost": coalesce_add(self.metrics.total_prompt_cost, self.metrics.total_completion_cost),
            }),
        });

        trace_if(log::Level::Debug, &tracer, "Custom columns", |_child| {
            if !custom_columns.is_empty() {
                // Merge all of the root doc fields into the base row.
                let root_doc_obj = root_doc.as_object_mut().expect("root doc is not an object");

                let mut root_doc_keys = Vec::new();
                for key in root_doc_obj.keys() {
                    let base_row_obj = base_row.as_object_mut().expect("base row is not an object");

                    if base_row_obj.contains_key(key) {
                        // Real-time root docs will contain overlapping fields with the base row, like
                        // id, so we want to just skip these.
                        continue;
                    }

                    root_doc_keys.push(key.clone());
                }

                for key in root_doc_keys.iter() {
                    let value = root_doc_obj.remove(key).expect("original key not found");
                    let base_row_obj = base_row.as_object_mut().expect("base row is not an object");
                    base_row_obj.insert(key.clone(), value);
                }

                for custom_column in custom_columns {
                    let (v, trash) = make_preview_field(
                        interpret_expr_value(expr_ctx, &custom_column.expr, &base_row)?
                            .into_owned(),
                        preview_length.unwrap_or(PREVIEW_FIELD_LENGTH),
                    );
                    trash_can.push(trash);
                    base_row
                        .as_object_mut()
                        .expect("base row is not an object")
                        .insert(custom_column.alias.clone(), v);
                }

                // Now, remove the root doc fields from the base row.
                let mut new_root_doc = Map::new();
                for key in root_doc_keys {
                    let value = base_row
                        .as_object_mut()
                        .expect("base row is not an object")
                        .remove(&key)
                        .expect("original key not found");
                    new_root_doc.insert(key, value);
                }
                root_doc = Value::Object(new_root_doc);
            }
            Ok::<_, InterpreterError>(())
        })?;

        // Finally, add in the preview fields.
        trace_if(log::Level::Debug, &tracer, "Preview fields", |child| {
            let root_doc_obj = root_doc.as_object_mut().expect("root doc is not an object");
            for field in SUMMARY_PREVIEW_FIELDS {
                let (v, trash) = trace_if(log::Level::Debug, &child, field, |_child| {
                    make_preview_field(
                        root_doc_obj.remove(field).unwrap_or(Value::Null),
                        preview_length.unwrap_or(PREVIEW_FIELD_LENGTH),
                    )
                });
                trash_can.push(trash);
                base_row
                    .as_object_mut()
                    .expect("base row is not an object")
                    .insert(field.to_string(), v);
            }
            // and the full fields
            for field in SUMMARY_FULL_FIELDS {
                let v = root_doc_obj.remove(field).unwrap_or(Value::Null);
                base_row
                    .as_object_mut()
                    .expect("base row is not an object")
                    .insert(field.to_string(), v);
            }
            Ok::<_, InterpreterError>(())
        })?;

        Ok((Some(base_row), trash_can))
    }
}

impl Collector for SummaryCollector {
    type Fruit = HashMap<String, SummaryFruit>;
    type Child = SummarySegmentCollector;

    fn for_segment(
        &self,
        segment_ord: SegmentOrdinal,
        segment_reader: &SegmentReader,
    ) -> tantivy::Result<SummarySegmentCollector> {
        let fast_fields = segment_reader.fast_fields();
        let fast_fields_impl = get_fast_fields_impl(fast_fields);

        let id_field = fast_fields
            .str(ID_FIELD)?
            .ok_or_else(|| tantivy::TantivyError::SchemaError(format!("id field not found")))?;

        let root_span_id_field = fast_fields.str(ROOT_SPAN_ID_FIELD)?.ok_or_else(|| {
            tantivy::TantivyError::SchemaError(format!("root_span_id field not found"))
        })?;

        let span_id_field = fast_fields.str(SPAN_ID_FIELD)?.ok_or_else(|| {
            tantivy::TantivyError::SchemaError(format!("span_id field not found"))
        })?;

        let is_root_field = fast_fields.bool(IS_ROOT_FIELD)?;
        let xact_id_field = fast_fields.u64(XACT_ID_FIELD)?;
        let pagination_key_field = fast_fields.u64(PAGINATION_KEY_FIELD)?;
        let created_field = fast_fields.date(CREATED_FIELD)?;

        let span_name_field = get_nested_field(
            &fast_fields_impl.columnar,
            &*SPAN_NAME_FIELD,
            Some(ColumnType::Str),
        )?
        .map(must_open_str_column)
        .transpose()?;
        let span_type_field = get_nested_field(
            &fast_fields_impl.columnar,
            &*SPAN_TYPE_FIELD,
            Some(ColumnType::Str),
        )?
        .map(must_open_str_column)
        .transpose()?;
        let span_generation_field = get_nested_field(
            &fast_fields_impl.columnar,
            &*SPAN_GENERATION_FIELD,
            Some(ColumnType::Str),
        )?
        .map(must_open_str_column)
        .transpose()?;
        let span_purpose_field = get_nested_field(
            &fast_fields_impl.columnar,
            &*SPAN_PURPOSE_FIELD,
            Some(ColumnType::Str),
        )?
        .map(must_open_str_column)
        .transpose()?;

        let model_field = get_nested_field(
            &fast_fields_impl.columnar,
            &*MODEL_FIELD,
            Some(ColumnType::Str),
        )?
        .map(must_open_str_column)
        .transpose()?;

        // NOTE: Unlike the string fields, we don't need to force the numerical fields to be a specific type, since they'll
        // be coerced.
        let prompt_tokens_field =
            get_nested_field(&fast_fields_impl.columnar, &*PROMPT_TOKENS_FIELD, None)?
                .map(|f| must_open_u64_column("prompt_tokens", f))
                .transpose()?;
        let completion_tokens_field =
            get_nested_field(&fast_fields_impl.columnar, &*COMPLETION_TOKENS_FIELD, None)?
                .map(|f| must_open_u64_column("completion_tokens", f))
                .transpose()?;
        let prompt_cached_tokens_field = get_nested_field(
            &fast_fields_impl.columnar,
            &*PROMPT_CACHED_TOKENS_FIELD,
            None,
        )?
        .map(|f| must_open_u64_column("prompt_cached_tokens", f))
        .transpose()?;
        let prompt_cache_creation_tokens_field = get_nested_field(
            &fast_fields_impl.columnar,
            &*PROMPT_CACHE_CREATION_TOKENS_FIELD,
            None,
        )?
        .map(|f| must_open_u64_column("prompt_cache_creation_tokens", f))
        .transpose()?;

        let cached_field = get_nested_field(&fast_fields_impl.columnar, &*CACHED_FIELD, None)?
            .map(|f| must_open_u64_column("cached", f))
            .transpose()?;
        let remote_field = get_nested_field(&fast_fields_impl.columnar, &*REMOTE_FIELD, None)?
            .map(|f| must_open_u64_column("remote", f))
            .transpose()?;
        let start_field = get_nested_field(&fast_fields_impl.columnar, &*START_FIELD, None)?
            .map(|f| must_open_f64_column("start", f))
            .transpose()?;
        let end_field = get_nested_field(&fast_fields_impl.columnar, &*END_FIELD, None)?
            .map(|f| must_open_f64_column("end", f))
            .transpose()?;

        let score_fields =
            get_columns_matching_prefix(&fast_fields_impl.columnar, &*SCORE_FIELD_PREFIX)?
                .into_iter()
                .map(|(name, col)| {
                    let f64_column = must_open_f64_column(&name, col)?;

                    Ok::<_, tantivy::TantivyError>((get_leaf_field_name(&name), f64_column))
                })
                .collect::<Result<Vec<_>, _>>()?;

        Ok(SummarySegmentCollector {
            model_costs: self.model_costs.clone(),
            segment_ord,

            // Base fields
            id_field,
            root_span_id_field,
            span_id_field,
            is_root_field,
            xact_id_field,
            pagination_key_field,
            created_field,

            // Attributes
            span_name_field,
            span_type_field,
            span_purpose_field,
            span_generation_field,

            // Metadata
            model_field,

            // Metrics
            prompt_tokens_field,
            completion_tokens_field,
            prompt_cached_tokens_field,
            prompt_cache_creation_tokens_field,
            cached_field,
            remote_field,
            start_field,
            end_field,

            // Scores
            score_fields,
            score_aggs: HashMap::new(),

            error: None,
        })
    }

    fn merge_fruits(
        &self,
        segment_fruits: Vec<<Self::Child as SegmentCollector>::Fruit>,
    ) -> tantivy::Result<Self::Fruit> {
        let mut buckets: HashMap<String, SummaryFruit> = HashMap::new();

        for (segment_fruit, error) in segment_fruits {
            if let Some(error) = error {
                return Err(tantivy::TantivyError::InternalError(error));
            }
            merge_summary_fruits(&mut buckets, segment_fruit)?;
        }

        Ok(buckets)
    }

    fn requires_scoring(&self) -> bool {
        false
    }
}

pub fn merge_summary_fruits(
    buckets: &mut HashMap<String, SummaryFruit>,
    segment_fruit: HashMap<String, SummaryFruit>,
) -> tantivy::Result<()> {
    for (key, fruit) in segment_fruit {
        let id_bucket = buckets.entry(key).or_insert_with(|| SummaryFruit {
            scores: HashMap::new(),
            root_doc: None,
            root_fields: None,
            metrics: Metrics::default(),
        });
        if let Some(root_fields) = fruit.root_fields {
            id_bucket.root_fields = Some(root_fields);
        }
        if let Some(root_doc) = fruit.root_doc {
            id_bucket.root_doc = Some(root_doc);
        }

        for (name, score) in fruit.scores {
            use std::collections::hash_map::Entry::*;
            match id_bucket.scores.entry(name) {
                Occupied(agg) => {
                    agg.into_mut()
                        .combine(score)
                        .map_err(|e| tantivy::TantivyError::InternalError(e.to_string()))?;
                }
                Vacant(agg) => {
                    agg.insert(score);
                }
            }
        }
        id_bucket
            .metrics
            .combine(fruit.metrics)
            .map_err(|e| tantivy::TantivyError::InternalError(e.to_string()))?;
    }

    Ok(())
}

fn get_leaf_field_name(json_path: &str) -> String {
    json_path
        .split(JSON_PATH_SEGMENT_SEP as char)
        .last()
        .expect("at least one name piece")
        .to_string()
}

pub struct SummarySegmentAggBucket {
    root_fields: Option<RootFields>,
    root_doc: Option<RootDoc>,
    metrics: Metrics,
    scores: Vec<FloatAvg>,
}

impl SummarySegmentAggBucket {
    fn new(num_scores: usize) -> Self {
        Self {
            root_fields: None,
            root_doc: None,
            metrics: Metrics::default(),
            scores: vec![
                FloatAvg::new(&btql::binder::ast::Expr::Literal(
                    btql::binder::ast::Literal {
                        value: Value::Null,
                        expr_type: ScalarType::Null,
                    },
                ));
                num_scores
            ],
        }
    }
}

pub struct SummarySegmentCollector {
    model_costs: Arc<Option<ModelCostsMap>>,
    segment_ord: SegmentOrdinal,

    id_field: StrColumn,
    root_span_id_field: StrColumn,
    span_id_field: StrColumn,
    is_root_field: Column<bool>,
    xact_id_field: Column<u64>,
    pagination_key_field: Column<u64>,
    created_field: Column<DateTime>,

    span_name_field: Option<StrColumn>,
    span_type_field: Option<StrColumn>,
    span_purpose_field: Option<StrColumn>,
    span_generation_field: Option<StrColumn>,

    model_field: Option<StrColumn>,

    prompt_tokens_field: Option<Column<u64>>,
    completion_tokens_field: Option<Column<u64>>,
    prompt_cached_tokens_field: Option<Column<u64>>,
    prompt_cache_creation_tokens_field: Option<Column<u64>>,
    cached_field: Option<Column<u64>>,
    remote_field: Option<Column<u64>>,
    start_field: Option<Column<f64>>,
    end_field: Option<Column<f64>>,

    score_fields: Vec<(String, Column<f64>)>,
    score_aggs: HashMap<String, SummarySegmentAggBucket>,

    error: Option<String>,
}

impl SegmentCollector for SummarySegmentCollector {
    type Fruit = (HashMap<String, SummaryFruit>, Option<String>);

    fn collect_block(&mut self, docs: &[tantivy::DocId]) {
        match self.collect_block_fallible(docs) {
            Ok(_) => {}
            Err(e) => {
                self.error = Some(e.to_string());
            }
        }
    }

    fn collect(&mut self, doc: tantivy::DocId, _score: tantivy::Score) {
        self.collect_block(&[doc]);
    }

    fn harvest(self) -> Self::Fruit {
        if let Some(error) = self.error {
            return (HashMap::new(), Some(error));
        }
        let mut fruit = HashMap::new();
        for (id, bucket) in self.score_aggs {
            fruit.insert(
                id,
                SummaryFruit {
                    scores: self
                        .score_fields
                        .iter()
                        .zip(bucket.scores)
                        .map(|((name, _), agg)| (name.clone(), agg))
                        .collect(),
                    metrics: bucket.metrics,
                    root_doc: bucket.root_doc,
                    root_fields: bucket.root_fields,
                },
            );
        }

        (fruit, None)
    }
}

impl SummarySegmentCollector {
    #[inline(always)]
    fn collect_block_fallible(&mut self, docs: &[u32]) -> std::io::Result<()> {
        let block_root_span_ids =
            get_required_string_column(&ROOT_SPAN_ID_FIELD, &self.root_span_id_field, docs)?;

        let block_ids = get_required_string_column(&ID_FIELD, &self.id_field, docs)?;
        let block_span_ids = get_required_string_column(&SPAN_ID_FIELD, &self.span_id_field, docs)?;

        let mut block_is_root = vec![None; docs.len()];
        self.is_root_field.first_vals(docs, &mut block_is_root);
        let block_is_root = block_is_root
            .iter()
            .map(|v| {
                v.ok_or_else(|| {
                    std::io::Error::new(std::io::ErrorKind::InvalidData, "is_root field is not set")
                })
            })
            .collect::<Result<Vec<_>, _>>()?;

        let mut block_xact_id = vec![None; docs.len()];
        self.xact_id_field.first_vals(docs, &mut block_xact_id);
        let block_xact_id = block_xact_id
            .iter()
            .map(|v| {
                v.ok_or_else(|| {
                    std::io::Error::new(std::io::ErrorKind::InvalidData, "xact_id field is not set")
                })
            })
            .collect::<Result<Vec<_>, _>>()?;

        let mut block_pagination_key = vec![None; docs.len()];
        self.pagination_key_field
            .first_vals(docs, &mut block_pagination_key);
        let block_pagination_key = block_pagination_key
            .iter()
            .map(|v| {
                v.ok_or_else(|| {
                    std::io::Error::new(
                        std::io::ErrorKind::InvalidData,
                        "pagination_key field is not set",
                    )
                })
            })
            .collect::<Result<Vec<_>, _>>()?;

        let mut block_created = vec![None; docs.len()];
        self.created_field.first_vals(docs, &mut block_created);
        let block_created = block_created
            .iter()
            .map(|v| {
                v.ok_or_else(|| {
                    std::io::Error::new(std::io::ErrorKind::InvalidData, "created field is not set")
                })
            })
            .collect::<Result<Vec<_>, _>>()?;

        let block_span_name =
            get_optional_string_column("span_attributes.name", &self.span_name_field, docs)?;
        let block_span_type =
            get_optional_string_column("span_attributes.type", &self.span_type_field, docs)?;
        let block_span_purpose =
            get_optional_string_column("span_attributes.purpose", &self.span_purpose_field, docs)?;
        let block_span_generation = get_optional_string_column(
            "span_attributes.generation",
            &self.span_generation_field,
            docs,
        )?;

        let block_model = get_optional_string_column(&MODEL_FIELD, &self.model_field, docs)?;

        let mut block_prompt_tokens = vec![None; docs.len()];
        if let Some(col) = &self.prompt_tokens_field {
            col.first_vals(docs, &mut block_prompt_tokens);
        }
        let mut block_completion_tokens = vec![None; docs.len()];
        if let Some(col) = &self.completion_tokens_field {
            col.first_vals(docs, &mut block_completion_tokens);
        }
        let mut block_prompt_cached_tokens = vec![None; docs.len()];
        if let Some(col) = &self.prompt_cached_tokens_field {
            col.first_vals(docs, &mut block_prompt_cached_tokens);
        }
        let mut block_prompt_cache_creation_tokens = vec![None; docs.len()];
        if let Some(col) = &self.prompt_cache_creation_tokens_field {
            col.first_vals(docs, &mut block_prompt_cache_creation_tokens);
        }
        let mut block_cached_field = vec![None; docs.len()];
        if let Some(col) = &self.cached_field {
            col.first_vals(docs, &mut block_cached_field);
        }
        let mut block_remote_field = vec![None; docs.len()];
        if let Some(col) = &self.remote_field {
            col.first_vals(docs, &mut block_remote_field);
        }

        let mut block_start_field = vec![None; docs.len()];
        if let Some(col) = &self.start_field {
            col.first_vals(docs, &mut block_start_field);
        }
        let mut block_end_field = vec![None; docs.len()];
        if let Some(col) = &self.end_field {
            col.first_vals(docs, &mut block_end_field);
        }

        let block_scores = self
            .score_fields
            .iter()
            .map(|(_, col)| collect_column_for_doc_batch_primitive(col, docs))
            .collect::<Vec<_>>();

        for (
            id_idx,
            (
                id,
                root_span_id,
                span_id,
                is_root,
                xact_id,
                pagination_key,
                created,
                span_name,
                span_type,
                span_purpose,
                span_generation,
                model,
                prompt_tokens,
                completion_tokens,
                prompt_cached_tokens,
                prompt_cache_creation_tokens,
                cached,
                remote,
                start,
                end,
            ),
        ) in izip!(
            block_ids,
            block_root_span_ids,
            block_span_ids,
            block_is_root,
            block_xact_id,
            block_pagination_key,
            block_created,
            block_span_name,
            block_span_type,
            block_span_purpose,
            block_span_generation,
            block_model,
            block_prompt_tokens,
            block_completion_tokens,
            block_prompt_cached_tokens,
            block_prompt_cache_creation_tokens,
            block_cached_field,
            block_remote_field,
            block_start_field,
            block_end_field,
        )
        .enumerate()
        {
            let id_entry = self
                .score_aggs
                .entry(root_span_id.clone())
                .or_insert_with(|| SummarySegmentAggBucket::new(self.score_fields.len()));

            accumulate_metrics(
                id_entry,
                span_name.as_ref().map(|s| s.as_str()),
                span_type.as_ref().map(|s| s.as_str()),
                span_purpose.as_ref().map(|s| s.as_str()),
                span_generation.as_ref().map(|s| s.as_str()),
                model.as_ref().map(|s| s.as_str()),
                prompt_tokens,
                completion_tokens,
                prompt_cached_tokens,
                prompt_cache_creation_tokens,
                cached,
                remote,
                start,
                end,
                self.model_costs.as_ref().as_ref(),
            )?;

            if is_root {
                id_entry.root_doc = Some(RootDoc::Address(DocAddress {
                    segment_ord: self.segment_ord,
                    doc_id: docs[id_idx],
                }));
                id_entry.root_fields = Some(RootFields {
                    id,
                    span_id,
                    root_span_id,
                    span_name,
                    span_type,
                    generation: span_generation,
                    remote,
                    _xact_id: xact_id,
                    _pagination_key: pagination_key,
                    created: CastInto::<String>::cast(&created).map_err(|e| {
                        std::io::Error::new(std::io::ErrorKind::InvalidData, e.to_string())
                    })?,
                });
            }

            for (score_idx, all_scores) in block_scores.iter().enumerate() {
                match &all_scores[id_idx] {
                    Some(OwnedValue::F64(n)) => id_entry.scores[score_idx].add_value(*n),
                    None | Some(OwnedValue::Null) => {}
                    o => {
                        return Err(std::io::Error::new(
                            std::io::ErrorKind::InvalidData,
                            format!("Score value is not a number: {:?}", o),
                        ));
                    }
                }
            }
        }
        Ok(())
    }
}

fn accumulate_metrics(
    id_entry: &mut SummarySegmentAggBucket,
    span_name: Option<&str>,
    span_type: Option<&str>,
    span_purpose: Option<&str>,
    _span_generation: Option<&str>,
    model: Option<&str>,
    prompt_tokens: Option<u64>,
    completion_tokens: Option<u64>,
    prompt_cached_tokens: Option<u64>,
    prompt_cache_creation_tokens: Option<u64>,
    cached: Option<u64>,
    _remote: Option<u64>,
    start: Option<f64>,
    end: Option<f64>,
    model_costs: Option<&ModelCostsMap>,
) -> std::io::Result<()> {
    let span_purpose = span_purpose.unwrap_or("default");
    if span_purpose != "scorer" {
        let prompt_tokens = prompt_tokens;
        let completion_tokens = completion_tokens;
        let prompt_cached_tokens = prompt_cached_tokens;
        let prompt_cache_creation_tokens = prompt_cache_creation_tokens;

        let (prompt_cost, completion_cost) = compute_model_cost(
            model,
            model_costs,
            prompt_tokens,
            completion_tokens,
            prompt_cached_tokens,
            prompt_cache_creation_tokens,
        );

        id_entry.metrics.total_prompt_tokens =
            coalesce_add(id_entry.metrics.total_prompt_tokens, prompt_tokens);
        id_entry.metrics.total_completion_tokens =
            coalesce_add(id_entry.metrics.total_completion_tokens, completion_tokens);
        id_entry.metrics.total_prompt_cached_tokens = coalesce_add(
            id_entry.metrics.total_prompt_cached_tokens,
            prompt_cached_tokens,
        );
        id_entry.metrics.total_prompt_cache_creation_tokens = coalesce_add(
            id_entry.metrics.total_prompt_cache_creation_tokens,
            prompt_cache_creation_tokens,
        );

        id_entry.metrics.total_prompt_cost =
            coalesce_add(id_entry.metrics.total_prompt_cost, prompt_cost);
        id_entry.metrics.total_completion_cost =
            coalesce_add(id_entry.metrics.total_completion_cost, completion_cost);
    }

    let start = match start {
        Some(n) => {
            id_entry.metrics.first_start = match id_entry.metrics.first_start {
                Some(f) => Some(f.min(n)),
                None => Some(n),
            };
            Some(n)
        }
        _ => None,
    };
    let end = match end {
        Some(n) => {
            id_entry.metrics.last_end = match id_entry.metrics.last_end {
                Some(f) => Some(f.max(n)),
                None => Some(n),
            };
            Some(n)
        }
        _ => None,
    };
    let duration = match (start, end) {
        (Some(s), Some(e)) => {
            let duration = e - s;
            id_entry.metrics.max_duration = match id_entry.metrics.max_duration {
                Some(d) => Some(d.max(duration)),
                None => Some(duration),
            };
            Some(duration)
        }
        _ => None,
    };

    if span_name.is_some() && span_name.unwrap() == "task" {
        id_entry.metrics.max_task_duration = match (duration, id_entry.metrics.max_task_duration) {
            (Some(duration), Some(d)) => Some(d.max(duration)),
            (Some(duration), None) => Some(duration),
            _ => None,
        };
    }

    let cached = match cached {
        Some(n) => Some(n > 0),
        _ => None,
    };

    if span_type.is_some() && span_type.unwrap() == "llm" && span_purpose != "scorer" {
        id_entry.metrics.all_cached = match (id_entry.metrics.all_cached, cached) {
            (Some(a), Some(b)) => Some(a && b),
            (Some(a), None) => Some(a),
            (None, Some(b)) => Some(b),
            (None, None) => None,
        };
        if let Some(duration) = duration {
            id_entry.metrics.sum_llm_duration.add_value(duration);
        }
    }

    Ok(())
}

#[inline]
fn compute_model_cost(
    model: Option<&str>,
    model_costs: Option<&ModelCostsMap>,
    prompt_tokens: Option<u64>,
    completion_tokens: Option<u64>,
    prompt_cached_tokens: Option<u64>,
    prompt_cache_creation_tokens: Option<u64>,
) -> (Option<f64>, Option<f64>) {
    let model = if let Some(m) = model {
        m
    } else {
        return (None, None);
    };
    let model_costs = if let Some(m) = model_costs {
        m
    } else {
        return (None, None);
    };
    let model_cost = if let Some(m) = model_costs.get(model) {
        m
    } else {
        return (None, None);
    };

    let prompt_cost = match prompt_tokens {
        Some(t) => {
            let uncached_prompt_tokens =
                t - prompt_cached_tokens.unwrap_or(0) - prompt_cache_creation_tokens.unwrap_or(0);
            let uncached_prompt_token_rate = model_cost.input_cost_per_mil_tokens.unwrap_or(0.0);
            let uncached_prompt_cost =
                uncached_prompt_token_rate * uncached_prompt_tokens as f64 / 1_000_000.0;
            let cache_read_cost = model_cost
                .input_cache_read_cost_per_mil_tokens
                .unwrap_or(uncached_prompt_token_rate)
                * prompt_cached_tokens.unwrap_or(0) as f64
                / 1_000_000.0;
            let cache_write_cost = model_cost
                .input_cache_write_cost_per_mil_tokens
                .unwrap_or(uncached_prompt_token_rate)
                * prompt_cache_creation_tokens.unwrap_or(0) as f64
                / 1_000_000.0;
            Some(uncached_prompt_cost + cache_read_cost + cache_write_cost)
        }
        None => None,
    };

    let completion_cost = completion_tokens
        .map(|t| model_cost.output_cost_per_mil_tokens.unwrap_or(0.0) * t as f64 / 1_000_000.0);
    (prompt_cost, completion_cost)
}

pub fn aggregate_realtime_summary(
    docs: Vec<Cow<Value>>,
    model_costs: Option<&ModelCostsMap>,
) -> Result<HashMap<String, SummaryFruit>> {
    // First, collect the distinct set of scores
    let mut all_scores = HashSet::new();
    for doc in &docs {
        let doc = doc.as_object().ok_or_else(|| {
            InterpreterError::InternalError("expected realtime doc to be an object".to_string())
        })?;
        let scores = match doc.get("scores") {
            Some(&Value::Object(ref scores)) => scores,
            Some(&Value::Null) | None => {
                continue;
            }
            _ => {
                return Err(InterpreterError::InternalError(
                    "expected scores to be an object".to_string(),
                ));
            }
        };
        all_scores.extend(scores.keys());
    }

    let all_scores = all_scores
        .into_iter()
        .map(|s| s.to_string())
        .collect::<Vec<_>>();
    let mut score_aggs = HashMap::new();

    let empty_map = Map::new();

    for base_doc in docs {
        let doc = base_doc.as_object().ok_or_else(|| {
            InterpreterError::InternalError("expected realtime doc to be an object".to_string())
        })?;

        let id = get_required_string_field(doc, ID_FIELD)?;
        let root_span_id = get_required_string_field(doc, ROOT_SPAN_ID_FIELD)?;
        let span_id = get_required_string_field(doc, SPAN_ID_FIELD)?;
        let is_root = get_required_bool_field(doc, IS_ROOT_FIELD)?;
        let xact_id = get_required_u64_field(doc, XACT_ID_FIELD)?;
        let pagination_key =
            PaginationKey::from_str(get_required_string_field(doc, PAGINATION_KEY_FIELD)?)?.0;
        let created = get_required_string_field(doc, CREATED_FIELD)?;
        let span_attributes = match doc.get("span_attributes") {
            Some(&Value::Object(ref span_attributes)) => span_attributes,
            Some(&Value::Null) | None => &empty_map,
            _ => {
                return Err(InterpreterError::InternalError(
                    "expected span_attributes to be an object".to_string(),
                ));
            }
        };
        let metadata = match doc.get("metadata") {
            Some(&Value::Object(ref metadata)) => metadata,
            Some(&Value::Null) | None => &empty_map,
            _ => {
                return Err(InterpreterError::InternalError(
                    "expected metrics to be an object".to_string(),
                ));
            }
        };
        let span_purpose = get_optional_string_field(span_attributes, "purpose")?;
        let span_type = get_optional_string_field(span_attributes, "type")?;
        let span_name = get_optional_string_field(span_attributes, "name")?;
        let span_generation = get_optional_string_field(span_attributes, "generation")?;

        let model = get_optional_string_field(metadata, "model")?;

        let metrics = match doc.get("metrics") {
            Some(&Value::Object(ref metrics)) => metrics,
            Some(&Value::Null) | None => &empty_map,
            _ => {
                return Err(InterpreterError::InternalError(
                    "expected metrics to be an object".to_string(),
                ));
            }
        };

        let prompt_tokens = get_optional_u64_field(metrics, "prompt_tokens")?;
        let completion_tokens = get_optional_u64_field(metrics, "completion_tokens")?;
        let prompt_cached_tokens = get_optional_u64_field(metrics, "prompt_cached_tokens")?;
        let prompt_cache_creation_tokens =
            get_optional_u64_field(metrics, "prompt_cache_creation_tokens")?;
        let cached = get_optional_u64_field(metrics, "cached")?;
        let remote = get_optional_u64_field(metrics, "remote")?;

        let start = get_optional_f64_field(metrics, "start")?;
        let end = get_optional_f64_field(metrics, "end")?;

        let id_entry = score_aggs
            .entry(root_span_id.to_string())
            .or_insert_with(|| SummarySegmentAggBucket::new(all_scores.len()));

        accumulate_metrics(
            id_entry,
            span_name,
            span_type,
            span_purpose,
            span_generation,
            model,
            prompt_tokens,
            completion_tokens,
            prompt_cached_tokens,
            prompt_cache_creation_tokens,
            cached,
            remote,
            start,
            end,
            model_costs,
        )
        .map_err(|e| InterpreterError::InternalError(e.to_string()))?;

        let scores = match doc.get("scores") {
            Some(&Value::Object(ref scores)) => scores,
            Some(&Value::Null) | None => &empty_map,
            _ => {
                return Err(InterpreterError::InternalError(
                    "expected scores to be an object".to_string(),
                ));
            }
        };

        for (score_idx, score) in all_scores.iter().enumerate() {
            let score_value = match scores.get(score) {
                Some(&Value::Number(ref n)) => n.as_f64(),
                _ => None,
            };
            if let Some(score_value) = score_value {
                id_entry.scores[score_idx].add_value(score_value);
            }
        }

        if is_root {
            id_entry.root_fields = Some(RootFields {
                id: id.to_string(),
                span_id: span_id.to_string(),
                root_span_id: root_span_id.to_string(),
                span_name: span_name.map(|s| s.to_string()),
                span_type: span_type.map(|s| s.to_string()),
                generation: span_generation.map(|s| s.to_string()),
                remote,
                _xact_id: xact_id,
                _pagination_key: pagination_key,
                created: created.to_string(),
            });
            id_entry.root_doc = Some(RootDoc::Doc(base_doc.into_owned()));
        }
    }

    let mut fruit = HashMap::new();
    for (root_span_id, agg) in score_aggs {
        fruit.insert(
            root_span_id,
            SummaryFruit {
                scores: all_scores
                    .iter()
                    .zip(agg.scores)
                    .map(|(s, v)| (s.clone(), v))
                    .collect(),
                root_fields: agg.root_fields,
                root_doc: agg.root_doc,
                metrics: agg.metrics,
            },
        );
    }

    Ok(fruit)
}

fn get_required_string_column(
    name: &'static str,
    column: &StrColumn,
    docs: &[u32],
) -> std::io::Result<Vec<String>> {
    let block = collect_column_for_doc_batch_str(column, docs)?;
    block
        .into_iter()
        .map(|id| {
            Ok(match id {
                Some(OwnedValue::Str(s)) => s,
                Some(_) => {
                    return Err(std::io::Error::new(
                        std::io::ErrorKind::InvalidData,
                        format!("{} field is not a string", name),
                    ))
                }
                None => {
                    return Err(std::io::Error::new(
                        std::io::ErrorKind::InvalidData,
                        format!("{} field is null", name),
                    ))
                }
            })
        })
        .collect::<Result<Vec<_>, _>>()
}

fn get_optional_string_column(
    name: &'static str,
    column: &Option<StrColumn>,
    docs: &[u32],
) -> std::io::Result<Vec<Option<String>>> {
    let column = match column {
        Some(c) => c,
        None => {
            return Ok(vec![None; docs.len()]);
        }
    };
    let block = collect_column_for_doc_batch_str(column, docs)?;
    block
        .into_iter()
        .map(|id| {
            Ok(match id {
                Some(OwnedValue::Str(s)) => Some(s),
                None => None,
                Some(_) => {
                    return Err(std::io::Error::new(
                        std::io::ErrorKind::InvalidData,
                        format!("{} field is not a string", name),
                    ))
                }
            })
        })
        .collect::<Result<Vec<_>, _>>()
}

fn get_nested_field(
    columnar: &Arc<ColumnarReader>,
    path: &str,
    column_type: Option<ColumnType>,
) -> tantivy::Result<Option<DynamicColumnHandle>> {
    let mut fields = get_columns_matching_prefix(columnar, path)?
        .into_iter()
        .filter(|(_, col)| {
            if let Some(column_type) = column_type {
                col.column_type() == column_type
            } else {
                true
            }
        });
    let next = fields.next();
    match next {
        Some((_, col)) => {
            if fields.next().is_some() {
                return Err(tantivy::TantivyError::SchemaError(format!(
                    "Expected 1 column for path {}, but received more.",
                    path,
                )));
            }
            Ok(Some(col))
        }
        None => Ok(None),
    }
}

fn must_open_str_column(col: DynamicColumnHandle) -> tantivy::Result<StrColumn> {
    match col.open()? {
        DynamicColumn::Str(column) => Ok(column),
        _ => Err(tantivy::TantivyError::SchemaError(format!(
            "Expected string column, got {}",
            col.column_type()
        ))),
    }
}

fn must_open_f64_column(name: &str, col: DynamicColumnHandle) -> tantivy::Result<Column<f64>> {
    let opened = col
        .open()
        .map_err(|e| tantivy::TantivyError::IoError(Arc::new(e)))?
        .coerce_numerical(NumericalType::F64)
        .ok_or_else(|| {
            tantivy::TantivyError::SchemaError(format!(
                "field {} is not a number ({:?})",
                name,
                col.column_type()
            ))
        })?;

    Ok(match opened {
        DynamicColumn::F64(column) => column,
        _ => {
            return Err(tantivy::TantivyError::SchemaError(format!(
                "field {} was not represented as an f64",
                name
            )))
        }
    })
}

// This is implemented in the same style as other mappings.
struct MapF64ToU64;
impl StrictlyMonotonicFn<f64, u64> for MapF64ToU64 {
    #[inline(always)]
    fn mapping(&self, inp: f64) -> u64 {
        inp as u64
    }
    #[inline(always)]
    fn inverse(&self, out: u64) -> f64 {
        out as f64
    }
}

// This is copied from Tantivy.
struct MapI64ToU64;
impl StrictlyMonotonicFn<i64, u64> for MapI64ToU64 {
    #[inline(always)]
    fn mapping(&self, inp: i64) -> u64 {
        inp as u64
    }
    #[inline(always)]
    fn inverse(&self, out: u64) -> i64 {
        out as i64
    }
}

fn must_open_u64_column(name: &str, col: DynamicColumnHandle) -> tantivy::Result<Column<u64>> {
    let opened = col
        .open()
        .map_err(|e| tantivy::TantivyError::IoError(Arc::new(e)))?;

    match opened {
        DynamicColumn::U64(column) => Ok(column),
        DynamicColumn::I64(column) => {
            // NOTE: This will underflow if the column contains negative values.
            Ok(Column {
                index: column.index,
                values: Arc::new(monotonic_map_column(column.values, MapI64ToU64)),
            })
        }
        DynamicColumn::F64(column) => Ok(Column {
            index: column.index,
            values: Arc::new(monotonic_map_column(column.values, MapF64ToU64)),
        }),
        _ => {
            return Err(tantivy::TantivyError::SchemaError(format!(
                "field {} is not a number ({:?})",
                name,
                col.column_type()
            )))
        }
    }
}

fn get_required_string_field<'a>(doc: &'a Map<String, Value>, name: &str) -> Result<&'a str> {
    let field = doc
        .get(name)
        .ok_or_else(|| InterpreterError::InternalError(format!("expected field {}", name)))?;
    Ok(field.as_str().ok_or_else(|| {
        InterpreterError::InternalError(format!("field {} is not a string in value", name))
    })?)
}

fn get_optional_string_field<'a>(
    doc: &'a Map<String, Value>,
    name: &str,
) -> Result<Option<&'a str>> {
    let field = match doc.get(name) {
        None | Some(Value::Null) => return Ok(None),
        Some(field) => field,
    };
    Ok(Some(field.as_str().ok_or_else(|| {
        InterpreterError::InternalError(format!("field {} is not a string in value", name))
    })?))
}

fn get_required_bool_field<'a>(doc: &'a Map<String, Value>, name: &str) -> Result<bool> {
    let field = doc
        .get(name)
        .ok_or_else(|| InterpreterError::InternalError(format!("expected field {}", name)))?;
    Ok(field.as_bool().ok_or_else(|| {
        InterpreterError::InternalError(format!("field {} is not a boolean in value", name))
    })?)
}

fn get_required_u64_field<'a>(doc: &'a Map<String, Value>, name: &str) -> Result<u64> {
    let field = doc
        .get(name)
        .ok_or_else(|| InterpreterError::InternalError(format!("expected field {}", name)))?;
    Ok(field.as_u64().ok_or_else(|| {
        InterpreterError::InternalError(format!("field {} is not a u64 in value", name))
    })?)
}

fn get_optional_u64_field<'a>(doc: &'a Map<String, Value>, name: &str) -> Result<Option<u64>> {
    let field = match doc.get(name) {
        None | Some(Value::Null) => return Ok(None),
        Some(field) => field,
    };
    Ok(Some(field.as_u64().ok_or_else(|| {
        InterpreterError::InternalError(format!("field {} is not a u64 in value", name))
    })?))
}

fn get_optional_f64_field<'a>(doc: &'a Map<String, Value>, name: &str) -> Result<Option<f64>> {
    let field = match doc.get(name) {
        None | Some(Value::Null) => return Ok(None),
        Some(field) => field,
    };
    Ok(Some(field.as_f64().ok_or_else(|| {
        InterpreterError::InternalError(format!("field {} is not a f64 in value", name))
    })?))
}

pub fn make_preview_field(value: Value, preview_length: usize) -> (Value, Option<Value>) {
    match value {
        Value::String(s) => (
            match truncate_if_needed(&s, preview_length) {
                Some(truncated) => truncated,
                None => Value::String(s),
            },
            None,
        ),
        Value::Null => (Value::Null, None),
        o => match preview_json(&o, preview_length) {
            Ok(JsonPreview::Full(_)) => (o, None),
            Ok(JsonPreview::Truncated(s)) => (Value::String(s), Some(o)),
            Err(_) => (Value::Null, Some(o)),
        },
    }
}

fn truncate_if_needed(s: &str, preview_length: usize) -> Option<Value> {
    if let Some((idx, _)) = s.char_indices().nth(preview_length) {
        // arbitrary number of chars
        Some(Value::String((s[..idx]).to_string() + "..."))
    } else {
        None
    }
}

#[inline]
fn coalesce_add<T>(a: Option<T>, b: Option<T>) -> Option<T>
where
    T: Add<Output = T> + Default,
{
    match (a, b) {
        (Some(a), Some(b)) => Some(a + b),
        (Some(a), None) => Some(a),
        (None, Some(b)) => Some(b),
        (None, None) => None,
    }
}

#[derive(Debug)]
pub enum JsonPreview {
    /// Whole value fitted in the limit; give the caller back
    /// the original value (no copy) and its serialised form.
    Full(String),

    /// We had to cut off at `limit` bytes; give the prefix.
    Truncated(String),
}

/// Stream-serialise `value` but stop after `limit` UTF-8 bytes.
///
/// * Never allocates more than `limit`+3.
/// * Uses the writer failure path to abort the serializer early.
pub fn preview_json<T>(value: &T, limit: usize) -> Result<JsonPreview, serde_json::Error>
where
    T: Serialize + Clone,
{
    let mut buf = String::with_capacity(limit.min(1024));
    let mut writer = LimitWriter {
        buf: &mut buf,
        remaining: limit,
    };

    match serde_json::to_writer(&mut writer, value) {
        Ok(()) => Ok(JsonPreview::Full(buf)), // fitted entirely
        Err(e) if e.is_io() => {
            buf.push_str("...");
            Ok(JsonPreview::Truncated(buf))
        }
        Err(e) => Err(e),
    }
}

struct LimitWriter<'a> {
    buf: &'a mut String,
    remaining: usize,
}

impl<'a> io::Write for LimitWriter<'a> {
    fn write(&mut self, data: &[u8]) -> io::Result<usize> {
        let mut take = data.len().min(self.remaining);
        match std::str::from_utf8(&data[..take]) {
            Ok(s) => self.buf.push_str(s),
            Err(e) => {
                take = e.valid_up_to(); // This helps ensure that we break when this occurs.
                self.buf
                    .push_str(std::str::from_utf8(&data[..take]).expect("incorrect valid_up_to"));
            }
        }
        self.remaining -= take;

        if take < data.len() {
            // We've hit the limit – abort further serialisation.
            Err(io::Error::new(io::ErrorKind::Other, "size limit reached"))
        } else {
            Ok(take)
        }
    }
    fn flush(&mut self) -> io::Result<()> {
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use serde_json::json;

    #[test]
    fn test_preview_json_small_value() {
        let value = json!({"key": "value"});
        let result = preview_json(&value, 100).unwrap();
        match result {
            JsonPreview::Full(s) => {
                assert_eq!(s, r#"{"key":"value"}"#);
            }
            JsonPreview::Truncated(_) => panic!("Expected Full, got Truncated"),
        }
    }

    #[test]
    fn test_preview_json_truncated() {
        let value = json!({"key": "a very long value that will be truncated"});
        let result = preview_json(&value, 10).unwrap();
        match result {
            JsonPreview::Full(_) => panic!("Expected Truncated, got Full"),
            JsonPreview::Truncated(s) => {
                assert_eq!(s, r#"{"key":"a ..."#);
            }
        }
    }

    #[test]
    fn test_preview_json_exact_limit() {
        let value = json!({"key": "value"});
        let json_str = r#"{"key":"value"}"#;
        let result = preview_json(&value, json_str.len()).unwrap();
        match result {
            JsonPreview::Full(s) => {
                assert_eq!(s, json_str);
            }
            JsonPreview::Truncated(_) => panic!("Expected Full, got Truncated"),
        }
    }

    #[test]
    fn test_preview_json_utf8_multibyte() {
        // Unicode characters that take multiple bytes
        let value = json!({"key": "こんにちは世界"}); // "Hello World" in Japanese
        let result = preview_json(&value, 15).unwrap();
        match result {
            JsonPreview::Full(_) => panic!("Expected Truncated, got Full"),
            JsonPreview::Truncated(s) => {
                // Should truncate in the middle of the Japanese text
                assert!(s.starts_with(r#"{"key":"こ"#));
                assert!(s.ends_with("..."));
            }
        }
    }

    #[test]
    fn test_preview_json_utf8_boundary() {
        // Create a string with a character at the boundary
        let value = json!({"key": "abc\u{1F600}"}); // "abc😀" - emoji takes 4 bytes

        // Test truncating right before the emoji
        let result = preview_json(&value, 9).unwrap(); // {"key":"a
        match result {
            JsonPreview::Truncated(s) => {
                assert_eq!(s, r#"{"key":"a..."#);
            }
            _ => panic!("Expected Truncated"),
        }

        // Test truncating in the middle of the emoji
        let result = preview_json(&value, 10).unwrap();
        match result {
            JsonPreview::Truncated(s) => {
                // Should not include partial UTF-8 sequence
                assert_eq!(s, r#"{"key":"ab..."#);
            }
            _ => panic!("Expected Truncated"),
        }
    }

    #[test]
    fn test_preview_json_nested_objects() {
        let value = json!({
            "nested": {
                "array": [1, 2, 3, 4, 5],
                "object": {"a": "b", "c": "d"}
            }
        });

        let result = preview_json(&value, 20).unwrap();
        match result {
            JsonPreview::Truncated(s) => {
                assert!(s.starts_with(r#"{"nested":{"array"#));
                assert!(s.ends_with("..."));
            }
            _ => panic!("Expected Truncated"),
        }
    }

    #[test]
    fn test_preview_json_empty_values() {
        // Empty object
        let value = json!({});
        let result = preview_json(&value, 10).unwrap();
        match result {
            JsonPreview::Full(s) => {
                assert_eq!(s, "{}");
            }
            _ => panic!("Expected Full"),
        }

        // Empty array
        let value = json!([]);
        let result = preview_json(&value, 10).unwrap();
        match result {
            JsonPreview::Full(s) => {
                assert_eq!(s, "[]");
            }
            _ => panic!("Expected Full"),
        }
    }

    #[test]
    fn test_preview_json_zero_limit() {
        let value = json!({"key": "value"});
        let result = preview_json(&value, 0).unwrap();
        match result {
            JsonPreview::Truncated(s) => {
                assert_eq!(s, "...");
            }
            _ => panic!("Expected Truncated"),
        }
    }
}
