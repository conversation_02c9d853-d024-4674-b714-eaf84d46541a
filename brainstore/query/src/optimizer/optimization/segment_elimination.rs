use std::{collections::HashSet, ops::Bound};

use btql::binder::ast::SortDirection;
use storage::segment_batches::{SegmentBatchingSortSpec, SegmentEliminationFilterSpec};
use tantivy::columnar::MonotonicallyMappableToU64;
use util::schema::TantivyField;

use crate::{
    interpreter::tantivy::expand::{ID_FIELD, ROOT_SPAN_ID_FIELD},
    optimizer::ast::{BooleanQueryOp, OptimizedTantivySearch, TermQuery},
};
use btql::typesystem::CastInto;

pub fn make_segment_batching_sort_spec(
    sort: &Option<(SortDirection, TantivyField)>,
) -> Option<SegmentBatchingSortSpec> {
    sort.as_ref().map(|(dir, field)| SegmentBatchingSortSpec {
        field: field.name.clone(),
        descending: matches!(dir, SortDirection::Desc),
    })
}

pub fn make_segment_elimination_filter_spec(
    filter: &OptimizedTantivySearch,
) -> Vec<SegmentEliminationFilterSpec> {
    match filter {
        OptimizedTantivySearch::RangeQuery {
            field,
            json_path,
            lower,
            upper,
            columnar,
            ..
        } => {
            if !json_path.is_empty() || !*columnar {
                // We could eventually support json (and would need to encode these paths into some simple
                // field name format).
                return vec![];
            }

            vec![SegmentEliminationFilterSpec::Range {
                field: field.name.clone(),
                min: map_bound_to_u64(&lower),
                max: map_bound_to_u64(&upper),
            }]
        }
        OptimizedTantivySearch::TermQuery(TermQuery { field, value, .. }) => {
            if field.name == ID_FIELD || field.name == ROOT_SPAN_ID_FIELD {
                let value: String = match value.cast() {
                    Ok(v) => v,
                    Err(_) => return vec![],
                };
                let value_filter = HashSet::from([value]);
                vec![if field.name == ID_FIELD {
                    SegmentEliminationFilterSpec::IdFilter {
                        ids: value_filter.clone(),
                        root_span_ids: HashSet::new(),
                    }
                } else {
                    SegmentEliminationFilterSpec::IdFilter {
                        root_span_ids: value_filter,
                        ids: HashSet::new(),
                    }
                }]
            } else {
                vec![]
            }
        }
        OptimizedTantivySearch::BooleanQuery(ops) => {
            let mut specs = vec![];
            for (op, filter) in ops {
                match op {
                    BooleanQueryOp::Must => {
                        specs.extend(make_segment_elimination_filter_spec(filter));
                    }
                    BooleanQueryOp::Should => {
                        if specs.is_empty() {
                            specs.extend(make_segment_elimination_filter_spec(filter));
                        } else if specs.len() == 1 {
                            let mut should_spec = make_segment_elimination_filter_spec(filter);
                            if should_spec.len() == 0 || should_spec.len() > 1 {
                                return vec![];
                            }

                            // If this is a should query that is is simply a bunch of ORs, then we can merge them
                            // into a single OR query.
                            let single_spec = should_spec.remove(0);
                            match (&mut specs[0], single_spec) {
                                (
                                    SegmentEliminationFilterSpec::IdFilter { ids, root_span_ids },
                                    SegmentEliminationFilterSpec::IdFilter {
                                        ids: ids2,
                                        root_span_ids: root_span_ids2,
                                    },
                                ) => {
                                    ids.extend(ids2);
                                    root_span_ids.extend(root_span_ids2);
                                }
                                _ => {
                                    return vec![];
                                }
                            }
                        } else {
                            return vec![];
                        }
                    }
                    BooleanQueryOp::MustNot => {
                        // NOTE: We could theoretically handle MustNot, but we don't need to for now.
                        return vec![];
                    }
                }
            }
            specs
        }
        _ => vec![],
    }
}

fn map_value_to_u64(value: &util::Value) -> Option<u64> {
    use util::Value;
    match value {
        Value::Number(n) => {
            if let Some(u) = n.as_u64() {
                Some(MonotonicallyMappableToU64::to_u64(u))
            } else if let Some(i) = n.as_i64() {
                Some(MonotonicallyMappableToU64::to_u64(i))
            } else if let Some(f) = n.as_f64() {
                Some(MonotonicallyMappableToU64::to_u64(f))
            } else {
                None
            }
        }
        Value::Bool(b) => Some(MonotonicallyMappableToU64::to_u64(*b)),
        Value::String(s) => match CastInto::<tantivy::DateTime>::cast(s) {
            Ok(dt) => Some(MonotonicallyMappableToU64::to_u64(dt)),
            Err(_) => None,
        },
        _ => None,
    }
}

fn map_bound_to_u64(bound: &Bound<util::Value>) -> Bound<u64> {
    match bound {
        Bound::Included(v) => map_value_to_u64(v)
            .map(Bound::Included)
            .unwrap_or(Bound::Unbounded),
        Bound::Excluded(v) => map_value_to_u64(v)
            .map(Bound::Excluded)
            .unwrap_or(Bound::Unbounded),
        Bound::Unbounded => Bound::Unbounded,
    }
}
