[{"error": null, "query": "/*!result -- This should return just one row (transaction id 3)\nlength == 1 and .[0]._xact_id == \"3\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "query": "/*!result -- This should return just one row (transaction id 2)\nlength == 1 and .[0]._xact_id == \"2\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAADAAE", "result_rows": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "query": "/*!result -- This should return just one row (transaction id 1)\nlength == 1 and .[0]._xact_id == \"1\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAACAAA", "result_rows": [{"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}], "skip": false}, {"error": null, "query": "/*!result -- This should return just one row (transaction id 1)\nlength == 1 and .[0]._xact_id == \"1\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAABAAI", "result_rows": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "skip": false}, {"error": null, "query": "/*!result -- This should return just one row (transaction id 1)\nlength == 1 and .[0]._xact_id == \"1\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAABAAE", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}], "skip": false}, {"error": null, "query": "-- Test the same thing, but ascending\n/*!result -- This should return just one row (transaction id 1)\nlength == 1 and .[0]._xact_id == \"1\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key asc", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}], "skip": false}, {"error": null, "query": "/*!result -- This should return just one row (transaction id 1)\nlength == 1 and .[0]._xact_id == \"1\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key asc | cursor: AAAAAAABAAA", "result_rows": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}], "skip": false}, {"error": null, "query": "/*!result -- This should return just one row (transaction id 1)\nlength == 1 and .[0]._xact_id == \"1\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key asc | cursor: AAAAAAABAAE", "result_rows": [{"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}], "skip": false}, {"error": null, "query": "/*!result -- This should return just one row (transaction id 1)\nlength == 1 and .[0]._xact_id == \"2\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key asc | cursor: AAAAAAABAAI", "result_rows": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "query": "/*!result -- This should return just one row (transaction id 1)\nlength == 1 and .[0]._xact_id == \"3\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key asc | cursor: AAAAAAACAAA", "result_rows": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "query": "-- We're done paginating so this should return nothing\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _pagination_key desc | cursor: AAAAAAABAAA", "result_rows": [], "skip": false}, {"error": null, "query": "from: dataset('singleton') spans | select: * | filter: s='bar baz' | sort: _pagination_key desc", "result_rows": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}, {"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "query": "-- This should return nothing\nfrom: dataset('singleton') spans | select: * | filter: s='bar baz' | sort: _pagination_key desc | cursor: AAAAAAACAAA", "result_rows": [], "skip": false}, {"error": null, "query": "-- Filter on a pagination key\nfrom: dataset('singleton') | select: * | filter: _pagination_key = 'p00000000000000065536'", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: * | filter: _pagination_key != 'p00000000000000065536'", "result_rows": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}, {"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: * | filter: _pagination_key > 'p00000000000000065536'", "result_rows": [{"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}, {"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}, {"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: * | filter: _pagination_key < 'p00000000000000065536'", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Schema must be an array", "query": "-- These produce different results with pushdown vs. not, because pushed down, they match\n-- the underlying u64 representation of the pagination key. We could theoretically solve\n-- this but it's not worth it.\n-- from: dataset('singleton') | select: * | filter: _pagination_key = '00000000000000065536'", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Schema must be an array", "query": "-- from: dataset('singleton') | select: * | filter: _pagination_key != '00000000000000065536'", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Schema must be an array", "query": "-- from: dataset('singleton') | select: * | filter: _pagination_key > '00000000000000065536'", "result_rows": [], "skip": false}, {"error": "btql bind failed: Error: Schema must be an array", "query": "-- from: dataset('singleton') | select: * | filter: _pagination_key < '00000000000000065536'", "result_rows": [], "skip": false}, {"error": null, "query": "/*!result --\nlength == 1 and .[0]._xact_id == \"3\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _xact_id desc", "result_rows": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "query": "/*!result --\nlength == 1 and .[0]._xact_id == \"2\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _xact_id desc | cursor: AAAAAAAAAAM", "result_rows": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "query": "/*!result -- This should return three rows, even though limit is 1, because there are three rows with the same xact_id\nlength == 3\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _xact_id desc | cursor: AAAAAAAAAAI", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}, {"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}], "skip": false}, {"error": null, "query": "/*!result --\nlength == 3\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _xact_id asc", "result_rows": [{"_pagination_key": "p00000000000000065536", "_xact_id": "1", "id": "0", "root_span_id": "0", "s": "foo", "span_id": "0"}, {"_pagination_key": "p00000000000000065537", "_xact_id": "1", "id": "1", "root_span_id": "1", "s": "foo", "span_id": "1"}, {"_pagination_key": "p00000000000000065538", "_xact_id": "1", "id": "2", "root_span_id": "1", "s": "baz", "span_id": "2", "span_parents": ["1"]}], "skip": false}, {"error": null, "query": "/*!result --\nlength == 1 and .[0]._xact_id == \"2\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _xact_id asc | cursor: AAAAAAAAAAE", "result_rows": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}, {"error": null, "query": "/*!result --\nlength == 1 and .[0]._xact_id == \"3\"\n*/\nfrom: dataset('singleton') | select: * | limit: 1 | sort: _xact_id asc | cursor: AAAAAAAAAAI", "result_rows": [{"_pagination_key": "p00000000000000196609", "_xact_id": "3", "id": "3", "root_span_id": "1", "s": "bar baz", "span_id": "3", "span_parents": ["2"]}], "skip": false}, {"error": null, "query": "from: dataset('singleton') | select: * | filter: root_span_id='4'", "result_rows": [{"_pagination_key": "p00000000000000131072", "_xact_id": "2", "id": "4", "root_span_id": "4", "s": "bar baz", "span_id": "4"}], "skip": false}]