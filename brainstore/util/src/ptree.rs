use std::{
    collections::{HashMap, HashSet},
    sync::Arc,
};

pub use ptree::{print_tree, write_tree, TreeBuilder};
use ptree::{write_tree_with, PrintConfig};
pub use ptree_derive::MakePTree;
use serde_json::Value;

pub trait MakePTree {
    fn label(&self) -> String;
    fn make_ptree(&self, builder: &mut TreeBuilder);

    fn passthrough(&self) -> bool {
        false
    }

    fn should_skip(&self) -> bool {
        false
    }

    fn add_child<T: MakePTree>(&self, builder: &mut TreeBuilder, child: &T) {
        if child.passthrough() {
            child.make_ptree(builder);
        } else {
            builder.begin_child(child.label());
            child.make_ptree(builder);
            builder.end_child();
        }
    }

    fn format_tree(&self) -> std::io::Result<String> {
        let mut builder = TreeBuilder::new(self.label());
        self.make_ptree(&mut builder);

        let tree = builder.build();

        let mut string_writer = Vec::new();
        write_tree_with(
            &tree,
            &mut string_writer,
            &PrintConfig {
                indent: 3,
                ..Default::default()
            },
        )?;
        Ok(String::from_utf8(string_writer.to_vec())
            .unwrap_or_else(|_| "<failed to serialize>".to_string()))
    }
}

impl<T> MakePTree for Box<T>
where
    T: MakePTree,
{
    fn label(&self) -> String {
        self.as_ref().label()
    }
    fn passthrough(&self) -> bool {
        self.as_ref().passthrough()
    }

    fn should_skip(&self) -> bool {
        self.as_ref().should_skip()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.as_ref().make_ptree(builder);
    }
}

impl<T> MakePTree for Arc<T>
where
    T: MakePTree,
{
    fn label(&self) -> String {
        self.as_ref().label()
    }

    fn passthrough(&self) -> bool {
        self.as_ref().passthrough()
    }

    fn should_skip(&self) -> bool {
        self.as_ref().should_skip()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.as_ref().make_ptree(builder);
    }
}

impl<T> MakePTree for Option<T>
where
    T: MakePTree,
{
    fn label(&self) -> String {
        match self {
            Some(value) => value.label(),
            None => "None".to_string(),
        }
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        match self {
            Some(value) => value.make_ptree(builder),
            None => {}
        }
    }

    fn should_skip(&self) -> bool {
        match self {
            Some(value) => value.should_skip(),
            None => true,
        }
    }

    fn passthrough(&self) -> bool {
        match self {
            Some(value) => value.passthrough(),
            None => true,
        }
    }
}

impl<T> MakePTree for Vec<T>
where
    T: MakePTree,
{
    fn label(&self) -> String {
        "Vec".to_string()
    }

    fn passthrough(&self) -> bool {
        true
    }

    fn should_skip(&self) -> bool {
        self.is_empty()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        for item in self {
            self.add_child(builder, item);
        }
    }
}

impl<T> MakePTree for [T]
where
    T: MakePTree,
{
    fn label(&self) -> String {
        "Array".to_string()
    }

    fn passthrough(&self) -> bool {
        true
    }

    fn should_skip(&self) -> bool {
        self.is_empty()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        for item in self {
            self.add_child(builder, item);
        }
    }
}

impl<T> MakePTree for HashMap<String, T>
where
    T: MakePTree,
{
    fn label(&self) -> String {
        "HashMap".to_string()
    }

    fn passthrough(&self) -> bool {
        true
    }

    fn should_skip(&self) -> bool {
        self.is_empty()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        for (key, value) in self {
            builder.begin_child(key.clone());
            self.add_child(builder, value);
            builder.end_child();
        }
    }
}

impl<T> MakePTree for HashSet<T>
where
    T: MakePTree,
{
    fn label(&self) -> String {
        "HashSet".to_string()
    }

    fn passthrough(&self) -> bool {
        true
    }

    fn should_skip(&self) -> bool {
        self.is_empty()
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        for item in self {
            self.add_child(builder, item);
        }
    }
}

impl MakePTree for usize {
    fn label(&self) -> String {
        format!("{}", self)
    }

    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}

impl MakePTree for u64 {
    fn label(&self) -> String {
        format!("{}", self)
    }

    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}

impl MakePTree for String {
    fn label(&self) -> String {
        format!("{}", self)
    }

    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}

impl MakePTree for &str {
    fn label(&self) -> String {
        format!("{}", self)
    }

    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}

impl MakePTree for bool {
    fn label(&self) -> String {
        format!("{}", self)
    }

    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}

impl<T: std::fmt::Debug, U: MakePTree> MakePTree for (T, U) {
    fn label(&self) -> String {
        format!("{:?}", self.0)
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, &self.1);
    }
}

impl<T: std::fmt::Debug, U: MakePTree, V: MakePTree> MakePTree for (T, U, V) {
    fn label(&self) -> String {
        format!("{:?}", self.0)
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        self.add_child(builder, &self.1);
        self.add_child(builder, &self.2);
    }
}

impl MakePTree for Value {
    fn label(&self) -> String {
        format!("{}", self)
    }

    fn make_ptree(&self, _builder: &mut TreeBuilder) {}
}

impl<T: MakePTree> MakePTree for std::ops::Bound<T> {
    fn label(&self) -> String {
        use std::ops::Bound::*;
        match self {
            Included(value) => format!("Included: {}", value.label()),
            Excluded(value) => format!("Excluded: {}", value.label()),
            Unbounded => "Unbounded".to_string(),
        }
    }

    fn make_ptree(&self, builder: &mut TreeBuilder) {
        use std::ops::Bound::*;
        match self {
            Included(value) => value.make_ptree(builder),
            Excluded(value) => value.make_ptree(builder),
            Unbounded => {}
        }
    }
}
