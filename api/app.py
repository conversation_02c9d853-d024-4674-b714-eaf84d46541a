import os
import sys

from chalice import Chalice, Rate

app = Chalice(app_name="api")

IS_LOCAL = (len(sys.argv) > 1 and sys.argv[1] == "local") or os.environ.get("CHALICE_LOCAL_ENV")

# This setting controls which Content-Type settings can contain binary data. See
# https://aws.github.io/chalice/api.html?highlight=binary#APIGateway.binary_types
# and
# https://docs.aws.amazon.com/apigateway/latest/developerguide/api-gateway-payload-encodings-workflow.html.
# These settings are only relevant when deploying to AWS. When running locally, we
# don't want these settings to interfere with response validation.
if not IS_LOCAL:
    app.api.binary_types = [
        "*/*",
        "application/json",
        "application/octet-stream",
        "application/x-tar",
        "application/zip",
        "audio/basic",
        "audio/ogg",
        "audio/mp4",
        "audio/mpeg",
        "audio/wav",
        "audio/webm",
        "image/png",
        "image/jpg",
        "image/jpeg",
        "image/gif",
        "video/ogg",
        "video/mpeg",
        "video/webm",
    ]

# should match the version in app/ui/api-version/api-version.ts and
# api-ts/src/api.ts. Though to avoid annoying users, only bump up the version in
# the UI if this backend change enables important new functionality or fixes
# bugs.
VERSION = "0.0.54"

REQUEST_KWARGS = {"content_types": ["application/json", "text/plain"]}


def proxy_to_js_api(*args, **kwargs):
    pass


def js_api(func):
    return proxy_to_js_api


@app.route("/", **REQUEST_KWARGS)
@js_api
def index():
    pass


@app.route("/ping", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def ping():
    pass


@app.route("/api/{object_type}", methods=["OPTIONS", "GET", "POST"], **REQUEST_KWARGS)
@js_api
def control_plane_proxy_1(object_type):
    pass


@app.route("/api/{object_type}/{action}", methods=["OPTIONS", "GET", "POST"], **REQUEST_KWARGS)
@js_api
def control_plane_proxy_2(object_type, action):
    pass


@app.route("/logs", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def log():
    pass


# For compatibility reasons, we cannot change the return shape of /logs
@app.route("/logs2", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def log2():
    pass


# This endpoint expects data in a different format than `/logs2`:
# `{ "rows": <data>, "api_version": <api_version> }`
# It will error if `api_version` is not provided, or if it is higher
# than the internal `API_VERSION`.
@app.route("/logs3", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def log3():
    pass


@app.route("/btql", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def btql():
    pass


@app.route("/insert-functions", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def insert_functions():
    pass


@app.route("/experiment-comparison2", methods=["OPTIONS", "GET", "POST"], **REQUEST_KWARGS)
@js_api
def experiment_comparison2():
    pass


@app.route("/dataset-summary", methods=["OPTIONS", "GET", "POST"], **REQUEST_KWARGS)
@js_api
def dataset_summary():
    pass


@app.route("/xact-id", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def xact_id():
    pass


@app.route("/broadcast-key", methods=["OPTIONS", "GET", "POST"], **REQUEST_KWARGS)
@js_api
def broadcast_key():
    pass


@app.route("/version", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def get_version():
    pass


@app.route("/version-" + VERSION, methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def get_version_nonce():
    pass


@app.route("/proxy-url", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def get_proxy_url():
    pass


@app.route("/realtime-url", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def get_realtime_url():
    pass


@app.route("/db-health", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def db_health():
    pass


@app.route("/migration-version", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def migration_version():
    pass


@app.route("/migration-status", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def migration_status():
    pass


@app.route("/clickhouse/etl-status", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def clickhouse_etl_status():
    pass


@app.route("/clickhouse/run-etl", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def clickhouse_run_etl():
    pass


@app.schedule(Rate(1, unit=Rate.MINUTES))
def clickhouse_catchup_etl(event):
    pass


@app.schedule(Rate(10, unit=Rate.MINUTES))
def automation_cron(event):
    pass


@app.schedule(Rate(5, unit=Rate.MINUTES))
def billing_cron(event):
    pass


@app.route("/flush-object-cache", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def flush_object_cache():
    pass


@app.route("/flush-org-object-cache", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def flush_org_object_cache():
    pass


@app.route(
    "/function-env/{object_type}/{object_id}",
    methods=["OPTIONS", "GET", "POST", "PUT", "DELETE", "PATCH"],
    **REQUEST_KWARGS,
)
@js_api
def function_env(object_type, object_id):
    pass


@app.route("/attachment", methods=["OPTIONS", "GET", "POST"], **REQUEST_KWARGS)
@js_api
def attachment():
    pass


@app.route("/attachment/status", methods=["OPTIONS", "GET", "POST"], **REQUEST_KWARGS)
@js_api
def attachment_status():
    pass


@app.route("/test-automation", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def test_automation():
    pass


@app.route("/service-token/upsert", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def service_token_upsert():
    pass


@app.route("/service-token/{name}", methods=["OPTIONS", "HEAD", "GET"], **REQUEST_KWARGS)
@js_api
def service_token_get(name):
    pass


# Preserved endpoints for legacy SDKs.


@app.route("/crud/base_experiments", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def crud_base_experiments():
    pass


###### Begin: Rest API v1 ######

V1_REQUEST_KWARGS = {"cors": app.api.cors, "content_types": ["application/json"]}


@app.route("/v1", methods=["OPTIONS", "GET"], **V1_REQUEST_KWARGS)
@js_api
def v1_index():
    pass


@app.route("/v1/prompt", methods=["OPTIONS", "GET", "POST", "PUT"], **V1_REQUEST_KWARGS)
@js_api
def v1_prompt():
    pass


@app.route("/v1/function", methods=["OPTIONS", "GET", "POST", "PUT"], **V1_REQUEST_KWARGS)
@js_api
def v1_function():
    pass


@app.route("/v1/{object_type}", methods=["OPTIONS", "GET", "POST", "PUT"], **V1_REQUEST_KWARGS)
@js_api
def v1_object_type(object_type):
    pass


@app.route("/v1/prompt/{id}", methods=["OPTIONS", "GET", "DELETE", "PATCH"], **V1_REQUEST_KWARGS)
@js_api
def v1_prompt_id(id):
    pass


@app.route("/v1/function/{id}", methods=["OPTIONS", "GET", "DELETE", "PATCH"], **V1_REQUEST_KWARGS)
@js_api
def v1_function_id(id):
    pass


@app.route("/v1/{object_type}/{id}", methods=["OPTIONS", "GET", "DELETE", "PATCH", "POST"], **V1_REQUEST_KWARGS)
@js_api
def v1_object_type_id(object_type, id):
    pass


@app.route("/v1/{object_type}/{id}/insert", methods=["OPTIONS", "POST"], **V1_REQUEST_KWARGS)
@js_api
def v1_object_type_id_insert(object_type, id):
    pass


@app.route("/v1/{object_type}/{id}/fetch", methods=["OPTIONS", "GET", "POST"], **V1_REQUEST_KWARGS)
@js_api
def v1_object_type_id_fetch(object_type, id):
    pass


@app.route("/v1/{object_type}/{id}/feedback", methods=["OPTIONS", "POST"], **V1_REQUEST_KWARGS)
@js_api
def v1_object_type_id_feedback(object_type, id):
    pass


@app.route("/v1/{object_type}/{id}/summarize", methods=["OPTIONS", "GET"], **V1_REQUEST_KWARGS)
@js_api
def v1_object_type_id_summarize(object_type, id):
    pass


@app.route("/v1/insert", methods=["OPTIONS", "POST"], **V1_REQUEST_KWARGS)
@js_api
def v1_insert():
    pass


###### End: Rest API v1 ######


OTEL_REQUEST_KWARGS = {"content_types": ["application/json", "application/x-protobuf"]}


@app.route("/otel/v1/traces", methods=["OPTIONS", "POST"], **OTEL_REQUEST_KWARGS)
@js_api
def otel_v1_traces():
    pass


@app.route("/brainstore/version", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def brainstore_version():
    pass


@app.route("/brainstore/status", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def brainstore_status():
    pass


@app.route("/brainstore/backfill/status", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def brainstore_backfill_status():
    pass


@app.route("/brainstore/backfill/status/active", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def brainstore_backfill_status_active():
    pass


@app.route("/brainstore/backfill/status/failed", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def brainstore_backfill_status_failed():
    pass


@app.route("/brainstore/backfill/status/project/{project_id}", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def brainstore_backfill_status_project(project_id):
    pass


@app.route("/brainstore/backfill/status/object/{object_id}", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def brainstore_backfill_status_object(object_id):
    pass


@app.route("/brainstore/segment/{segment_id}", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def brainstore_segment_object_id(segment_id):
    pass


@app.route("/brainstore/backfill/track", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def brainstore_backfill_track():
    pass


@app.route("/brainstore/backfill/enable", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def brainstore_backfill_enable():
    pass


@app.route("/brainstore/backfill/delete", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def brainstore_backfill_delete():
    pass


@app.route("/brainstore/backfill/optimize", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def brainstore_backfill_optimize():
    pass


@app.route("/brainstore/backfill/run", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def brainstore_backfill_run():
    pass


@app.route("/brainstore/vacuum/status", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def brainstore_vacuum_status():
    pass


@app.route("/brainstore/vacuum/reset_state", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def brainstore_vacuum_reset_state():
    pass


@app.route("/brainstore/vacuum/object/{object_id}", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def brainstore_vacuum_object(object_id):
    pass


@app.route("/automation/flush-cache", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def brainstore_automation_flush_cache():
    pass


@app.route("/automation/cloud-identity", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def brainstore_automation_cloud_identity():
    pass


@app.route("/automation/cron", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def brainstore_automation_cron():
    pass


@app.route("/automation/cron/{id}/status", methods=["OPTIONS", "GET"], **REQUEST_KWARGS)
@js_api
def brainstore_automation_cron_status(id):
    pass


@app.route("/automation/cron/{id}/run", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def brainstore_automation_cron_run(id):
    pass


@app.route("/automation/cron/{id}/reset", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def brainstore_automation_cron_reset(id):
    pass


@app.route("/billing/status", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def billing_status():
    pass


@app.route("/billing/refresh", methods=["OPTIONS", "POST"], **REQUEST_KWARGS)
@js_api
def billing_refresh():
    pass
