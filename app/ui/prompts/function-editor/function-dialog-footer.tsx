import { CopyAIProxyCode } from "#/app/app/[org]/p/[project]/prompts/copy-ai-proxy-code";
import { Button, buttonVariants } from "#/ui/button";
import { DialogFooter } from "#/ui/dialog";
import {
  type Message,
  type FunctionObjectType,
} from "@braintrust/core/typespecs";
import { AlertTriangle, Code, Copy, Trash2 } from "lucide-react";
import {
  type Dispatch,
  type RefObject,
  type SetStateAction,
  useEffect,
  useMemo,
  useRef,
  useState,
} from "react";
import { useHotkeys } from "react-hotkeys-hook";
import {
  type SyncedPlaygroundBlock,
  type UIFunction,
} from "#/ui/prompts/schema";
import { type TransactionId } from "@braintrust/core";
import { useSyncedPrompts } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { atom, useAtomValue } from "jotai";
import isEqual from "lodash.isequal";
import { TransactionIdField } from "@braintrust/local/query";
import { cn } from "#/utils/classnames";
import Link from "next/link";
import { getSavedPromptLink } from "#/app/app/[org]/prompt/[prompt]/getPromptLink";
import { type FunctionEditorContext, type Mode } from "./types";
import { CreatePlaygroundButton } from "./create-playground";
import { useRouter } from "next/navigation";

export function FunctionDialogFooter({
  mode,
  type,
  getOutputPrompt,
  isSlugTouchedRef,
  isDirtyRef,
  codeTouched,
  error,
  setError,
  hasLintErrors,
  initialDirtyFunctionComparisonBase,
  orgName,
  projectId,
  projectName,
  extraMessages = [],
  promptName,
  dataEditorValue,
  context,
}: {
  mode: Mode;
  type: FunctionObjectType;
  getOutputPrompt: (promptState: SyncedPlaygroundBlock) => UIFunction;
  isSlugTouchedRef: RefObject<boolean>;
  isDirtyRef: RefObject<boolean>;
  codeTouched: number;
  error: string | null;
  setError: Dispatch<SetStateAction<string | null>>;
  hasLintErrors: boolean;
  initialDirtyFunctionComparisonBase: UIFunction | null;
  orgName: string;
  projectId: string;
  projectName: string;
  extraMessages?: Message[];
  promptName?: string;
  dataEditorValue: Record<string, unknown>;
  context: FunctionEditorContext;
}) {
  const { sortedSyncedPromptsAtom_ROOT } = useSyncedPrompts();
  const prompt = useAtomValue(
    useMemo(
      () => atom((get) => get(sortedSyncedPromptsAtom_ROOT)[0]),
      [sortedSyncedPromptsAtom_ROOT],
    ),
  );
  const [updating, setUpdating] = useState(false);

  return (
    <DialogFooter className="flex-none items-center border-t px-4 py-3">
      <div className="flex flex-1 items-center gap-2">
        {type === "prompt" && prompt.prompt_data && (
          <CopyAIProxyCode promptData={prompt.prompt_data}>
            <Button size="xs" variant="ghost" Icon={Code}>
              Code snippet
            </Button>
          </CopyAIProxyCode>
        )}
        {mode.type === "update" && !!mode.onDuplicate && (
          <Button
            size="xs"
            variant="ghost"
            onClick={(e) => {
              e.preventDefault();
              const outputPrompt = getOutputPrompt(prompt);
              mode.onDuplicate?.(outputPrompt);
            }}
            Icon={Copy}
          >
            Duplicate
          </Button>
        )}
        {mode.type === "update" && !!mode.onDelete && (
          <Button
            Icon={Trash2}
            size="xs"
            variant="ghost"
            onClick={(e) => {
              e.preventDefault();
              mode.onDelete?.();
            }}
            disabled={updating}
          >
            Delete
          </Button>
        )}
        {mode.type !== "create" && context !== "playground" && (
          <CreatePlaygroundButton
            orgName={orgName}
            projectName={projectName}
            type={type}
            inputData={dataEditorValue}
            extraMessages={extraMessages}
            prompt={prompt}
            promptName={promptName}
            projectId={projectId}
            mode={mode}
            getOutputPrompt={getOutputPrompt}
          />
        )}
      </div>
      {error && (
        <div className="flex items-center gap-1.5 px-2 text-xs font-medium text-bad-700">
          <AlertTriangle className="size-3 flex-none" />
          {error}
        </div>
      )}
      {
        // TODO: support linking to scorers in other projects
        mode.type === "view_saved" &&
          projectId === initialDirtyFunctionComparisonBase?.project_id && (
            <Link
              href={getSavedPromptLink({
                orgName,
                projectSlug: projectName,
                promptId: prompt.id,
                promptVersion: prompt[TransactionIdField],
                type,
              })}
              className={cn(
                buttonVariants({
                  size: "xs",
                }),
              )}
            >
              Edit in library
            </Link>
          )
      }
      {mode.type !== "view_saved" && (
        <SubmitButton
          mode={mode}
          type={type}
          getOutputPrompt={getOutputPrompt}
          prompt={prompt}
          isSlugTouchedRef={isSlugTouchedRef}
          codeTouched={codeTouched}
          setError={setError}
          upsert={mode.upsert}
          hasLintErrors={hasLintErrors}
          initialDirtyFunctionComparisonBase={
            initialDirtyFunctionComparisonBase
          }
          updating={updating}
          setUpdating={setUpdating}
          isDirtyRef={isDirtyRef}
          orgName={orgName}
          projectName={projectName}
        />
      )}
    </DialogFooter>
  );
}

function SubmitButton({
  mode,
  type,
  getOutputPrompt,
  prompt,
  isSlugTouchedRef,
  codeTouched,
  isDirtyRef,
  setError,
  upsert,
  hasLintErrors,
  initialDirtyFunctionComparisonBase,
  updating,
  setUpdating,
  orgName,
  projectName,
}: {
  mode: Mode;
  type: FunctionObjectType;
  getOutputPrompt: (promptState: SyncedPlaygroundBlock) => UIFunction;
  prompt: SyncedPlaygroundBlock;
  isSlugTouchedRef: RefObject<boolean>;
  codeTouched: number;
  isDirtyRef: RefObject<boolean>;
  setError: Dispatch<SetStateAction<string | null>>;
  upsert: (
    prompt: UIFunction,
    isSlugTouchedRef: boolean,
  ) => Promise<TransactionId | null>;
  hasLintErrors: boolean;
  initialDirtyFunctionComparisonBase: UIFunction | null;
  updating: boolean;
  setUpdating: Dispatch<SetStateAction<boolean>>;
  orgName: string;
  projectName: string;
}) {
  const saveButtonRef = useRef<HTMLButtonElement>(null);
  useHotkeys(
    "Mod+S",
    () => {
      saveButtonRef.current?.click();
    },
    {
      enableOnFormTags: true,
      enableOnContentEditable: true, // codemirror is contenteditable
      preventDefault: true,
    },
  );

  const [dirtyFunctionComparisonBase, setDirtyFunctionComparisonBase] =
    useState<UIFunction | null>(initialDirtyFunctionComparisonBase);
  const isDirty = useMemo(
    () =>
      !isEqual(
        makeComparisonPrompt(dirtyFunctionComparisonBase),
        makeComparisonPrompt(getOutputPrompt(prompt)),
      ),
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps -- codeTouched intentionally added to recompute isDirty when the code editor is modified
    [dirtyFunctionComparisonBase, getOutputPrompt, prompt, codeTouched],
  );
  useEffect(() => {
    isDirtyRef.current = isDirty;
  }, [isDirty, isDirtyRef]);

  const router = useRouter();
  const onSubmit = async () => {
    const outputPrompt = getOutputPrompt(prompt);

    if (!outputPrompt.name || outputPrompt.name.trim() === "") {
      setError("Name cannot be empty");
      return;
    }
    if (!outputPrompt.slug || outputPrompt.slug.trim() === "") {
      setError("Slug cannot be empty");
      return;
    }

    setError(null);
    setUpdating(true);

    try {
      await upsert(outputPrompt, isSlugTouchedRef.current);
      setDirtyFunctionComparisonBase(outputPrompt);

      if (mode.type === "view_unsaved") {
        router.push(
          getSavedPromptLink({
            orgName,
            projectSlug: projectName,
            promptId: outputPrompt.id,
            type,
          }),
        );
      }
    } catch (e) {
      setError(`${e}`);
    } finally {
      setUpdating(false);
    }
  };

  return (
    <Button
      className="flex-none"
      size="xs"
      variant="primary"
      type="submit"
      ref={saveButtonRef}
      disabled={
        mode.type === "view_unsaved" ? false : !isDirty || hasLintErrors
      }
      onClick={async (e) => {
        e.preventDefault();
        onSubmit();
      }}
      isLoading={updating}
    >
      {mode.type === "update" ? "Save version" : `Save as custom ${type}`}
    </Button>
  );
}

function makeComparisonPrompt(prompt: UIFunction | null) {
  if (prompt == null) return null;

  const cleanedPromptData = {
    ...prompt.prompt_data,
    // strip position
    ...(prompt.prompt_data?.options?.position
      ? { ...prompt.prompt_data?.options, position: undefined }
      : {}),
    // strip origin
    origin: undefined,
  };

  return {
    id: prompt.id,
    [TransactionIdField]: prompt[TransactionIdField],
    project_id: prompt.project_id,
    name: prompt.name,
    slug: prompt.slug,
    description: prompt.description,
    metadata: prompt.metadata,
    prompt_data: cleanedPromptData,
    function_data: prompt.function_data,
    function_type: prompt.function_type,
    tags: prompt.tags,
  };
}
