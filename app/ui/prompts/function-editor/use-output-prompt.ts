import { type RefObject, useCallback } from "react";
import {
  type SyncedPlaygroundBlock,
  type UIFunction,
} from "#/ui/prompts/schema";
import { type FunctionTab, type MetaFields } from "./types";
import { type TextEditorHandle } from "#/ui/text-editor";

type Args = {
  initialFunction: UIFunction | null;
  coercedFunction: UIFunction;
  tab: FunctionTab;
  jsEditorRef: RefObject<TextEditorHandle<string> | null>;
  pyEditorRef: RefObject<TextEditorHandle<string> | null>;
  metaFields: MetaFields;
};

/** Combines the selected tab, prompt editor atom state, and imperative retrieval of the code editors to construct the output prompt. Returns a callback which returns this output prompt when invoked with the current prompt state. */
export function useOutputPrompt({
  initialFunction,
  coercedFunction,
  tab,
  jsEditorRef,
  pyEditorRef,
  metaFields,
}: Args) {
  const initialCode =
    initialFunction?.function_data.type === "code" &&
    initialFunction?.function_data.data.type === "inline"
      ? initialFunction.function_data.data.code
      : undefined;
  const isBundled =
    initialFunction?.function_data?.type === "code" &&
    initialFunction?.function_data?.data?.type === "bundle";

  return useCallback(
    (promptState: SyncedPlaygroundBlock): UIFunction => {
      const [runtime_context, code] =
        tab === "ts"
          ? [
              { runtime: "node", version: "20" } as const,
              jsEditorRef.current?.getValue(),
            ]
          : tab === "py"
            ? [
                { runtime: "python", version: "3.12" } as const,
                pyEditorRef.current?.getValue(),
              ]
            : [undefined, undefined];

      const overwrittenFunctionData =
        runtime_context != null
          ? {
              type: "code" as const,
              data: {
                type: "inline" as const,
                runtime_context,
                // Fallback to initialCode to ensure code is correct before the editor refs are set up
                code: code ?? initialCode ?? "",
              },
            }
          : { type: "prompt" as const };

      const prompt: UIFunction = {
        ...promptState,
        ...metaFields,
        name: metaFields.name ?? coercedFunction.name,
        slug: metaFields.slug ?? coercedFunction.slug,
        tags: coercedFunction.tags,
        project_id: coercedFunction.project_id,
        function_type: coercedFunction.function_type,
        ...(promptState.function_data.type !== "global" && !isBundled
          ? { function_data: overwrittenFunctionData }
          : {}),
      };

      if (prompt.function_data.type === "code") {
        delete prompt.prompt_data;
      }

      return prompt;
    },
    [
      tab,
      jsEditorRef,
      pyEditorRef,
      initialCode,
      metaFields,
      coercedFunction.name,
      coercedFunction.slug,
      coercedFunction.tags,
      coercedFunction.project_id,
      coercedFunction.function_type,
      isBundled,
    ],
  );
}
