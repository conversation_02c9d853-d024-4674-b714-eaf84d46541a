import { type RefObject, useMemo } from "react";
import { Field, Schema, Utf8 } from "apache-arrow";
import { <PERSON><PERSON> } from "#/ui/button";
import { Info } from "lucide-react";
import { useAvailableModels } from "#/ui/prompts/models";
import { isEmpty } from "#/utils/object";
import { type JSONStructure, usePromptExtensions } from "#/ui/prompts/hooks";
import { type TextEditorHandle } from "#/ui/text-editor";

import { Ta<PERSON>, TabsContent, TabsTrigger, TabsList } from "#/ui/tabs";
import { Tooltip, TooltipContent, TooltipTrigger } from "#/ui/tooltip";
import { useFeatureFlags } from "#/lib/feature-flags";
import dynamic from "next/dynamic";
import { useAPIVersion } from "#/ui/api-version/check-api-version";
import { PLACEHOLDER_PY, PLACEHOLDER_TS } from "./code-editor-placeholders";
import { <PERSON><PERSON><PERSON>, TypescriptLogo } from "#/app/app/[org]/onboarding-logos";
import { InfoBanner } from "#/ui/info-banner";
import { PromptEditorSynced } from "#/app/app/[org]/prompt/[prompt]/prompt-editor-synced";
import { type FunctionTab, type Mode } from "./types";
import { useSyncedPrompts } from "#/app/app/[org]/p/[project]/prompts/synced/use-synced-prompts";
import { atom, useAtomValue } from "jotai";
import { type FunctionObjectType } from "@braintrust/core/typespecs";
import { type CopilotContextBuilder } from "#/ui/copilot/context";
import { toast } from "sonner";
import { NoAISecrets } from "#/ui/prompts/empty";
import { useUpsellContext } from "#/app/playground/upsell-dialog";

const DynamicCodeEditor = dynamic(
  () => import("./code-editor").then((mod) => mod.CodeEditor),
  {
    ssr: false,
  },
);

export const emptyPromptMetaSchema = new Schema([
  Field.new({ name: "id", type: new Utf8() }),
  Field.new({ name: "user", type: new Utf8() }),
  Field.new({ name: "search_text", type: new Utf8() }),
]);

const DEFAULT_OUTPUT_NAMES: string[] = [];

export function FunctionEditor({
  orgName,
  type,
  jsonStructure = null,
  outputNames = DEFAULT_OUTPUT_NAMES,
  copilotContext,
  mode,
  tab,
  pyEditorRef,
  jsEditorRef,
  onTouchCode,
  onRun,
  onTabChange,
  rowData,
}: {
  orgName: string;
  type: FunctionObjectType;
  jsonStructure?: JSONStructure | null;
  outputNames?: string[];
  copilotContext?: CopilotContextBuilder;
  mode: Mode;
  tab: FunctionTab;
  pyEditorRef: RefObject<TextEditorHandle<string> | null>;
  jsEditorRef: RefObject<TextEditorHandle<string> | null>;
  onTouchCode: VoidFunction;
  onRun: VoidFunction;
  onTabChange?: (tab: FunctionTab) => void;
  rowData?: Record<string, unknown>;
}) {
  const isReadOnly = mode.type === "view_saved" || mode.type === "view_unsaved";

  const {
    flags: { customFunctions },
  } = useFeatureFlags();

  const { extensions } = usePromptExtensions({
    jsonStructure,
    outputNames,
    expandInputVariables: false,
  });
  const {
    allAvailableModels,
    configuredModelsByProvider,
    noConfiguredSecrets,
  } = useAvailableModels({ orgName });
  const { onUpsell } = useUpsellContext();
  const showNoConfiguredSecretsMessage = noConfiguredSecrets && !onUpsell;

  const { sortedSyncedPromptsAtom_ROOT } = useSyncedPrompts();
  const prompt = useAtomValue(
    useMemo(
      () => atom((get) => get(sortedSyncedPromptsAtom_ROOT)[0]),
      [sortedSyncedPromptsAtom_ROOT],
    ),
  );

  const showTabs =
    mode.type === "create" &&
    prompt.function_data.type !== "global" &&
    type === "scorer";

  const bundledPreview =
    prompt.function_data?.type === "code" &&
    prompt.function_data?.data?.type === "bundle" &&
    prompt.function_data?.data?.preview;
  const tooComplex =
    (prompt.function_data?.type === "global" &&
      isEmpty(prompt.prompt_data?.prompt)) ||
    (prompt.function_data?.type === "code" &&
      prompt.function_data?.data?.type === "bundle");
  const showTooComplex = tooComplex && !bundledPreview;

  const codeBetaMessage = (
    <div className="px-1 pt-2 text-xs text-primary-500">
      Code runs in a sandboxed environment with restrictions on imports and
      filesystem access
    </div>
  );
  const { code_execution } = useAPIVersion();
  const noCodeExecution = <>Upgrade your stack and enable code execution</>;
  const codeDisabled = showTooComplex || !customFunctions || !code_execution;

  return (
    <>
      {!showTooComplex && type !== "agent" && (
        <Tabs
          className="flex flex-col gap-4"
          value={tab}
          onValueChange={(t) => {
            // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
            onTabChange?.(t as FunctionTab);
          }}
        >
          {showTabs && (
            <div>
              <div className="mb-2 text-xs font-medium">Type</div>
              <TabsList className="mb-2 inline-flex h-auto gap-1 rounded-md border p-1 bg-background">
                <TabsTrigger value="llm" asChild>
                  <Button
                    size="xs"
                    disabled={showTooComplex}
                    variant={tab === "llm" ? "primary" : "ghost"}
                    className="rounded text-xs data-[state=active]:shadow-none data-[state=active]:bg-primary-200"
                  >
                    LLM-as-a-judge
                  </Button>
                </TabsTrigger>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <TabsTrigger value="ts" asChild>
                        <Button
                          size="xs"
                          disabled={codeDisabled}
                          variant={tab === "ts" ? "primary" : "ghost"}
                          className="gap-2 rounded text-xs data-[state=active]:shadow-none data-[state=active]:bg-primary-200"
                        >
                          <TypescriptLogo className="size-3" />
                          TypeScript
                        </Button>
                      </TabsTrigger>
                    </div>
                  </TooltipTrigger>
                  {!code_execution && (
                    <TooltipContent>{noCodeExecution}</TooltipContent>
                  )}
                </Tooltip>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div>
                      <TabsTrigger value="py" asChild>
                        <Button
                          size="xs"
                          disabled={codeDisabled}
                          variant={tab === "py" ? "primary" : "ghost"}
                          className="gap-2 rounded text-xs data-[state=active]:shadow-none data-[state=active]:bg-primary-200"
                        >
                          <PythonLogo className="size-3" />
                          Python
                        </Button>
                      </TabsTrigger>
                    </div>
                  </TooltipTrigger>
                  {!code_execution && (
                    <TooltipContent>{noCodeExecution}</TooltipContent>
                  )}
                </Tooltip>
              </TabsList>
            </div>
          )}
          <TabsContent
            value="llm"
            className="mt-0"
            forceMount
            hidden={tab !== "llm"}
          >
            <div className="mb-2 text-xs font-medium">Prompt</div>
            <PromptEditorSynced
              isReadOnly={isReadOnly}
              orgName={orgName}
              modelOptionsByProvider={configuredModelsByProvider}
              allAvailableModels={allAvailableModels}
              extensions={extensions}
              promptData={prompt.prompt_data}
              onRun={() => {
                if (showNoConfiguredSecretsMessage) {
                  toast("No configured secrets", {
                    description: <NoAISecrets orgName={orgName} />,
                  });
                  return;
                }
                onRun();
              }}
              copilotContext={copilotContext}
              promptId={prompt.id}
              type={type}
              rowData={rowData}
              enableHotkeys
            />
          </TabsContent>
          <TabsContent
            value="ts"
            className="mt-0"
            forceMount
            hidden={tab !== "ts"}
          >
            <div className="mb-2 flex items-center gap-1 text-xs font-medium">
              {!showTabs && <TypescriptLogo className="size-3" />}
              TypeScript
            </div>
            {customFunctions ? (
              <div className="rounded-md border p-2 bg-primary-100">
                <DynamicCodeEditor
                  savedCode={
                    bundledPreview
                      ? bundledPreview
                      : prompt.function_data.type === "code" &&
                          prompt.function_data.data.type === "inline" &&
                          prompt.function_data.data.runtime_context.runtime ===
                            "node"
                        ? prompt.function_data.data.code
                        : PLACEHOLDER_TS
                  }
                  readOnly={!!bundledPreview || isReadOnly}
                  editorRef={jsEditorRef}
                  language="ts"
                  copilotContext={copilotContext}
                  onSave={onTouchCode}
                />
                {codeBetaMessage}
              </div>
            ) : (
              noCodeExecution
            )}
          </TabsContent>
          <TabsContent
            value="py"
            className="mt-0"
            forceMount
            hidden={tab !== "py"}
          >
            <div className="mb-2 flex items-center gap-1 text-xs font-medium">
              {!showTabs && <PythonLogo className="size-3" />}
              Python
            </div>
            {customFunctions ? (
              <div className="rounded-md border p-2 bg-primary-100">
                <DynamicCodeEditor
                  savedCode={
                    bundledPreview
                      ? bundledPreview
                      : prompt.function_data.type === "code" &&
                          prompt.function_data.data.type === "inline" &&
                          prompt.function_data.data.runtime_context.runtime ===
                            "python"
                        ? prompt.function_data.data.code
                        : PLACEHOLDER_PY
                  }
                  readOnly={!!bundledPreview || isReadOnly}
                  editorRef={pyEditorRef}
                  language="py"
                  copilotContext={copilotContext}
                  onSave={onTouchCode}
                />
                {codeBetaMessage}
              </div>
            ) : (
              noCodeExecution
            )}
          </TabsContent>
        </Tabs>
      )}
      {showTooComplex && (
        <div className="flex items-center rounded-md border p-3 text-sm bg-accent-50 border-accent-100 text-primary-700">
          <Info className="mr-1.5 inline-block size-3 flex-none" />
          This function is too complex to edit in the UI. You can run the
          function here, or edit it via the API.
        </div>
      )}
      {type === "agent" && (
        <InfoBanner>
          Agents are currently only visible in playgrounds
        </InfoBanner>
      )}
    </>
  );
}
