import { useState } from "react";
import { type FunctionTab } from "./types";
import { type UIFunction } from "#/ui/prompts/schema";

export function useFunctionEditorTabs({
  initialFunction,
}: {
  initialFunction: UIFunction | null;
}) {
  const defaultTab: FunctionTab =
    initialFunction?.function_data.type === "code"
      ? initialFunction?.function_data.data.runtime_context.runtime === "node"
        ? "ts"
        : "py"
      : "llm";
  const [_activeTab, setActiveTab] = useState<FunctionTab>();
  const activeTab = _activeTab ?? defaultTab;

  return { activeTab, setActiveTab };
}
