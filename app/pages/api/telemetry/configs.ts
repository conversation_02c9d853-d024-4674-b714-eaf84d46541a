import { runJsonRequest } from "#/pages/api/_request_util";
import { isAllowedSysadmin } from "#/utils/derive-error-context";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { endpointSchemas } from "@braintrust/local/app-schema";
import type { NextApiRequest, NextApiResponse } from "next";
import { z } from "zod";

const { input: paramsSchema, output: outputSchema } =
  endpointSchemas.telemetry_configs_typespecs;

const lookupConfigsByOrgIds = async (orgIds: string[]) => {
  // Retrieve configs for the given org ids, excluding any org that
  // the user doesn't have access to
  const supabase = getServiceRoleSupabase();
  return await supabase.query(
    `with accessible_orgs as (
      select organizations.id as org_id
      from organizations
      where organizations.id = any($1::uuid[])
    )
    select org_billing.org_id, org_billing.telemetry_url
    from org_billing
    join accessible_orgs on org_billing.org_id = accessible_orgs.org_id`,
    [orgIds],
  );
};

const lookupConfigsByOrgIdsForUser = async (
  orgIds: string[],
  userId: string,
) => {
  // Retrieve configs for the given org ids, excluding any org that
  // the user doesn't have access to
  const supabase = getServiceRoleSupabase();
  return await supabase.query(
    `with accessible_orgs as (
      select organizations.id as org_id
      from organizations
      join members on organizations.id = members.org_id
      where members.user_id = $1
        and organizations.id = any($2::uuid[])
    )
    select org_billing.org_id, org_billing.telemetry_url
    from org_billing
    join accessible_orgs on org_billing.org_id = accessible_orgs.org_id`,
    [userId, orgIds],
  );
};

export default async function action(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  await runJsonRequest(
    req,
    res,
    async ({ org_ids: orgIdsToCheck }, authLookup) => {
      const configs: Record<string, { url: string; secret: string }> = {};

      const isSysAdmin = await isAllowedSysadmin(authLookup);

      if (orgIdsToCheck.length) {
        const result = !isSysAdmin
          ? await lookupConfigsByOrgIdsForUser(
              orgIdsToCheck,
              authLookup.user_id,
            )
          : await lookupConfigsByOrgIds(orgIdsToCheck);
        const resultParsed = z
          .object({
            org_id: z.string(),
            telemetry_url: z.string().nullish(),
          })
          .array()
          .parse(result.rows);

        for (const row of resultParsed) {
          const url = row.telemetry_url || "";
          configs[row.org_id] = {
            url,
            // future proof should we need to provide a different secret
            // for now all orgs should fallback to the user's token
            secret: "",
          };
        }
      }

      return { configs };
    },
    {
      paramsSchema,
      outputSchema,
    },
  );
}
