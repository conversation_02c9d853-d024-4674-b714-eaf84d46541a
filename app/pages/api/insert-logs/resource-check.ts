import type { NextApiRequest, NextApiResponse } from "next";
import { invokeServiceRoleSupabaseUdf } from "../_invoke_supabase_udf";
import { endpointSchemas } from "@braintrust/local/app-schema";
import { makeError } from "@braintrust/local";
import { httpHandleError } from "../_request_util";
import { z } from "zod";
import { DebugError } from "#/utils/error";
import { OPTIMIZATION_PROJECT_ID } from "#/utils/constants";
import { getServiceRoleSupabase } from "#/utils/supabase";

const { input: paramsSchema, output: outputSchema } =
  endpointSchemas.insert_logs_resource_check_typespecs;

export default async function get_token_api(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  const getEveryOrgIsUnlimited = () => {
    // We can't assume the input shape is valid. Let's get the important subset of the input.
    // If we fail to parse, we'll just assume no one is unlimited.
    const parsed = z
      .object({
        input: z.object({
          additional_org_ids: paramsSchema.shape.input.shape.additional_org_ids,
        }),
      })
      .safeParse(req.body);

    const orgIds =
      (parsed.success && parsed.data.input.additional_org_ids) || [];

    return {
      is_unlimited: Object.fromEntries(orgIds.map((orgId) => [orgId, true])),
    };
  };

  if (
    req.headers["x-bt-debug-disable-resource-check"] ||
    process.env.DISABLE_RESOURCE_CHECK
  ) {
    res.json(outputSchema.parse(getEveryOrgIsUnlimited()));
    res.end();
    return;
  }

  await invokeServiceRoleSupabaseUdf(
    req,
    res,
    "update_resource_counts_for_insert",
    {
      paramsSchema,
      outputSchema,
      argnames: ["input", "num_shards", "auth_id"],
      allowExtraFields: true,
      async processError({ error: e, res, authLookup }) {
        // Doing this in the error handler so that we don't add additional parsing on the critical path, since
        // this is an edge case.
        const parsedParams = paramsSchema.safeParse(req.body);
        if (OPTIMIZATION_PROJECT_ID && parsedParams.success) {
          if (
            parsedParams.data.input.additional_project_ids?.length === 1 &&
            parsedParams.data.input.additional_project_ids[0] ===
              OPTIMIZATION_PROJECT_ID
          ) {
            const is_unlimited: Record<string, boolean> = {};
            const conn = getServiceRoleSupabase();
            const orgProjectId = await conn.query(
              "SELECT org_id FROM projects WHERE id = $1",
              [OPTIMIZATION_PROJECT_ID],
            );
            if (orgProjectId.rows.length > 0) {
              const orgProjectIdParsed = z
                .object({ org_id: z.string() })
                .parse(orgProjectId.rows[0]);
              is_unlimited[orgProjectIdParsed.org_id] = true;
            }
            const response: z.infer<typeof outputSchema> = {
              is_unlimited,
            };
            res.json(response);
            res.end();
            return;
          }
        }

        const error = makeError(e);

        // An allow list of errors that we do intend to raise to the user.
        //
        // Note: it may be possible that the expected errors are raised erroneously due to regressions.
        // At which point you should use the DISABLE_RESOURCE_CHECK and a redeploy to disable the
        // resource check momentarily.
        if (
          error.message.includes("Violations of resource constraint") ||
          error.message.includes("User does not have permission") ||
          error.message.includes("failed to validate clerk")
        ) {
          return await httpHandleError({ error, authLookup, req, res });
        }

        console.warn(`Relaxing resource check error`, { error });

        if (e instanceof DebugError) {
          res.setHeader("x-bt-debug-resource-check-error", error.message);
        }

        res.json(outputSchema.parse(getEveryOrgIsUnlimited()));
      },
    },
  );
}
