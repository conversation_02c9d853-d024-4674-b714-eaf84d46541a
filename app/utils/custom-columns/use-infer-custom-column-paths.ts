import {
  dataObjectPageShape,
  fetchBtql,
  fetchInferBtql,
  rowWithIdsSchema,
  type InferSchema,
} from "#/utils/btql/btql";
import { useMemo } from "react";
import { useFeatureFlags } from "#/lib/feature-flags";
import { useSessionToken } from "#/utils/auth/session-token";
import { useOrg } from "#/utils/user";
import * as Query from "#/utils/btql/query-builder";
import { useQuery } from "@tanstack/react-query";
import { type DataObjectType } from "#/utils/btapi/btapi";
import { withErrorTiming } from "#/utils/btapi/type-error";
import { makeRowIdPrimary, type RowId } from "#/utils/diffs/diff-objects";

export function useInferCustomColumnPaths({
  objectType,
  objectId,
  rowId,
}: {
  objectType: DataObjectType;
  objectId: string | null;
  rowId: RowId | null;
}) {
  const org = useOrg();
  const { flags } = useFeatureFlags();
  const { getOrRefreshToken } = useSessionToken();

  const hasSchemaInference = flags.schemaInference;
  const shape = dataObjectPageShape(objectType);
  const primaryRowId = makeRowIdPrimary(rowId);

  const { data: inferData } = useQuery({
    queryKey: ["inferCustomColumnPathsData", objectType, objectId, shape],
    queryFn: withErrorTiming(
      async ({ signal }: { signal: AbortSignal }) =>
        objectId && primaryRowId
          ? await fetchBtql({
              args: {
                query: {
                  from: Query.from(objectType, [objectId], shape),
                  select: [{ op: "star" }],
                  sort: [{ expr: { btql: "_pagination_key" }, dir: "desc" }],
                  filter: {
                    op: "eq",
                    left: { op: "ident", name: ["id"] },
                    right: { op: "literal", value: primaryRowId },
                  },
                  ...(shape === "summary" ? { preview_length: -1 } : {}),
                },
                brainstoreRealtime: true,
              },
              flags,
              apiUrl: org.api_url,
              getOrRefreshToken,
              schema: rowWithIdsSchema,
              signal,
            })
          : null,
      "Infer custom column paths (data)",
      {
        objectType,
        shape,
      },
    ),
    enabled: !!objectId && hasSchemaInference && !!primaryRowId,
    gcTime: 1000 * 30,
    staleTime: 1000 * 60,
  });

  const { data: inferSchemaData } = useQuery({
    queryKey: ["inferCustomColumnPathsSchema", objectType, objectId],
    queryFn: withErrorTiming(
      async ({ signal }: { signal: AbortSignal }) =>
        objectId
          ? await fetchInferBtql({
              args: {
                query: {
                  from: Query.from(objectType, [objectId]),
                  filter: {
                    op: "eq",
                    left: { op: "ident", name: ["is_root"] },
                    right: { op: "literal", value: true },
                  },
                  infer: [
                    { op: "ident" as const, name: ["input"] },
                    { op: "ident" as const, name: ["expected"] },
                    { op: "ident" as const, name: ["metadata"] },
                  ].concat(
                    objectType !== "dataset"
                      ? [{ op: "ident" as const, name: ["output"] }]
                      : [],
                  ),
                  sort: [{ expr: { btql: "_pagination_key" }, dir: "desc" }],
                },
                brainstoreRealtime: true,
              },
              flags,
              apiUrl: org.api_url,
              getOrRefreshToken,
              signal,
            })
          : null,
      "Infer custom column paths (schema)",
      {
        objectType,
      },
    ),
    enabled: !!objectId && hasSchemaInference,
    gcTime: 1000 * 30,
    staleTime: 1000 * 60,
  });

  return useMemo(() => {
    if (!inferSchemaData || !inferData) {
      return;
    }

    const processedPaths = processInferredPaths(
      inferSchemaData.data,
      inferData.data,
    );

    const paths = Array.from(processedPaths.values());
    const groupedFields = paths.reduce((acc: Record<string, string[][]>, d) => {
      const firstItem = d[0];
      if (!acc[firstItem]) {
        acc[firstItem] = [];
      }
      if (d.length > 1) {
        acc[firstItem].push(d.slice(1));
      }
      return acc;
    }, {});

    Object.keys(groupedFields).forEach((key) => {
      groupedFields[key].sort((a, b) => {
        return a.join(".").localeCompare(b.join("."));
      });
    });

    return groupedFields;
  }, [inferSchemaData, inferData]);
}

function processInferredPaths(
  inferSchemaData: InferSchema[],
  inferData: Record<string, unknown>[],
) {
  const processedPaths = new Map<string, string[]>();

  for (const schema of inferSchemaData) {
    const pathParts = schema.name;
    if (processedPaths.has(pathParts.join("."))) {
      continue;
    }
    for (const data of inferData) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      let current: any = data;
      const modifiedPath = [...pathParts];

      for (let i = 0; i < pathParts.length; i++) {
        if (!current || typeof current !== "object") {
          break;
        }
        const part = pathParts[i];
        if (Array.isArray(current)) {
          modifiedPath.splice(i, 0, "[0]");
        }
        current = current[part];
      }

      processedPaths.set(pathParts.join("."), modifiedPath);
    }
  }

  return processedPaths;
}
