import NextAuth from "next-auth";
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials";
import { getServiceRoleSupabase } from "./utils/supabase";
import { lookupApiKey } from "./pages/api/_lookup_api_key";
import { z } from "zod";
import { API_KEY_AUTH } from "./utils/auth/constants";

export const { auth, handlers, signIn, signOut } = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        api_key: { label: "api_key", type: "text", placeholder: "api key" },
      },

      authorize: async (credentials) => {
        const conn = getServiceRoleSupabase();
        const key = z.string().parse(credentials!.api_key);

        const authResult = await (async () => {
          try {
            return await lookupApiKey({ key });
          } catch (e) {
            console.log(`${e}`);
            return null;
          }
        })();
        if (!authResult) {
          return authResult;
        }
        const userResult = await conn.query(
          "SELECT email FROM users WHERE auth_id=$1",
          [authResult.auth_id],
        );
        const userParsed = z
          .object({ email: z.string() })
          .nullish()
          .parse(userResult.rows[0]);

        if (userParsed) {
          // Technically, we should (optionally) scope the JWT to the org, but for now, since
          // this is just a debugging feature, we'll just allow the API key to grant access to
          // all orgs.
          return {
            id: authResult.auth_id,
            api_key: key,
            email: userParsed.email,
          };
        } else {
          return null;
        }
      },
    }),
  ],
  session: {
    // We'll refresh up to this amount of time before the token expires
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, account, profile, user }) {
      // Persist the OAuth access_token to the token right after signin
      if (account) {
        token.accessToken = account.access_token;
        token.idToken = account.id_token;
        token.refreshToken = account.refresh_token;
        token.accountId = account.providerAccountId;
        token.expiresAt = account.expires_at;

        if (API_KEY_AUTH) {
          token.apiKey = user.api_key;
          token.expiresAt = Date.now() / 1000 + 60 * 60 * 12; // Allow API key to expire after 12 hours
        }

        token.loggedIn = false;

        const supabase = getServiceRoleSupabase();
        try {
          if (!API_KEY_AUTH) {
            // There are two dup-keys: auth_id and email. In G-suite, if you have multiple domains mapped to the
            // same org, you could have multiple emails/aliases mapping to the same underlying user (auth_id). The opposite
            // should not occur (two emails with different auth_ids), UNLESS you invite a user in terms of an email that
            // is already registered to a different auth_id.
            //
            // For now, we reconcile these cases by pivoting around the auth_id as the dup key, and asking the user to contact us
            // if they notice an error.
            await supabase.query(
              `
            INSERT INTO users
              (email, auth_id, given_name, family_name, avatar_url)
            VALUES
              ($1, $2, $3, $4, $5)
            ON CONFLICT (auth_id) DO UPDATE
              SET
                email = EXCLUDED.email,
                given_name = EXCLUDED.given_name,
                family_name = EXCLUDED.family_name,
                avatar_url = EXCLUDED.avatar_url
          `,
              [
                token.email,
                token.accountId,
                profile?.given_name,
                profile?.family_name,
                profile?.picture,
              ],
            );
          }
        } catch (e) {
          console.error("Failed to update by auth_id, will try email", e);

          // Try one more time, this time dup-keying on the email
          try {
            await supabase.query(
              `
            INSERT INTO users
              (email, auth_id, given_name, family_name, avatar_url)
            VALUES
              ($1, $2, $3, $4, $5)
            ON CONFLICT (email) DO UPDATE
              SET
                auth_id = EXCLUDED.auth_id,
                given_name = EXCLUDED.given_name,
                family_name = EXCLUDED.family_name,
                avatar_url = EXCLUDED.avatar_url
          `,
              [
                token.email,
                token.accountId,
                profile?.given_name,
                profile?.family_name,
                profile?.picture,
              ],
            );
          } catch (e) {
            console.error("Failed to update by email", e);
            return { ...token, error: "InternalError" };
          }
        }
        token.loggedIn = true;
      }

      return token;
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    async session({ session, token, user }: any) {
      session.loggedIn = token.loggedIn;
      session.error = token.error;

      // Send properties to the client, like an access_token from a provider.
      if (session.loggedIn) {
        session.idToken = token.idToken;
        session.accessToken = token.accessToken;
        session.refreshToken = token.refreshToken;
        session.accountId = token.accountId;
      }

      if (API_KEY_AUTH && token.apiKey) {
        session.apiKey = token.apiKey;
      }

      return session;
    },
  },
});

declare module "next-auth" {
  interface Session {
    loggedIn: boolean;
    accountId?: string;
    idToken?: string;
    accessToken?: string;
    refreshToken?: string;
    apiKey?: string;
    error?: "RefreshAccessTokenError" | "InternalError";
  }

  interface User {
    api_key?: string;
  }

  interface Account {
    api_key: string;
  }
}
