CREATE TYPE user_type_enum AS ENUM ('user', 'service_account');
ALTER TABLE users ADD COLUMN user_type user_type_enum NOT NULL DEFAULT 'user';

create or replace function create_api_key_unchecked(
    auth_id uuid,
    org_id uuid,
    name text,
    is_service_token boolean)
returns jsonb
language plpgsql
security definer
as $$
DECLARE
  api_key text;
  prefix text;
  _created_api_key_id uuid;
BEGIN
  -- Remove + and / to make it easier to use in terminals, etc.
  api_key := replace(replace(encode(pgsodium.randombytes_buf(36), 'base64'), '+', '0'), '/', '1');
  IF NOT is_service_token THEN
    prefix := 'sk-';
  ELSE
    prefix := 'bt-st-';
  END IF;
  INSERT INTO api_keys (key_hash, name, preview_name, user_id, org_id)
  SELECT
    pgsodium.crypto_generichash(decode(api_key, 'base64')), name, CONCAT(prefix, RIGHT(api_key, 4)), id, org_id
    FROM users WHERE users.auth_id = create_api_key_unchecked.auth_id
  RETURNING id INTO _created_api_key_id;

  return (
    select jsonb_build_object(
        'api_key', (
          to_jsonb(api_keys) ||
          jsonb_build_object('key', concat(prefix, api_key)) ||
          jsonb_build_object('user_given_name', users.given_name) ||
          jsonb_build_object('user_family_name', users.family_name) ||
          jsonb_build_object('user_email', users.email)
        )
    )
    from api_keys
    join users on api_keys.user_id = users.id
    where api_keys.id = _created_api_key_id
  );
END;
$$;

revoke execute on function create_api_key_unchecked from anon;

drop function create_api_key_full;

create or replace function create_api_key_full(
    auth_id uuid,
    org_id uuid,
    name text,
    is_service_token boolean)
returns jsonb
language plpgsql
security definer
as $$
DECLARE
  _org_check int;
BEGIN
  SELECT 1
  FROM users JOIN members ON users.id = members.user_id
  WHERE users.auth_id = create_api_key_full.auth_id
    AND (members.org_id = create_api_key_full.org_id OR create_api_key_full.org_id IS NULL)
  INTO _org_check;

  if not found then
    raise exception '%', jsonb_build_object('kind', 'http-error', 'code', 400, 'message', format('User with auth_id %s does not belong to organization with id %s', auth_id, org_id));
  end if;

  return create_api_key_unchecked(auth_id, org_id, name, is_service_token);
END;
$$;

revoke execute on function create_api_key_full from anon;

-- We preserve the version of the function with a more restricted API for
-- existing callers.
create or replace function create_api_key(
    auth_id uuid,
    org_id uuid,
    name text)
returns text
language sql
security definer
as $$ select create_api_key_full(auth_id, org_id, name, false)->'api_key'->>'key' $$;

revoke execute on function create_api_key from anon;

create or replace function bt_service_account_user_id()
returns uuid language sql immutable parallel safe
as $$
    SELECT '6f87ebcc-7ea3-4f47-ae4c-e8f9514723e1'::uuid;
$$;

revoke execute on function bt_service_account_user_id from public, anon;

create or replace function bt_service_account_auth_id()
returns uuid language sql immutable parallel safe
as $$
    SELECT '8bd7e3e0-7b82-46a4-9d81-b58c5edeb281'::uuid;
$$;

revoke execute on function bt_service_account_auth_id from public, anon;

create or replace function bt_service_account_email()
returns text language sql immutable parallel safe
as $$
    SELECT 'bt::sp::bt_data_plane_manager-sysadmin';
$$;

revoke execute on function bt_service_account_email from public, anon;

insert into users (id, auth_id, email, given_name, family_name, user_type)
values (
  bt_service_account_user_id(),
  bt_service_account_auth_id(),
  bt_service_account_email(),
  'bt_data_plane_manager',
  '',
  'service_account'
);
