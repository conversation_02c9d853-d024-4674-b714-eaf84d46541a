import { getRedirectParam } from "#/utils/auth/redirect";
import { trackSegmentEvent } from "#/utils/segment/segment";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { SignOutButton } from "@clerk/nextjs";
import { auth, clerkClient } from "@clerk/nextjs/server";
import Link from "next/link";
import { redirect } from "next/navigation";
import { z } from "zod";

const newUserInfoSchema = z.object({
  auth_id: z.string(),
  id: z.string(),
  is_new: z.boolean(),
});

export default async function AfterSignIn(props: {
  params: Promise<{ slug: string }>;
  searchParams: Promise<Record<string, string | string[]>>;
}) {
  const searchParams = await props.searchParams;
  const { userId, sessionClaims } = await auth();

  const email = sessionClaims?.primaryEmail ?? null;

  let authId: string;
  let btUserId: string;
  let isNew: boolean;

  if (userId) {
    const supabase = getServiceRoleSupabase();
    try {
      // There are two dup-keys: clerk_id and email. In G-suite, if you have multiple domains mapped to the
      // same org, you could have multiple emails/aliases mapping to the same underlying user (clerk_id). The opposite
      // should not occur (two emails with different clerk_ids), UNLESS you invite a user in terms of an email that
      // is already registered to a different clerk_id.
      //
      // For now, we reconcile these cases by pivoting around the clerk_id as the dup key, and asking the user to contact us
      // if they notice an error.
      //
      // Also note that we support the (legacy) auth_id by just generating a random uuid if needed
      const { rows } = await supabase.query(
        `
            INSERT INTO users
              (email, auth_id, clerk_id, given_name, family_name, avatar_url)
            VALUES
              ($1, gen_random_uuid(), $2, $3, $4, $5)
            ON CONFLICT (clerk_id) DO UPDATE
              SET
                email = EXCLUDED.email,
                -- Do not clobber existing values
                auth_id = COALESCE(users.auth_id, EXCLUDED.auth_id),
                given_name = COALESCE(users.given_name, EXCLUDED.given_name),
                family_name = COALESCE(users.family_name, EXCLUDED.family_name),
                avatar_url = COALESCE(users.avatar_url, EXCLUDED.avatar_url)
            RETURNING auth_id, id, (xmax = 0) as is_new
          `,
        [
          email,
          userId,
          sessionClaims?.firstName,
          sessionClaims?.lastName,
          sessionClaims?.imageUrl,
        ],
      );
      const rowsParsed = newUserInfoSchema.array().parse(rows);
      authId = rowsParsed[0].auth_id;
      btUserId = rowsParsed[0].id;
      isNew = rowsParsed[0].is_new;
    } catch (e) {
      console.error("Failed to update by auth_id, will try email", e);

      // Try one more time, this time dup-keying on the email
      try {
        const { rows } = await supabase.query(
          `
            INSERT INTO users
              (email, auth_id, clerk_id, given_name, family_name, avatar_url)
            VALUES
              ($1, gen_random_uuid(), $2, $3, $4, $5)
            ON CONFLICT (email) DO UPDATE
              SET
                clerk_id = EXCLUDED.clerk_id,
                -- Do not clobber existing values
                auth_id = COALESCE(users.auth_id, EXCLUDED.auth_id),
                given_name = COALESCE(users.given_name, EXCLUDED.given_name),
                family_name = COALESCE(users.family_name, EXCLUDED.family_name),
                avatar_url = COALESCE(users.avatar_url, EXCLUDED.avatar_url)
            RETURNING auth_id, id, (xmax = 0) as is_new
          `,
          [
            email,
            userId,
            sessionClaims?.firstName,
            sessionClaims?.lastName,
            sessionClaims?.imageUrl,
          ],
        );
        const rowsParsed = newUserInfoSchema.array().parse(rows);
        authId = rowsParsed[0].auth_id;
        btUserId = rowsParsed[0].id;
        isNew = rowsParsed[0].is_new;
      } catch (e) {
        console.error("Failed to update by email", e);
        return (
          <div>
            Failed to update user. Please contact{" "}
            <Link href="mailto:<EMAIL>">
              <EMAIL>
            </Link>{" "}
            for help.
            <br />
            <br />
            <SignOutButton />
          </div>
        );
      }
    }

    const domain = email ? email.split("@")[1] : null;
    if (domain && isNew) {
      // RBAC_DISCLAIMER: In general, group modification is only allowed by users who have
      // update permissions on the group. But since this codepath is only activated for
      // newly-added users, and presumably the org administrator has selected this group
      // for new users, we are skipping the RBAC check.
      const { rows: toAddOrgRows } = await supabase.query(
        `
        WITH add_orgs AS (
          SELECT org_id, group_id FROM domain_mappings
          WHERE domain = $1 AND NOT EXISTS (
            SELECT 1 FROM members WHERE user_id = $2 AND org_id = domain_mappings.org_id
          )
        )
        SELECT
            org_id,
            group_id,
            add_member_to_org_unchecked($2, org_id, array [group_id])
          FROM add_orgs
        `,
        [domain, btUserId],
      );
      console.log(
        `Added ${email} to ${toAddOrgRows
          .map((r) => `org: ${r.org_id}, group: ${r.group_id ?? "none"}`)
          .join(", ")}`,
      );
    }

    if (authId !== sessionClaims?.publicMetadata?.bt_auth_id) {
      const client = await clerkClient();
      await client.users.updateUser(userId, {
        publicMetadata: {
          bt_auth_id: authId,
        },
      });
    }

    trackSegmentEvent({
      userId: btUserId,
      event: "signin",
      properties: {
        email: sessionClaims?.primaryEmail,
      },
    });
  }

  const [redirectPath, redirectParamsString] = (
    getRedirectParam(searchParams) || "/app"
  ).split("?");
  const redirectParams = new URLSearchParams(redirectParamsString);
  redirectParams.append("reset-user", "1");
  return redirect(`${redirectPath}?${redirectParams.toString()}`);
}
