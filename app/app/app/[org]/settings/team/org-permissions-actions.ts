"use server";

import type { AuthLookup } from "#/utils/server-util";
import { HTTPError } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  extractSingularRow,
  makeFullResultSetQuery,
} from "#/pages/api/_object_crud_util";
import {
  type OrgPermissionsCheckboxes,
  orgPermissionsCheckboxesSchema,
  orgPermissionsRequirements,
} from "./org-permissions-types";
import {
  type UserObjectType,
  permissionsRequirementKeySchema,
} from "#/ui/permissions/permissions-types";
import { permissionsCheckboxQuery } from "#/ui/permissions/permissions-query-util";
import { singleQuote } from "#/utils/sql-utils";
import { z } from "zod";
//import { substituteParamsDebug } from "#/utils/sql-query-params";

export type GetOrgPermissionsInputs = {
  orgId: string;
  userObjectType: UserObjectType;
  userGroupId: string;
};

export async function getOrgPermissions(
  { orgId, userObjectType, userGroupId }: GetOrgPermissionsInputs,
  authLookupRaw?: AuthLookup,
): Promise<OrgPermissionsCheckboxes> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!orgId) {
    throw new HTTPError(403, "Unauthorized");
  }

  const {
    query: orgQuery,
    queryParams,
    notFoundErrorMessage,
  } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "organization",
      aclPermission: "read_acls",
    },
    filters: {
      id: [orgId],
    },
  });
  const userGroupIdParam = queryParams.add(userGroupId);
  const fullQuery = `
    with
    org_access as (
      select id from (${orgQuery}) "t" limit 1
    )
    select jsonb_build_object(
        ${Object.entries(orgPermissionsRequirements)
          .map(
            ([key, requirement]) => `
            ${singleQuote(key)}, (${permissionsCheckboxQuery({
              requirement,
              objectIdSubquery: "select id from org_access",
              userObjectType,
              userGroupIdParam,
            })})
          `,
          )
          .join(", ")}
    ) result
    from org_access
  `;
  const supabase = getServiceRoleSupabase();
  //console.log("Running query\n", substituteParamsDebug(fullQuery, queryParams.params));
  const row = extractSingularRow({
    rows: (await supabase.query(fullQuery, queryParams.params)).rows,
    notFoundErrorMessage,
  });
  const rowResult = z
    .object({ result: z.record(z.unknown()) })
    .parse(row).result;
  const out: Record<string, Record<string, unknown>> = {};
  for (const key of Object.keys(orgPermissionsRequirements)) {
    const { outerKey, innerKey } = permissionsRequirementKeySchema.parse(
      JSON.parse(key),
    );
    if (!(outerKey in out)) {
      out[outerKey] = {};
    }
    out[outerKey][innerKey] = rowResult[key];
  }
  //console.log("out =\n", out);
  return orgPermissionsCheckboxesSchema.parse(out);
}
