"use server";

import type { AuthLookup } from "#/utils/server-util";
import { HTTPError } from "#/utils/server-util";
import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import {
  extractSingularRow,
  makeFullResultSetQuery,
} from "#/pages/api/_object_crud_util";
import {
  type ProjectPermissionsCheckboxes,
  projectPermissionsCheckboxesSchema,
  projectPermissionsRequirements,
} from "./project-permissions-types";
import {
  type UserObjectType,
  permissionsRequirementKeySchema,
} from "#/ui/permissions/permissions-types";
import { permissionsCheckboxQuery } from "#/ui/permissions/permissions-query-util";
import { singleQuote } from "#/utils/sql-utils";
import { z } from "zod";

export type GetProjectPermissionsInputs = {
  projectId: string;
  userObjectType: UserObjectType;
  userGroupId: string;
};

export async function getProjectPermissions(
  { projectId, userObjectType, userGroupId }: GetProjectPermissionsInputs,
  authLookupRaw?: AuthLookup,
): Promise<ProjectPermissionsCheckboxes> {
  const authLookup = authLookupRaw ?? (await getServerSessionAuthLookup());
  if (!projectId) {
    throw new HTTPError(403, "Unauthorized");
  }

  const {
    query: projectQuery,
    queryParams,
    notFoundErrorMessage,
  } = makeFullResultSetQuery({
    authLookup,
    permissionInfo: {
      aclObjectType: "project",
      aclPermission: "read_acls",
    },
    filters: {
      id: [projectId],
    },
  });
  const userGroupIdParam = queryParams.add(userGroupId);
  const fullQuery = `
    with
    project_access as (
      select id from (${projectQuery}) "t" limit 1
    )
    select jsonb_build_object(
        ${Object.entries(projectPermissionsRequirements)
          .map(
            ([key, requirement]) => `
            ${singleQuote(key)}, (${permissionsCheckboxQuery({
              requirement,
              objectIdSubquery: "select id from project_access",
              userObjectType,
              userGroupIdParam,
            })})
          `,
          )
          .join(", ")}
    ) result
    from project_access
  `;
  const supabase = getServiceRoleSupabase();
  //console.log("Running query\n", fullQuery, "\nwith params\n", queryParams.params);
  const row = extractSingularRow({
    rows: (await supabase.query(fullQuery, queryParams.params)).rows,
    notFoundErrorMessage,
  });
  const rowResult = z
    .object({ result: z.record(z.unknown()) })
    .parse(row).result;
  const out: Record<string, Record<string, unknown>> = {};
  for (const key of Object.keys(projectPermissionsRequirements)) {
    const { outerKey, innerKey } = permissionsRequirementKeySchema.parse(
      JSON.parse(key),
    );
    if (!(outerKey in out)) {
      out[outerKey] = {};
    }
    out[outerKey][innerKey] = rowResult[key];
  }
  //console.log("out =\n", out);
  return projectPermissionsCheckboxesSchema.parse(out);
}
