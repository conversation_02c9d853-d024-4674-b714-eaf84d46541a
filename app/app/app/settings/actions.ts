"use server";

import { getServerSessionAuthLookup } from "#/utils/server-session-util";
import { getServiceRoleSupabase } from "#/utils/supabase";
import { z } from "zod";

// RBAC_DISCLAIMER: A user can see all of the orgs they belong to.
export async function getFirstOrg(): Promise<{
  orgName?: string;
  total: number;
} | null> {
  const authLookup = await getServerSessionAuthLookup();

  const supabase = getServiceRoleSupabase();
  const { rows: organizations } = await supabase.query(
    `
    SELECT organizations.name
    FROM organizations
      JOIN members ON organizations.id = members.org_id
      JOIN users ON members.user_id = users.id
    WHERE users.auth_id = $1`,
    [authLookup.auth_id],
  );

  if (!(organizations && organizations.length)) {
    return null;
  }

  const organizationsParsed = z
    .object({ name: z.string() })
    .array()
    .parse(organizations);

  return {
    orgName: organizationsParsed[0].name,
    total: organizationsParsed.length,
  };
}
