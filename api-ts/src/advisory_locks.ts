import { z } from "zod";
import { IS_MERGE_FIELD } from "@braintrust/core";
import { SIPHASH_KEY } from "./hash";
import { LogPGConnection } from "./db/pg";
import { PoolingBtPg, PoolingBtPgClient } from "@braintrust/local/bt-pg";

const siphash = require("siphash");

export type AdvisoryLocks = {
  pg_migration: number;
  clickhouse_etl: number;
  brainstore_etl: number;
  brainstore_etl_modify_tracked_objects: number;
  brainstore_etl_only_historcal: number;
  brainstore_etl_only_nonhistorcal: number;
  min_merge_lock: number;
  max_merge_lock: number;
};

type StoredAdvisoryLocks = Omit<AdvisoryLocks, "max_merge_lock"> &
  Partial<Pick<AdvisoryLocks, "max_merge_lock">>;

const storedAdvisoryLocks: StoredAdvisoryLocks = {
  pg_migration: 0,
  clickhouse_etl: 1,
  brainstore_etl: 2,
  brainstore_etl_modify_tracked_objects: 3,
  brainstore_etl_only_historcal: 4,
  brainstore_etl_only_nonhistorcal: 5,
  min_merge_lock: 50,
};

export async function advisoryLockInfo(
  conn: PoolingBtPg | PoolingBtPgClient | LogPGConnection,
): Promise<AdvisoryLocks> {
  let maxMergeLock = storedAdvisoryLocks.max_merge_lock;
  if (maxMergeLock === undefined) {
    // Obtained from
    // https://dba.stackexchange.com/questions/285579/determine-maximum-advisory-locks-supported-by-postgres#:~:text=According%20to%20the%20Postgres%20documentation,configuration%20variables%20max_locks_per_transaction%20and%20max_connections
    const queryText = `
        select (current_setting('max_locks_per_transaction')::int) *
                (current_setting('max_connections')::int +
                current_setting('max_prepared_transactions')::int) as result
      `;
    // Typescript cannot reconcile the different signatures of Pool/PoolClient
    // and LogPGConnection for whatever reason. So we call it in different
    // branches.
    const queryRes = await (async () => {
      if (conn instanceof LogPGConnection) {
        return await conn.query(queryText);
      } else {
        return await conn.query(queryText);
      }
    })();
    maxMergeLock = z.number().parse(queryRes.rows[0]["result"]);
    storedAdvisoryLocks.max_merge_lock = maxMergeLock;
  }
  return { ...storedAdvisoryLocks, max_merge_lock: maxMergeLock };
}

export function computeAdvisoryLockIds({
  rows,
  minLockId,
  maxLockId,
}: {
  rows: Readonly<{
    id: string;
    objectType: string;
    objectId: string;
    [IS_MERGE_FIELD]: boolean;
  }>[];
  minLockId: number;
  maxLockId: number;
}): number[] {
  if (minLockId >= maxLockId) {
    throw new Error(
      `minLockId ${minLockId} must be less than maxLockId ${maxLockId}`,
    );
  }
  const allHashes = new Set<number>();
  rows.forEach(({ id, objectType, objectId, [IS_MERGE_FIELD]: is_merge }) => {
    if (is_merge) {
      allHashes.add(
        (siphash.hash_uint(SIPHASH_KEY, [objectType, objectId, id].join(":")) %
          (maxLockId - minLockId)) +
          minLockId,
      );
    }
  });
  const ret: number[] = [...allHashes.keys()];
  ret.sort((a, b) => a - b);
  return ret;
}
