import { GraphData } from "@braintrust/core/typespecs";

export function findLoops(graph: GraphData): Set<string> {
  // Map of node IDs to their outgoing edges
  const nodeToOutgoingEdges = new Map<
    string,
    Array<{ edgeId: string; targetNode: string }>
  >();

  // Set to collect all edges that form cycles
  const cycleEdges = new Set<string>();

  // First, detect direct self-loops (node to itself)
  Object.entries(graph.edges).forEach(([edgeId, edge]) => {
    const sourceNodeId = edge.source.node;
    const targetNodeId = edge.target.node;

    // Check for self-loop
    if (sourceNodeId === targetNodeId) {
      cycleEdges.add(edgeId);
    }

    // Build adjacency list
    if (!nodeToOutgoingEdges.has(sourceNodeId)) {
      nodeToOutgoingEdges.set(sourceNodeId, []);
    }
    nodeToOutgoingEdges.get(sourceNodeId)!.push({
      edgeId,
      targetNode: targetNodeId,
    });
  });

  // Set of unvisited, visiting, and visited nodes
  const unvisited = new Set(Object.keys(graph.nodes));
  const visiting = new Set<string>();
  const visited = new Set<string>();

  // Recursive DFS function
  function dfs(
    node: string,
    path: Array<{ node: string; edgeId: string }> = [],
  ): boolean {
    // Move node from unvisited to visiting
    unvisited.delete(node);
    visiting.add(node);

    // Check outgoing edges
    const outgoingEdges = nodeToOutgoingEdges.get(node) || [];

    for (const { edgeId, targetNode } of outgoingEdges) {
      // If target node is already visited, skip
      if (visited.has(targetNode)) {
        continue;
      }

      // If target node is being visited (in the current path), we found a cycle
      if (visiting.has(targetNode)) {
        // Find the start of the cycle in our path
        const cycleStartIndex = path.findIndex(
          (item) => item.node === targetNode,
        );

        // Add all edges in the cycle to our result
        if (cycleStartIndex !== -1) {
          // Add edges from the path that are part of the cycle
          for (let i = cycleStartIndex; i < path.length; i++) {
            cycleEdges.add(path[i].edgeId);
          }
          // Add the current edge that completes the cycle
          cycleEdges.add(edgeId);
        }
        continue;
      }

      // Add the current node and edge to our path
      const newPath = [...path, { node, edgeId }];

      // Recursively visit the target node
      dfs(targetNode, newPath);
    }

    // Move node from visiting to visited
    visiting.delete(node);
    visited.add(node);

    return false;
  }

  // Run DFS from each unvisited node
  while (unvisited.size > 0) {
    const node = Array.from(unvisited)[0];
    // Start with an empty path for the first node in each connected component
    dfs(node, []);
  }

  // Convert to the required return format
  return cycleEdges;
}
