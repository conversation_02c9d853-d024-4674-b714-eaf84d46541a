import { Readable } from "node:stream";
import { BadRequestError, InternalServerError } from "../util";
import { getPG } from "../db/pg";
import { CODE_BUNDLE_BUCKET_NAME } from "../env";
import { createGunzip } from "node:zlib";
import { makeObjectStore } from "../object-storage/object-storage";
import { z } from "zod";

export async function loadBundledCode({
  bundleId,
}: {
  bundleId: string;
}): Promise<{
  bundlePath: string;
}> {
  const db = getPG();
  const bundleInfo = await db.query(
    `SELECT
        path
      FROM
        code_bundles
      WHERE
        id = $1`,
    [bundleId],
  );

  if (bundleInfo.rowCount === 0) {
    throw new InternalServerError(
      `Bundle ${bundleId} not found (while loading bundle)`,
    );
  }
  const { path: bundlePath } = z
    .object({ path: z.string() })
    .parse(bundleInfo.rows[0]);

  return {
    bundlePath,
  };
}

export async function fetchBundle({
  bundlePath,
}: {
  bundlePath: string;
}): Promise<Readable> {
  const objectStore = await makeObjectStore();
  const { stream, contentEncoding } = await objectStore.get({
    bucket: CODE_BUNDLE_BUCKET_NAME!,
    key: bundlePath,
  });

  let requestStream = stream;

  if (contentEncoding === "gzip") {
    requestStream = requestStream.pipe(createGunzip());
  } else if (contentEncoding) {
    throw new BadRequestError(
      `Unsupported Content-Encoding: ${contentEncoding}`,
    );
  }

  return requestStream;
}
