import {
  ASYNC_SCORING_CONTROL_FIELD,
  getObj<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ath,
  SKIP_ASYNC_SCORING_FIELD,
  SpanComponentsV3,
  SpanObjectTypeV3,
  SpanType,
} from "@braintrust/core";
import {
  chatCompletionMessageParamSchema,
  functionDataSchema,
  FunctionId,
  InvokeFunctionRequest,
  Message,
  runEvalSchema,
  scoreSchema,
  SSEProgressEventData,
  sseProgressEventDataSchema,
  stopFunctionSchema,
} from "@braintrust/core/typespecs";
import { CallEventSchema } from "@braintrust/core/typespecs";
import {
  apiVersionSchema,
  DEFAULT_FUNCTION_CACHE_TTL,
  InvocableFunction,
  invocableFunctionSchema,
  INVOKE_API_VERSION,
  invokeAsyncBatchRequestSchema,
  invokeAsyncInputRefSchema,
  invokeRequestSchema,
  InvokeResponse,
  scoreToScoreObject,
  writeSSEEvent,
} from "@braintrust/local/functions";
import { ORG_NAME_HEADER, ProxyBadRequestError } from "@braintrust/proxy";
import { Scorer } from "autoevals";
import {
  BraintrustState,
  currentSpan,
  Eval,
  EvalCase,
  FailedHTTPResponse,
  initDataset,
  loginToState,
  Span,
  startSpan,
  updateSpan,
} from "braintrust";
import { InternalServerError as InternalServerErrorOpenAI } from "openai";
import { z } from "zod";
import { parseBraintrustAuthHeader, parseHeader } from "../auth-header";
import { extractAllowedOrigin, ORIGIN_HEADER } from "../cors";
import { customFetch } from "../custom_fetch";
import {
  BRAINSTORE_REALTIME_WAL_BUCKET_NAME,
  BRAINTRUST_APP_URL,
  ENABLE_INVOKE_RATE_LIMIT,
  INVOKE_RATE_LIMIT_PER_10S,
} from "../env";
import { getFunctionSecretCacheKeyPrefixes } from "../function-secrets";
import { useFunction } from "../functions";
import { canUseLambdaQuarantine } from "../lambda-quarantine/pool";
import { OBJECT_CACHE } from "../object_cache";
import { PENDING_FLUSHABLES } from "../pending_flushables";
import {
  AccessDeniedError,
  BadRequestError,
  BT_FUNCTION_LOGIN_CACHE_HEADER,
  BT_FUNCTION_META_CACHE_HEADER,
  extractErrorText,
  ForbiddenError,
  HTTPError,
  InternalServerError,
  NotFoundError,
  objectTypeToAclObjectType,
  TooManyRequestsError,
  wrapZodError,
} from "../util";
import {
  callFunctionWrapper,
  devNullWritableNode,
  invokeFunction,
  makePromptInputArg,
} from "./call";
import { wrapperCodeHash } from "./code-wrappers";
import { encryptedGet, encryptedPut, GetResFn, sha256Digest } from "./proxy";
import { createChannelListener, getRedis } from "../redis";
import { handleUploadCode } from "./register-code";
import { finish, writeTo } from "./request";
import { handleWarmupCodeExecution } from "./warmup";
import { constructPrefixSetAndCacheKeys } from "../prompt_cache_invalidation";
import { replacePayloadWithAttachments } from "./attachment-wrapper";
import { checkTokenAuthorized } from "../token_auth";
import { getTracer, logCounter } from "../instrumentation/api";
import { SpanStatusCode } from "@opentelemetry/api";
import { getLogger } from "../instrumentation/logger";
import { checkRateLimit } from "../rate-limit";
import { ROW_REF_FIELD } from "@braintrust/local";
import { makeObjectStore } from "../object-storage/object-storage";
import { readableToString } from "../stream_util";
import { NoopSpanWithState } from "./noop-span";

export const functionPrefix = "/function";

export async function handleFunction({
  url,
  headers,
  body,
  setHeader,
  setStatusCode,
  getRes,
  proxyUrl,
}: {
  url: string;
  headers: Record<string, string>;
  body: unknown;
  setHeader: (name: string, value: string) => void;
  setStatusCode: (code: number) => void;
  getRes: GetResFn;
  proxyUrl: string;
}): Promise<void> {
  const relativeURL = url
    .slice(functionPrefix.length)
    .split("/")
    .filter((x) => x.trim() !== "");

  if (relativeURL.length !== 1) {
    return finish({
      getRes,
      setStatusCode,
      statusCode: 404,
      message: "Not found",
    });
  }

  const taskName = relativeURL[0];
  return handleFunctionTask({
    taskName,
    headers,
    body,
    setHeader,
    setStatusCode,
    getRes,
    proxyUrl,
  });
}

export async function handleFunctionTask({
  taskName,
  headers,
  body,
  setHeader,
  setStatusCode,
  getRes,
  proxyUrl,
}: {
  taskName: string;
  headers: Record<string, string>;
  body: unknown;
  setHeader: (name: string, value: string) => void;
  setStatusCode: (code: number) => void;
  getRes: GetResFn;
  proxyUrl: string;
}) {
  try {
    switch (taskName) {
      case "invoke":
      case "call": {
        return await handleInvoke({
          headers,
          body,
          setHeader,
          setStatusCode,
          getRes,
          proxyUrl,
        });
      }
      case "invoke-async-batch": {
        return await handleInvokeAsyncBatch({
          headers,
          body,
          proxyUrl,
          setStatusCode,
          getRes,
        });
      }
      case "eval":
        return await handleEval({
          headers,
          proxyUrl,
          body,
          setHeader,
          setStatusCode,
          getRes,
        });
      case "code":
        return await handleUploadCode({
          headers,
          body,
          setHeader,
          setStatusCode,
          getRes,
        });
      case "warmup-code-execution":
        return await handleWarmupCodeExecution({
          headers,
          setHeader,
          setStatusCode,
          getRes,
        });
      case "stop":
        return await handleStop({
          headers,
          body,
          setStatusCode,
          getRes,
        });
      default:
        return finish({
          getRes,
          setStatusCode,
          statusCode: 404,
          message: "Not found",
        });
    }
  } catch (err) {
    let statusCode: number;
    let message: string;
    if (err instanceof HTTPError) {
      statusCode = err.status;
      message = err.message;
    } else if (
      err instanceof ForbiddenError ||
      err instanceof AccessDeniedError
    ) {
      statusCode = 403;
      message = err.message;
    } else if (
      err instanceof BadRequestError ||
      err instanceof ProxyBadRequestError
    ) {
      statusCode = 400;
      message = err.message;
    } else if (err instanceof NotFoundError) {
      statusCode = 404;
      message = err.message;
    } else if (err instanceof TooManyRequestsError) {
      setHeader("Retry-After", err.tryAfter.toString());
      statusCode = 429;
      message = err.message;
    } else if (
      err instanceof InternalServerErrorOpenAI ||
      err instanceof InternalServerError
    ) {
      getLogger().error("INTERNAL SERVER ERROR");
      statusCode = 500;
      message = err.message;
    } else {
      getLogger().error("INTERNAL SERVER ERROR");
      statusCode = 500;
      message =
        err && typeof err === "object" && "message" in err
          ? `${err.message}`
          : `${err}`;
    }

    // This is just a dummy span to capture the error context
    getTracer().startActiveSpan("error", (span) => {
      try {
        span.setAttribute("error.status", statusCode);
        span.setAttribute("error.message", message);
        span.setStatus({
          code: SpanStatusCode.ERROR,
          message,
        });
        if (err instanceof Error) {
          span.recordException(err);
        }
      } finally {
        span.end();
      }
    });

    return finish({
      getRes,
      setStatusCode,
      statusCode,
      message,
    });
  }
}

async function handleInvoke({
  headers,
  proxyUrl,
  body: bodyRaw,
  setHeader,
  setStatusCode: setStatusCodeArg,
  getRes,
  cachedRowRefs,
}: {
  headers: Record<string, string>;
  proxyUrl: string;
  body: unknown;
  setHeader: (name: string, value: string) => void;
  setStatusCode: (code: number) => void;
  getRes: GetResFn;
  cachedRowRefs?: Map<string, unknown>;
}): Promise<void> {
  cachedRowRefs = cachedRowRefs ?? new Map<string, unknown>();
  const appOrigin = extractAllowedOrigin(headers[ORIGIN_HEADER]);
  const ctxToken = parseBraintrustAuthHeader(headers) ?? undefined;
  const orgNameHeader = parseHeader(headers, ORG_NAME_HEADER);
  const pino = getLogger().child({
    task: "invoke",
  });

  let hasSetStatusCode = false;
  const setStatusCode = (code: number) => {
    if (!hasSetStatusCode) {
      setStatusCodeArg(code);
      hasSetStatusCode = true;
    }
  };

  // DEPRECATION_NOTICE: "call" is the "legacy" endpoint. We can remove it soon.
  const parsedBody = invokeRequestSchema.safeParse(bodyRaw);
  if (!parsedBody.success) {
    pino.error(
      {
        error: parsedBody.error,
      },
      "Invalid request",
    );
    return finish({
      getRes,
      setStatusCode,
      statusCode: 400,
      message: "Invalid request",
    });
  }
  const body = parsedBody.data;
  const orgName = parsedBody.data.org_name ?? orgNameHeader;
  if (body.api_version > INVOKE_API_VERSION) {
    return finish({
      getRes,
      setStatusCode,
      statusCode: 505,
      message: `Unsupported API version ${body.api_version} (max ${INVOKE_API_VERSION}))`,
    });
  }

  const func = await cachedUseFunction({
    body,
    appOrigin,
    ctxToken,
    orgName,
    setHeader,
  });

  const state = await cachedLogin({
    token: ctxToken,
    appOrigin,
    orgName,
    setHeader,
  });

  logCounter({
    name: "api.function.invoke",
    value: 1,
    attributes: {
      org_id: state.orgId ?? undefined,
    },
  });

  const parent = parseParent(body.parent);

  const span = parent
    ? startSpan({
        state,
        name: func.name,
        type: functionSpanType(func),
        parent,
        spanAttributes: {
          remote: true,
          slug: func.slug,
        },
      })
    : // We rely on being able to access the state from the span in order to
      // download attachments.
      new NoopSpanWithState(state);

  body.input = await resolveRowRefInput({
    input: body.input,
    cachedRowRefs,
    parentSpan: span,
  });
  span.log(
    replacePayloadWithAttachments(
      {
        input: body.input,
        expected: body.expected ?? undefined,
        metadata: body.metadata ?? undefined,
        tags: body.tags ?? undefined,
      },
      state,
      {},
    ),
  );

  const requestId = span.id;
  const spanExport = await span.export();
  if (requestId.trim() !== "") {
    setHeader("x-bt-span-id", requestId);
    setHeader("x-bt-span-export", spanExport);
  }

  const resWrapper = body.stream ? getRes : devNullWritableNode;

  if (body.stream) {
    setHeader("Content-Type", "text/event-stream");
  } else {
    setHeader("Content-Type", "application/json");
  }

  // Note: typescript is unable to recognize the variable assignments inside the
  // promise constructor as widening the type, so we use this workaround to
  // preserve the type information:
  // https://github.com/microsoft/TypeScript/issues/11498#issuecomment-1592811986.
  const flushablePromiseResolvers: {
    resolve:
      | ((value: InvokeResponse | PromiseLike<InvokeResponse>) => void)
      | null;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
    reject: ((reason?: any) => void) | null;
  } = { resolve: null, reject: null };
  const flushablePromise = new Promise<InvokeResponse>((resolve, reject) => {
    flushablePromiseResolvers.resolve = resolve;
    flushablePromiseResolvers.reject = reject;
  })
    // This is useful for testing flushing. At some point, we should introduce
    // random sleeps to stress test.
    //.then((response: InvokeResponse) => new Promise<InvokeResponse>((resolve) => setTimeout(() => resolve(response), 100)))
    .then(async (response: InvokeResponse) => {
      if (response.data !== undefined) {
        span.log({ output: response.data });
        (() => {
          if (!body.update_score) return;
          const scoreResult = scoreSchema.safeParse(response.data);
          if (!scoreResult.success) {
            span.log({
              error: `Cannot log ${JSON.stringify(response.data)} as a score`,
            });
            return;
          }
          const scoreObj = scoreToScoreObject(func.name, scoreResult.data);
          const spanSlug = new SpanComponentsV3({
            object_type:
              body.update_score.object_type === "experiment"
                ? SpanObjectTypeV3.EXPERIMENT
                : body.update_score.object_type === "playground_logs"
                  ? SpanObjectTypeV3.PLAYGROUND_LOGS
                  : SpanObjectTypeV3.PROJECT_LOGS,
            object_id: body.update_score.object_id,
            row_id: body.update_score.row_id,
            // These won't be used because we are updating an existing row
            // rather than creating a subspan. But something must be provided.
            span_id: body.update_score.row_id,
            root_span_id: body.update_score.row_id,
          }).toStr();
          updateSpan({
            exported: spanSlug,
            state,
            scores: {
              [scoreObj.name]: scoreObj.score,
            },
            [ASYNC_SCORING_CONTROL_FIELD]: {
              kind: "score_update",
              token: body.update_score.token,
            },
            [SKIP_ASYNC_SCORING_FIELD]: true,
          });
        })();
      }
      if (response.error !== undefined) {
        span.log({ error: response.error });
      }
      span.end();

      if (!body.stream) {
        await span.flush();
        setStatusCode(response.error ? 500 : 200);
        writeTo({
          getRes,
          message: response.error ?? JSON.stringify(response.data ?? null),
        });
      }
    })
    .catch((err) => {
      pino.error(`Error while invoking function: ${extractErrorText(err)}`);
      span.log({ error: `${err}` });
      span.end();
    });
  if (!flushablePromiseResolvers.resolve || !flushablePromiseResolvers.reject) {
    throw new Error("Impossible (flushablePromiseResolvers not set)");
  }

  PENDING_FLUSHABLES.add(waitFor({ flushable: flushablePromise, state }));
  try {
    // Dynamically call `getRes()` when the first write occurs, so that if a write
    // never occurs, ordinary error codes can be propagated correctly.
    let res: ReturnType<typeof resWrapper> | null = null;
    const writable = new WritableStream({
      async write(chunk) {
        if (!res) {
          res = resWrapper();
        }
        const ok = res.write(chunk);
        if (!ok) {
          // Backpressure
          await new Promise((resolve) => res!.once("drain", resolve));
        }
      },
      async close() {
        if (!res) {
          res = resWrapper();
        }
        res.end();
      },
    });

    await invokeFunction({
      func,
      input: body.input,
      hookData:
        body.expected || body.metadata
          ? {
              expected: body.expected,
              metadata: body.metadata,
            }
          : undefined,
      messages: body.messages,
      timeoutMs: body.timeout_ms,
      strict: body.strict,
      ctxToken,
      orgName,
      orgId: state.orgId ?? undefined,
      proxyUrl,
      appOrigin,
      span,
      mode: body.mode ?? undefined,
      setHeader,
      setStatusCode,
      writable,
      onFinal: flushablePromiseResolvers.resolve,
      purpose: func.function_type === "scorer" ? "scorer" : undefined,
    });
  } catch (e) {
    flushablePromiseResolvers.reject(e);
    throw e;
  }
}

async function handleInvokeAsyncBatch({
  headers,
  proxyUrl,
  body: bodyRaw,
  setStatusCode,
  getRes,
}: {
  headers: Record<string, string>;
  proxyUrl: string;
  body: unknown;
  setStatusCode: (code: number) => void;
  getRes: GetResFn;
}): Promise<void> {
  const asyncBody = wrapZodError(() =>
    invokeAsyncBatchRequestSchema.array().parse(bodyRaw),
  );
  // Create a cache for the subsequent invoke calls to share when resolving row refs.
  const cachedRowRefs = new Map<string, unknown>();
  const bodies = asyncBody.flatMap(
    ({ function_ids, non_id_args }): z.infer<typeof invokeRequestSchema>[] =>
      function_ids.map((functionId) => ({
        ...functionId,
        ...non_id_args,
      })),
  );
  const resolutions = await Promise.allSettled(
    bodies.map((body) =>
      handleInvoke({
        headers,
        proxyUrl,
        body,
        setHeader: () => {},
        setStatusCode: () => {},
        getRes: devNullWritableNode,
        cachedRowRefs,
      }),
    ),
  );
  resolutions.forEach((resolution) => {
    if (resolution.status === "rejected") {
      throw resolution.reason;
    }
  });
  // Return a success status.
  finish({
    getRes,
    setStatusCode,
    statusCode: 200,
    message: JSON.stringify({ status: "success" }),
  });
}

export async function cachedUseFunction({
  body,
  appOrigin,
  ctxToken,
  orgName,
  setHeader,
}: {
  body: FunctionId;
  appOrigin: string;
  ctxToken: string | undefined;
  orgName: string | undefined;
  setHeader: (name: string, value: string) => void;
}): Promise<InvocableFunction> {
  const functionCacheKeys = constructPrefixSetAndCacheKeys({
    prefix: functionCacheKeyPrefix(body),
    version: "version" in body && body.version ? body.version : undefined,
    token: ctxToken,
  });

  if (ENABLE_INVOKE_RATE_LIMIT) {
    await checkRateLimit({
      appOrigin,
      authToken: ctxToken,
      key: functionCacheKeys.cacheKey,
      // Configurable rate limit per 10 seconds
      points: INVOKE_RATE_LIMIT_PER_10S,
      duration: 10,
      context: "function invocation",
      enforce: true,
    });
  }

  const functionInfoKey = functionCacheKeys.cacheKey;
  const encryptionKey = sha256Digest(functionCacheKeys.cacheKey);
  const pino = getLogger();

  let invocableFunction: InvocableFunction | null = null;
  const functionInfoResp = await encryptedGet(encryptionKey, functionInfoKey);
  if (functionInfoResp) {
    const parsed = invocableFunctionSchema.safeParse(
      JSON.parse(functionInfoResp),
    );
    if (parsed.success) {
      invocableFunction = fillCachedFunctionValue(body, parsed.data);
      setHeader(BT_FUNCTION_META_CACHE_HEADER, "HIT");
    } else {
      pino.warn(
        {
          error: parsed.error,
        },
        "Failed to parse cached invocable function",
      );
    }
  }

  if (!invocableFunction) {
    setHeader(BT_FUNCTION_META_CACHE_HEADER, "MISS");
    // NOTE: We may eventually want to call this through an API instead, but for now, let's call it directly.
    // This essentially constrains this proxy to be on the same on-prem network as the API.
    invocableFunction = await useFunction({
      body,
      appOrigin,
      ctxToken,
      orgName,
    });

    const secretSets = getFunctionSecretCacheKeyPrefixes({
      orgId: invocableFunction.org_id,
      projectId: invocableFunction.project_id,
      functionId: invocableFunction.id,
    }).map((prefix) => ({
      name: prefix,
      value: functionInfoKey,
    }));

    let expiresIn: number;
    if (
      invocableFunction.invoke_method &&
      "expiresAt" in invocableFunction.invoke_method
    ) {
      expiresIn =
        invocableFunction.invoke_method.expiresAt - new Date().getTime() / 1000;
    } else {
      expiresIn = DEFAULT_FUNCTION_CACHE_TTL;
    }

    if (expiresIn > 1) {
      encryptedPut(
        encryptionKey,
        functionInfoKey,
        JSON.stringify(cacheableFunctionValue(body, invocableFunction)),
        {
          ttl: expiresIn,
          extraSetOps: [
            ...secretSets,
            ...(functionCacheKeys.prefixSetKey
              ? [
                  {
                    name: functionCacheKeys.prefixSetKey,
                    value: functionInfoKey,
                  },
                ]
              : []),
          ],
        },
      ).catch((e) => {
        pino.error(
          {
            error: e,
          },
          "Error while caching function",
        );
      });
    }
  }

  return invocableFunction;
}

async function waitFor({
  flushable,
  state,
}: {
  flushable: Promise<unknown>;
  state: BraintrustState;
}) {
  try {
    await flushable;
  } catch (err) {
    getLogger().warn("Error surfaced while awaiting final state", err);
  }
  try {
    await state.bgLogger().flush();
  } catch (err) {
    getLogger().warn("Error surfaced while flushing", err);
  }
}

export async function cachedLogin({
  appOrigin,
  token,
  orgName,
  setHeader,
  onFlushError,
}: {
  appOrigin: string;
  token: string | undefined;
  orgName: string | undefined;
  setHeader: (name: string, value: string) => void;
  onFlushError?: (error: unknown) => void;
}) {
  const encryptionKey = sha256Digest(
    JSON.stringify({ token: token ?? "anon", orgName }),
  );

  let state: BraintrustState;
  const stateResp = await encryptedGet(encryptionKey, encryptionKey);
  if (stateResp) {
    setHeader(BT_FUNCTION_LOGIN_CACHE_HEADER, "HIT");
    state = BraintrustState.deserialize(JSON.parse(stateResp), {
      noExitFlush: true,
      fetch: customFetch,
      onFlushError,
    });
  } else {
    setHeader(BT_FUNCTION_LOGIN_CACHE_HEADER, "MISS");
    state = await loginToState({
      apiKey: token,
      // If the app URL is explicitly set to an env var, it's meant to override
      // the origin.
      appUrl: BRAINTRUST_APP_URL ?? appOrigin,
      orgName,
      noExitFlush: true,
      fetch: customFetch,
      onFlushError,
    });

    encryptedPut(
      encryptionKey,
      encryptionKey,
      JSON.stringify(state.serialize()),
      {
        ttl: DEFAULT_FUNCTION_CACHE_TTL,
      },
    ).catch((e) => {
      getLogger().error(
        {
          error: e,
        },
        "Error while caching login credentials",
      );
    });
  }

  return state;
}

export const evalRequestSchema = runEvalSchema.and(apiVersionSchema);

// This is longer than the default lambda 30s timeout. We don't have the same CDN/origin
// 60s timeout when invoking functions in an Eval, so we can be more generous. 4 minutes
// is what Cloudflare's timeout is (240s), so this seems like a good number.
const EVAL_TIMEOUT_MS = 1000 * 60 * 4; // 4 minutes

async function handleEval({
  headers,
  proxyUrl,
  body: bodyRaw,
  setHeader,
  setStatusCode,
  getRes,
}: {
  headers: Record<string, string>;
  proxyUrl: string;
  body: unknown;
  setHeader: (name: string, value: string) => void;
  setStatusCode: (code: number) => void;
  getRes: GetResFn;
}) {
  const pino = getLogger().child({
    task: "eval",
  });

  const parsedBody = evalRequestSchema.safeParse(bodyRaw);
  if (!parsedBody.success) {
    pino.error(
      {
        error: parsedBody.error,
      },
      "Invalid request",
    );
    return finish({
      getRes,
      setStatusCode,
      statusCode: 400,
      message: "Invalid request",
    });
  }
  const body = parsedBody.data;
  if (body.api_version > INVOKE_API_VERSION) {
    return finish({
      getRes,
      setStatusCode,
      statusCode: 505,
      message: `Unsupported API version ${body.api_version} (max ${INVOKE_API_VERSION}))`,
    });
  }

  const appOrigin = extractAllowedOrigin(headers[ORIGIN_HEADER]);
  const ctxToken = parseBraintrustAuthHeader(headers) ?? undefined;
  const orgName = parseHeader(headers, ORG_NAME_HEADER);

  const parent = parseParent(body.parent);

  const datasetInfo:
    | { project: string; dataset: string }
    | { data: unknown[] } =
    "project_name" in body.data
      ? {
          project: body.data.project_name,
          dataset: body.data.dataset_name,
        }
      : "dataset_id" in body.data
        ? await getDatasetById({
            ctxToken,
            appOrigin,
            datasetId: body.data.dataset_id,
          })
        : body.data;

  const devNull = devNullWritableNode();
  let res = devNull;
  const runEval = async () => {
    const onFlushError = (err: unknown) => {
      let errorText: string;
      if (err instanceof FailedHTTPResponse) {
        errorText = `${err.data}`;
      } else {
        errorText = `${err}`;
      }

      writeSSEEvent(res, {
        event: "error",
        data: JSON.stringify(errorText),
      });
    };
    const state = await cachedLogin({
      token: ctxToken,
      appOrigin,
      orgName,
      setHeader,
      onFlushError,
    });
    const orgId = state.orgId ?? undefined;

    const taskFunction = await cachedUseFunction({
      body: body.task,
      appOrigin,
      ctxToken,
      orgName,
      // We do not want to set the Eval() results header to say the task function was cached
      setHeader: () => {},
    });

    const scoreFunctions = await Promise.all(
      body.scores.map((score) =>
        cachedUseFunction({
          body: score,
          appOrigin,
          ctxToken,
          orgName,
          setHeader: () => {},
        }),
      ),
    );

    const scores = scoreFunctions.map((score) => {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- MISSING JUSTIFICATION
      const ret = async (input: any) => {
        return await callFunctionWrapper({
          func: score,
          // `input` already has input, metadata, and expected fields, so we don't need to add them here
          input,
          hookData: undefined,
          messages: undefined,
          strict: body.strict ?? undefined,
          span: currentSpan(),
          ctxToken,
          orgName,
          orgId,
          proxyUrl,
          appOrigin,
          purpose: "scorer",
          timeoutMs: EVAL_TIMEOUT_MS,
        });
      };
      Object.defineProperties(ret, {
        name: { value: functionName(score) },
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      return ret as Scorer<any, any>;
    });

    if (body.stream) {
      res = getRes();
    }
    pino.info(
      {
        projectId: body.project_id,
        experimentName: body.experiment_name,
      },
      "Starting experiment",
    );

    // This logic is roughly duplicated in sdk/js/dev/server.ts, so if you change it, make sure
    // to take a look at that code as well.

    const abortController = new AbortController();
    const stopToken = body.stop_token;
    const stopListener = stopToken ? createChannelListener(stopToken) : null;
    const evalPromise = Eval(
      "worker-thread",
      {
        data:
          "project" in datasetInfo
            ? initDataset({
                ...datasetInfo,
                state,
                _internal_btql:
                  "_internal_btql" in body.data
                    ? (body.data._internal_btql ?? undefined)
                    : undefined,
              })
            : // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
              (datasetInfo.data as EvalCase<unknown, unknown, unknown>[]),
        task: async (input, { span, metadata, expected, reportProgress }) => {
          const hookData = {
            metadata,
            expected,
          };
          let messages: Message[] | undefined = undefined;
          if (body.extra_messages) {
            const inputArg = makePromptInputArg({ input, hookData });
            messages = renderExtraMessages(body.extra_messages, inputArg);
          }

          const ret = await callFunctionWrapper({
            func: taskFunction,
            input,
            hookData: {
              metadata,
              expected,
            },
            messages,
            strict: body.strict ?? undefined,
            span,
            ctxToken,
            orgName,
            orgId,
            proxyUrl,
            appOrigin,
            sendProgress: (event: CallEventSchema) => {
              const progressEvent =
                sseProgressEventDataSchema.shape.event.safeParse(event.event);
              if (!progressEvent.success) {
                pino.info(
                  {
                    event,
                  },
                  "SKIPPING EVENT",
                );
              }
              if (progressEvent.success) {
                reportProgress({
                  format: "llm",
                  output_type: "completion",
                  event: progressEvent.data,
                  data: event.data,
                });
              }
            },
            timeoutMs: EVAL_TIMEOUT_MS,
          });

          return ret;
        },
        scores,
        experimentName: body.experiment_name,
        metadata: body.metadata,
        projectId: body.project_id,
        state,
        trialCount: body.trial_count ?? undefined,
        isPublic: body.is_public ?? undefined,
        timeout: body.timeout ?? undefined,
        maxConcurrency: body.max_concurrency ?? undefined,
        baseExperimentName: body.base_experiment_name ?? undefined,
        baseExperimentId: body.base_experiment_id ?? undefined,
        gitMetadataSettings: body.git_metadata_settings ?? undefined,
        repoInfo: body.repo_info ?? undefined,
        signal: abortController.signal,
      },
      {
        // Avoid printing the bar to the console
        progress: {
          start: (name, total) => {},
          stop: () => {},
          increment: (name) => {},
        },
        stream: (data: SSEProgressEventData) => {
          writeSSEEvent(res, {
            event: "progress",
            data: JSON.stringify(data),
          });
        },
        onStart: (metadata) => {
          writeSSEEvent(res, {
            event: "start",
            data: JSON.stringify(metadata),
          });
        },
        parent,
      },
    );

    await Promise.race([
      evalPromise,
      ...(stopListener ? [stopListener.promise] : []),
    ]);
    if (stopListener?.stopped()) {
      abortController.abort();
      state.disable();
      pino.info(
        {
          projectId: body.project_id,
          experimentName: body.experiment_name,
        },
        "Stopping experiment",
      );
      throw new Error("AbortError");
    }
    if (stopListener) {
      await stopListener.cleanup();
    }

    pino.info(
      {
        projectId: body.project_id,
        experimentName: body.experiment_name,
      },
      "Flushing experiment",
    );
    await state.bgLogger().flush();
    pino.info(
      {
        projectId: body.project_id,
        experimentName: body.experiment_name,
      },
      "Flushed experiment",
    );

    const summary = await evalPromise;
    return summary.summary;
  };

  let result;
  try {
    result = await runEval();
  } catch (err) {
    if (res === devNull) {
      res = getRes();
    }

    const isAbortError = err instanceof Error && err.message === "AbortError";
    if (!isAbortError) {
      pino.error(
        {
          error: err,
        },
        "Error running eval",
      );
      writeSSEEvent(res, {
        event: "error",
        data: JSON.stringify(`${err}`),
      });
    }
    writeSSEEvent(res, {
      event: "done",
      data: "",
    });
    res.end();
    return;
  }

  if (body.stream) {
    writeSSEEvent(res, {
      event: "summary",
      data: JSON.stringify(result),
    });
    writeSSEEvent(res, {
      event: "done",
      data: "",
    });
    res.end();
  } else {
    return finish({
      getRes,
      setStatusCode,
      statusCode: 200,
      message: JSON.stringify(result),
    });
  }
}

async function handleStop({
  headers,
  body,
  setStatusCode,
  getRes,
}: {
  headers: Record<string, string>;
  body: unknown;
  setStatusCode: (code: number) => void;
  getRes: GetResFn;
}) {
  const parsedBody = stopFunctionSchema.safeParse(body);
  if (!parsedBody.success) {
    getLogger().error(
      {
        error: parsedBody.error,
      },
      "Invalid request",
    );
    return finish({
      getRes,
      setStatusCode,
      statusCode: 400,
      message: "Invalid request",
    });
  }

  const appOrigin = extractAllowedOrigin(headers[ORIGIN_HEADER]);
  const ctxToken = parseBraintrustAuthHeader(headers);
  await checkTokenAuthorized({ ctxToken, appOrigin });

  const stopToken = parsedBody.data.stop_token;

  const parts = stopToken.split(":");
  if (parts.length !== 3) {
    return finish({
      getRes,
      setStatusCode,
      statusCode: 400,
      message: "Invalid stop token",
    });
  }

  const [prefix, promptSessionId, _] = parts;
  if (prefix !== "stop_function") {
    return finish({
      getRes,
      setStatusCode,
      statusCode: 400,
      message: "Invalid stop token",
    });
  }

  await checkPromptSessionBelongsToUser({
    ctxToken,
    appOrigin,
    promptSessionId,
  });

  const redis = await getRedis();
  await redis.publish(stopToken, "stop");

  return finish({
    getRes,
    setStatusCode,
    statusCode: 200,
    message: "Stop token set",
  });
}

export async function checkPromptSessionBelongsToUser({
  ctxToken,
  appOrigin,
  promptSessionId,
}: {
  ctxToken: string | undefined;
  appOrigin: string;
  promptSessionId: string;
}) {
  const { aclObjectType, overrideRestrictObjectType } =
    objectTypeToAclObjectType("prompt_session");
  const aclCheck = await OBJECT_CACHE.checkAndGet({
    appOrigin,
    authToken: ctxToken,
    aclObjectType,
    overrideRestrictObjectType,
    objectId: promptSessionId,
  });
  if (!aclCheck) {
    throw new AccessDeniedError({
      permission: "read",
      aclObjectType,
      overrideRestrictObjectType,
      objectId: promptSessionId,
    });
  }
}

export function functionCacheKeyPrefix(functionId: FunctionId) {
  if ("function_id" in functionId) {
    return `use_function_id:${functionId.function_id}`;
  } else if ("prompt_session_id" in functionId) {
    // Prompt sessions use roughly the same format as functions, but are prefixed by the prompt session ID
    // instead of the individual prompt's id.
    return `use_function_id:${functionId.prompt_session_id}:${functionId.prompt_session_function_id}`;
  } else if ("project_name" in functionId) {
    return `use_function_project_name_slug:${functionId.project_name}:${functionId.slug}`;
  } else if ("global_function" in functionId) {
    // NOTE: This is never used
    return `use_function_global:${functionId.global_function}`;
  } else if ("inline_context" in functionId) {
    const isLambda = canUseLambdaQuarantine();
    const hash = wrapperCodeHash({
      runtimeSpec: functionId.inline_context,
      inline: true,
      lambda: isLambda,
    });
    return `use_function_inline_code:${JSON.stringify(functionId.inline_context)}:${hash}:${isLambda ? "lambda" : ""}`;
  } else if ("inline_function" in functionId) {
    return `use_function_inline_function`;
  } else if ("inline_prompt" in functionId) {
    return `use_function_inline_prompt`;
  } else {
    const x: never = functionId;
    throw new Error(`Invalid function id ${JSON.stringify(x)}`);
  }
}

// When we cache function credentials, we don't always need or want to cache the whole thing. For
// example, for inline code, we don't want to cache the code itself, since we share cached values
// across different values of inline code.
function cacheableFunctionValue(
  req: InvokeFunctionRequest,
  func: InvocableFunction,
): InvocableFunction {
  if (
    func.function_data.type === "code" &&
    func.function_data.data.type === "inline" &&
    "code" in req
  ) {
    return {
      ...func,
      function_data: {
        ...func.function_data,
        data: {
          ...func.function_data.data,
          code: "",
        },
      },
    };
  } else {
    return func;
  }
}

function fillCachedFunctionValue(
  req: FunctionId,
  func: InvocableFunction,
): InvocableFunction {
  if (
    func.function_data.type === "code" &&
    func.function_data.data.type === "inline" &&
    "code" in req
  ) {
    return {
      ...func,
      function_data: {
        ...func.function_data,
        data: {
          ...func.function_data.data,
          code: req.code,
        },
      },
    };
  } else if ("inline_function" in req) {
    return {
      ...func,
      prompt_data: req.inline_prompt,
      function_data: functionDataSchema.parse(req.inline_function),
      name: req.name ?? func.name,
    };
  } else if ("inline_prompt" in req) {
    return {
      ...func,
      prompt_data: req.inline_prompt,
      function_data: {
        type: "prompt",
      },
      name: req.name ?? func.name,
    };
  } else {
    return func;
  }
}

function functionName(func: InvocableFunction) {
  return func.name;
}

async function getDatasetById({
  ctxToken,
  appOrigin,
  datasetId,
}: {
  ctxToken: string | undefined;
  appOrigin: string;
  datasetId: string;
}) {
  const { aclObjectType, overrideRestrictObjectType } =
    objectTypeToAclObjectType("dataset");
  const aclCheck = await OBJECT_CACHE.checkAndGet({
    appOrigin,
    authToken: ctxToken,
    aclObjectType,
    overrideRestrictObjectType,
    objectId: datasetId,
  });
  if (!aclCheck) {
    throw new AccessDeniedError({
      permission: "read",
      aclObjectType,
      overrideRestrictObjectType,
      objectId: datasetId,
    });
  }
  return {
    project: aclCheck.parent_cols.project.name,
    dataset: aclCheck.object_name,
  };
}

export function functionSpanType(func: InvocableFunction): SpanType {
  switch (func.function_type) {
    case "scorer":
      return "score";
    case "llm":
      return "llm";
    case "tool":
      return "tool";
  }
  // We should do something more granular, but for now this is how we
  // capture autoevals
  if (func.function_data.type === "global") {
    return "score";
  }

  return "function";
}

function parseParent(
  parent: InvokeFunctionRequest["parent"],
): string | undefined {
  return typeof parent === "string"
    ? parent
    : parent
      ? new SpanComponentsV3({
          object_type:
            parent.object_type === "experiment"
              ? SpanObjectTypeV3.EXPERIMENT
              : parent.object_type === "playground_logs"
                ? SpanObjectTypeV3.PLAYGROUND_LOGS
                : SpanObjectTypeV3.PROJECT_LOGS,
          object_id: parent.object_id,
          ...(parent.row_ids
            ? {
                row_id: parent.row_ids.id,
                span_id: parent.row_ids.span_id,
                root_span_id: parent.row_ids.root_span_id,
              }
            : {
                row_id: undefined,
                span_id: undefined,
                root_span_id: undefined,
              }),
          propagated_event: parent.propagated_event,
        }).toStr()
      : undefined;
}

function renderExtraMessages(
  extraMessages: string,
  context: unknown,
): Message[] {
  const path = extraMessages.split(".");

  let value = context;
  if (path.length > 0) {
    const contextAsRecord = z.record(z.unknown()).safeParse(context);
    if (!contextAsRecord.success) {
      throw new Error(`Variable '${extraMessages}' is not an object.`);
    }
    value = getObjValueByPath(contextAsRecord.data, path);
  }

  if (value === undefined) {
    throw new Error(`Variable '${extraMessages}' does not exist.`);
  }
  const parsed = wrapZodError(() =>
    z.array(chatCompletionMessageParamSchema).parse(value),
  );
  return parsed;
}

async function resolveRowRefInput({
  input,
  cachedRowRefs,
  parentSpan,
}: {
  input: unknown;
  // Maps the stringified row ref to the resolved value.
  cachedRowRefs: Map<string, unknown>;
  parentSpan: Span;
}): Promise<unknown> {
  const parsed = invokeAsyncInputRefSchema.safeParse(input);
  if (!parsed.success) {
    return input;
  }
  const doResolve = async () => {
    if (!BRAINSTORE_REALTIME_WAL_BUCKET_NAME) {
      throw new BadRequestError(
        "Cannot resolve row ref input without a bucket",
      );
    }
    const rowRef = parsed.data[ROW_REF_FIELD];
    const cacheKey = JSON.stringify(rowRef);
    const objectData = await (async () => {
      const cachedRowRef = cachedRowRefs.get(cacheKey);
      if (cachedRowRef !== undefined) {
        return cachedRowRef;
      }
      const object = await (
        await makeObjectStore()
      ).get({
        bucket: BRAINSTORE_REALTIME_WAL_BUCKET_NAME,
        key: rowRef.key,
        range: {
          offset: rowRef.byte_range_start,
          length: rowRef.byte_range_end - rowRef.byte_range_start,
        },
      });
      const objectStr = await readableToString(object.stream);
      const objectData = JSON.parse(objectStr);
      cachedRowRefs.set(cacheKey, objectData);
      return objectData;
    })();
    return Object.fromEntries(
      parsed.data.fields.map((field) => [field, objectData[field]]),
    );
  };

  // Don't log the resolved input here since it might be large.
  return await parentSpan.traced(
    async (span) => {
      try {
        return doResolve();
      } catch (e) {
        span.log({ error: extractErrorText(e) });
        throw e;
      }
    },
    {
      name: "Resolve Row Ref Input",
      event: { input: parsed.data[ROW_REF_FIELD] },
    },
  );
}
