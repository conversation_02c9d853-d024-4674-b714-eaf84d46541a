import { Request, Response } from "express";
import { z } from "zod";
import {
  AccessDeniedError,
  InternalServerError,
  NotFoundError,
  wrapZodError,
} from "./util";
import { getRequestContext } from "./request_context";
import { checkTokenAuthorized } from "./token_auth";
import { getPG } from "./db/pg";
import { encryptMessage } from "@braintrust/proxy/utils";
import { getServiceTokenSecretKey, decryptServiceToken } from "./cron/cron";
import { Me } from "@braintrust/local/app-schema";
import { BRAINTRUST_HOSTED_DATA_PLANE } from "./env";

const upsertServiceTokenRequest = z.object({
  name: z.string(),
  service_token: z.string(),
});

const hasServiceTokenPermission = (me: Me) => {
  return me.is_sysadmin && BRAINTRUST_HOSTED_DATA_PLANE;
};

export async function upsertServiceToken(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const params = wrapZodError(() => upsertServiceTokenRequest.parse(ctx.data));

  const { me } = await checkTokenAuthorized({
    ctxToken: ctx.token,
    appOrigin: ctx.appOrigin,
    checkSysadmin: true,
  });

  const meResult = await me;
  if (!hasServiceTokenPermission(meResult)) {
    throw new AccessDeniedError({
      permission: "sysadmin",
      objectType: "service-token",
      objectId: "status",
    });
  }

  await performUpsertServiceToken({
    name: params.name,
    serviceToken: params.service_token,
  });

  res.json({ success: true });
}

const SERVICE_TOKENS_TABLE_NAME = "service_tokens";

// NOTE: This function assumes you have already done ACL checks.
async function performUpsertServiceToken({
  name,
  serviceToken,
}: {
  name: string;
  serviceToken: string;
}) {
  const db = getPG();
  const serviceTokenEncrypted = await encryptMessage(
    await getServiceTokenSecretKey(),
    serviceToken,
  );
  await db.query(
    `
      insert into ${SERVICE_TOKENS_TABLE_NAME} (name, service_token_encrypted)
      values ($1::text, $2::text)
      on conflict (name)
      do update set service_token_encrypted = excluded.service_token_encrypted
    `,
    [name, serviceTokenEncrypted],
  );
}

async function getServiceToken(name: string): Promise<string | undefined> {
  const db = getPG();
  const { rows } = await db.query(
    `SELECT service_token_encrypted FROM ${SERVICE_TOKENS_TABLE_NAME} WHERE name = $1`,
    [name],
  );

  if (rows.length === 0) {
    return undefined;
  }

  if (rows.length > 1) {
    throw new InternalServerError(
      `Multiple service tokens found with name: ${name}`,
    );
  }

  const encryptedServiceToken = z
    .object({
      service_token_encrypted: z.string().nullish(),
    })
    .parse(rows[0]).service_token_encrypted;
  if (!encryptedServiceToken) {
    return undefined;
  }

  return await decryptServiceToken(encryptedServiceToken);
}

export async function headServiceTokenRequest(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const { name } = req.params;

  const { me } = await checkTokenAuthorized({
    ctxToken: ctx.token,
    appOrigin: ctx.appOrigin,
    checkSysadmin: true,
  });
  const meResult = await me;
  if (!hasServiceTokenPermission(meResult)) {
    throw new AccessDeniedError({
      permission: "sysadmin",
      objectType: "service-token",
      objectId: "status",
    });
  }

  const serviceToken = await getServiceToken(name);
  if (!serviceToken) {
    throw new NotFoundError(`Service token not found: ${name}`);
  }

  res.status(200).end();
}

export async function getServiceTokenRequest(req: Request, res: Response) {
  const ctx = getRequestContext(req);
  const { name } = req.params;

  const { me } = await checkTokenAuthorized({
    ctxToken: ctx.token,
    appOrigin: ctx.appOrigin,
    checkSysadmin: true,
  });
  const meResult = await me;
  if (!hasServiceTokenPermission(meResult)) {
    throw new AccessDeniedError({
      permission: "sysadmin",
      objectType: "service-token",
      objectId: "status",
    });
  }

  const serviceToken = await getServiceToken(name);
  if (!serviceToken) {
    throw new NotFoundError(`Service token not found: ${name}`);
  }

  res.json({ service_token: serviceToken });
}
