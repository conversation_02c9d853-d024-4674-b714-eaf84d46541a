import { Me } from "@braintrust/local/app-schema";
import { TELEMETRY_ENABLED } from "../env";
import { PENDING_FLUSHABLES } from "../pending_flushables";
import { checkTokenAuthorized } from "../token_auth";
import { logger } from "./logger";
import { reportTelemetry } from "./reportTelemetry";
import { AnyEvent, AnyEventSchema } from "./types";
import { makeError } from "@braintrust/local";

export function withTelemetry<TArgs extends unknown[], TReturn>(
  fn: (...args: TArgs) => Promise<TReturn>,
  generator: (event: {
    args: TArgs;
    stats: {
      duration_ms: number;
    };
    output: TReturn;
  }) => Promise<{
    events: (AnyEvent | undefined)[];
    token: string | undefined;
    appOrigin: string;
  }>,
): (...args: TArgs) => Promise<TReturn> {
  if (!TELEMETRY_ENABLED) {
    return fn;
  }

  return async (...args: TArgs): Promise<TReturn> => {
    const startTime = Date.now();

    const output = await fn(...args);

    PENDING_FLUSHABLES.add(
      (async () => {
        let config: Awaited<ReturnType<typeof generator>>;
        try {
          config = await generator({
            args,
            stats: {
              duration_ms: Date.now() - startTime,
            },
            output,
          });
        } catch (e) {
          logger.warn("Error generating config", { error: makeError(e) });
          return;
        }

        let events = config.events
          .map((event) => {
            const result = AnyEventSchema.safeParse(event);
            if (!result.success) {
              logger.warn("Invalid event", {
                error: result.error,
                event_name: event?.event_name,
              });
              return;
            }
            return result.data;
          })
          .filter((e) => e !== undefined);

        if (events.length) {
          let authInfo: Me = { id: "anon", organizations: [] };
          if (config.token) {
            try {
              authInfo = await (
                await checkTokenAuthorized({
                  ctxToken: config.token,
                  appOrigin: config.appOrigin,
                })
              ).me;
            } catch (e) {
              logger.warn("Error checking token", { error: makeError(e) });
            }
          }

          events = events.map((event) => ({
            ...event,
            properties: {
              ...event.properties,
              user_id: authInfo.id,
              app_origin: config.appOrigin,
              // TODO: we should include impersonation info, if any
            },
          }));

          await reportTelemetry({
            events,
            token: config.token,
          });
        }
      })(),
    );

    return output;
  };
}
