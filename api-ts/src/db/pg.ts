import {
  type BtQueryResult,
  PoolingBtPg,
  PoolingBtPgClient,
} from "@braintrust/local/bt-pg";
import { Pool as NodePgPool, types } from "pg";
import {
  CLICKHOUSE_PG_URL,
  IS_LOCAL,
  PG_URL,
  PG_POOL_CONFIG_MAX_NUM_CLIENTS,
  PG_URL_NOMODIFY,
} from "../env";
import { getLogger } from "../instrumentation/logger";

// Only set for boolean type. We'll handle numbers separately
types.setTypeParser(types.builtins.BOOL, (val) => val === "t");

// The driver does not parse 64-bit ints or numerics by default
types.setTypeParser(types.builtins.INT8, (val) => parseInt(val));
types.setTypeParser(types.builtins.NUMERIC, (val) => parseFloat(val));

const DATE_PARSING_TYPES = [
  types.builtins.DATE,
  types.builtins.TIME,
  types.builtins.TIMESTAMPTZ,
  types.builtins.TIMETZ,
];
for (const typeId of DATE_PARSING_TYPES) {
  types.setTypeParser(typeId, (val) => new Date(val).toISOString());
}

const _connections: Record<string, PoolingBtPg> = {};
export function getPGByURL(url: string) {
  url = trySanitizePgUrl(url);
  if (_connections[url]) {
    return _connections[url];
  }

  const conn = new PoolingBtPg(
    new NodePgPool({
      connectionString: url,
      // According to the docs, any ssl-related connection params specified in the
      // `connectionString` will override these settings:
      // https://node-postgres.com/features/ssl#usage-with-connectionstring. So
      // they serve as a default.
      ssl:
        // Locally, or if connecting to clickhouse, default to no SSL (we have not
        // configured Clickhouse to use SSL).
        //
        // Otherwise, we configure the SSL connection as suggested in these docs:
        // https://node-postgres.com/announcements#pg80-release.
        IS_LOCAL || url === CLICKHOUSE_PG_URL
          ? false
          : {
              rejectUnauthorized: false,
            },
      max: PG_POOL_CONFIG_MAX_NUM_CLIENTS,
    }),
  );

  conn.onError((err, client) => {
    getLogger().error({ error: err, client }, "Received error on client");
  });

  _connections[url] = conn;

  return conn;
}

export function getPG(opts?: { url?: string }) {
  const url = opts?.url ?? PG_URL;
  if (url === undefined) {
    throw new Error("url not set");
  }
  return getPGByURL(url);
}

export function pgStats() {
  return Object.entries(_connections).map(([connectionUrl, pool]) => ({
    connectionUrl,
    poolProperties: {
      totalCount: pool.totalCount(),
      idleCount: pool.idleCount(),
      waitingCount: pool.waitingCount(),
    },
  }));
}

export type PgError = {
  code: string;
};

export function isPgError(e: unknown): e is PgError {
  return e instanceof Error && "code" in e;
}

function trySanitizePgUrl(urlStr: string): string {
  if (PG_URL_NOMODIFY) {
    return urlStr;
  }
  try {
    const url = new URL(urlStr);
    // By default, translate `sslmode=require` to `sslmode=no-verify`. This
    // seems to match what clients in other languages (like python) do when you
    // specify `sslmode=require`.
    const sslmode = url.searchParams.get("sslmode");
    if (sslmode === "require") {
      url.searchParams.set("sslmode", "no-verify");
    }
    return url.toString();
  } catch {
    getLogger().warn("Failed to sanitize PG URL");
    return urlStr;
  }
}

// IMPORTANT: Every postgres query we run within a transaction from `logPg` must
// have this prefix in the query, so that we can identify the query as belonging
// to an insert transaction. This is important for the backfill loops to
// function correctly (see getSourceMaxSequenceId).
export const LOG_PG_TRANSACTION_QUERY_IDENTIFIER = "LOG_PG_TRANSACTION_QUERY";
export const LOG_PG_TRANSACTION_QUERY_PREFIX = `/* ${LOG_PG_TRANSACTION_QUERY_IDENTIFIER} */`;

// A wrapper around a connection object which prepends
// LOG_PG_TRANSACTION_QUERY_PREFIX to all queries.
export class LogPGConnection {
  private conn: PoolingBtPgClient;

  constructor(conn: PoolingBtPgClient) {
    this.conn = conn;
  }

  query(queryText: string, values?: unknown[]): Promise<BtQueryResult> {
    return this.conn.query(
      `${LOG_PG_TRANSACTION_QUERY_PREFIX} ${queryText}`,
      values,
    );
  }
}
