import { z } from "zod";
import { getPG } from "../db/pg";
import { ident, join, snippet, sql, ToSQL } from "@braintrust/btql/planner";
import { extractDebugErrorText, InternalServerError } from "../util";
import {
  BRAINSTORE_ADD_TRACKED_OBJECTS_SYNC,
  BRAINSTORE_BACKFILL_DISABLE_HISTORICAL,
  BRAINSTORE_BACKFILL_ENABLE_NONHISTORICAL,
  BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE,
  BRAINSTORE_BACKFILL_NEW_OBJECTS,
  BRAINSTORE_DISABLE_ETL_LOOP,
  BRAINSTORE_ENABLE_HISTORICAL_FULL_BACKFILL,
  BRAINSTORE_REALTIME_WAL_BUCKET_NAME,
  PG_COMMENTS_TABLE,
  PG_<PERSON>OGS_TABLE,
  PG_LOGS2_TABLE,
  BRAINSTORE_BACKFILL_DISABLE_COMMENTS,
  BRAINSTORE_BACKFILL_DISABLE_FRONTIER_LOOP,
} from "../env";
import { getLogger } from "../instrumentation/logger";
import {
  ETL_BATCH_SIZE,
  ETL_PERIOD_S,
  EtlLoopBatch,
  acquireAdvisoryLock,
  getSourceMaxSequenceId,
  RunEtlLoopOutput,
  tryAcquireAdvisoryLock,
} from "../clickhouse_etl";
import { makeObjectIdExprFromTable } from "../query_util";
import {
  assertBrainstoreEnabled,
  brainstoreEnabled,
  makeBrainstoreObjectId,
  processWal,
} from "./brainstore";
import {
  OBJECT_TYPE_FIELD,
  objectIdsUnionSchema,
} from "@braintrust/local/api-schema";
import {
  backfillableObjectTypeSchema,
  FullTrackedObject,
  GlobalBackfillStatus,
  ObjectBackfillStatus,
  objectBackfillStatusSchema,
  ProjectBackfillStatus,
  TrackedObject,
  trackedObjectSchema,
} from "@braintrust/local/app-schema";
import { ObjectType } from "../schema";
import { mapSetDefault } from "@braintrust/core";
import { AdvisoryLocks } from "../advisory_locks";
import { v4 as uuidv4 } from "uuid";
import { PoolingBtPg, PoolingBtPgClient } from "@braintrust/local/bt-pg";

type SourceDbTableIndex = "one" | "two";

function makeCounterSuffix(sourceDbTableIndex: SourceDbTableIndex) {
  return snippet(sourceDbTableIndex === "one" ? "" : "_2");
}

// This expression is the same as the index expression we use in the DB schema.
function compareSequenceIdsExpr(sourceDbTableIndex: SourceDbTableIndex) {
  const suffix = makeCounterSuffix(sourceDbTableIndex);
  return sql`(last_processed_sequence_id${suffix} < last_encountered_sequence_id${suffix})`;
}

const estimatedProgressExpr = sql`greatest(least(
  (
    case when last_encountered_sequence_id <= initial_sequence_id then 1
    else (
      ((last_processed_sequence_id - initial_sequence_id)::double precision) /
      ((last_encountered_sequence_id - initial_sequence_id)::double precision)
    )
    end
  ),
  (
    case when last_encountered_sequence_id_2 <= initial_sequence_id_2 then 1
    else (
      ((last_processed_sequence_id_2 - initial_sequence_id_2)::double precision) /
      ((last_encountered_sequence_id_2 - initial_sequence_id_2)::double precision)
    )
    end
  ),
  1
), 0)`;

function isAlreadyBackfilledExpr(isAlreadyBackfilled: boolean) {
  return isAlreadyBackfilled
    ? sql`(completed_initial_backfill_ts is not null)`
    : sql`(completed_initial_backfill_ts is null)`;
}

// NOTE: These functions assume you have already done ACL checks.

export async function getGlobalBackfillStatus(): Promise<GlobalBackfillStatus> {
  const getBackfillStatus = async () => {
    const conn = getPG();
    const queryTemplate = sql`
        with
        per_object_stats as (
        select
            last_processed_sequence_id,
            last_encountered_sequence_id,
            last_processed_sequence_id_2,
            last_encountered_sequence_id_2
        from brainstore_backfill_tracked_objects
        where is_backfilling
        )
        select
            coalesce(
                bool_and(
                    last_processed_sequence_id >= last_encountered_sequence_id and
                    last_processed_sequence_id_2 >= last_encountered_sequence_id_2
                ),
                true) as all_backfilled
        from per_object_stats
    `;
    const { query, params } = queryTemplate.toNumericParamQuery();
    const { rows } = await conn.query(query, params);
    if (rows.length > 1) {
      throw new InternalServerError("Expected at most one row");
    }
    return z
      .object({
        all_backfilled: z.boolean(),
      })
      .parse(rows[0]);
  };

  const [
    backfillStatus,
    frontierSequenceId,
    logsMaxSequenceId,
    logs2MaxSequenceId,
    commentsMaxSequenceId,
  ] = await Promise.all([
    getBackfillStatus(),
    getBackfillGlobalState(),
    getMaxSequenceId(PG_LOGS_TABLE),
    getMaxSequenceId(PG_LOGS2_TABLE),
    getCommentsMaxSequenceId(),
  ]);

  const historicalBackfillProgressOne =
    frontierSequenceId.one.frontier_sequence_id === 0
      ? null
      : frontierSequenceId.one.historical_full_backfill_sequence_id /
        frontierSequenceId.one.frontier_sequence_id;
  const historicalBackfillProgressTwo =
    frontierSequenceId.two.frontier_sequence_id === 0
      ? null
      : frontierSequenceId.two.historical_full_backfill_sequence_id /
        frontierSequenceId.two.frontier_sequence_id;
  const historicalBackfillProgress =
    historicalBackfillProgressOne === null &&
    historicalBackfillProgressTwo === null
      ? null
      : Math.min(
          historicalBackfillProgressOne ?? 1,
          historicalBackfillProgressTwo ?? 1,
        );

  return {
    ...backfillStatus,
    logs_max_sequence_id: logsMaxSequenceId.id,
    logs2_max_sequence_id: logs2MaxSequenceId.id,
    backfill_frontier_sequence_id: frontierSequenceId.one.frontier_sequence_id,
    backfill_frontier_sequence_id_2:
      frontierSequenceId.two.frontier_sequence_id,

    historical_backfill_progress: historicalBackfillProgress,

    comments_backfill_progress:
      frontierSequenceId.comments_backfill_sequence_id === 0
        ? null
        : frontierSequenceId.comments_backfill_sequence_id /
          commentsMaxSequenceId.comments_max_sequence_id,

    completed_initial_comments_backfill_ts:
      frontierSequenceId.completed_initial_comments_backfill_ts
        ? frontierSequenceId.completed_initial_comments_backfill_ts.toISOString()
        : null,

    backfill_mode: BRAINSTORE_ENABLE_HISTORICAL_FULL_BACKFILL
      ? "historical_full"
      : "per_project",
  };
}

export async function getTrackedObjectStatus({
  projectIds,
  objectType,
}: {
  projectIds: string[];
  objectType: ObjectType | null;
}): Promise<ObjectBackfillStatus[]> {
  if (projectIds.length === 0) {
    return [];
  }
  const db = getPG();
  const selectQuery = sql`
      select
          project_id,
          object_type,
          (
            case when last_encountered_sequence_id_2 > 0
            then last_processed_sequence_id_2
            else last_processed_sequence_id
            end
          ) as backfill_frontier_sequence_id,
          (
            case when last_encountered_sequence_id_2 > 0
            then last_encountered_sequence_id_2
            else last_encountered_sequence_id
            end
          ) as backfill_target_sequence_id,
          (
            case when last_encountered_sequence_id_2 > 0
            then last_backfilled_ts_2
            else last_backfilled_ts
            end
          ) as last_backfilled_ts,
          completed_initial_backfill_ts,
          ${estimatedProgressExpr} as estimated_progress,
          is_backfilling as enabled
      from brainstore_backfill_tracked_objects
      where
          project_id in (${join(
            projectIds.map((p) => sql`${p}`),
            ",",
          )})
          ${objectType ? sql`and object_type = ${objectType}` : snippet("")}
      order by row_created desc, object_type asc
  `;
  const { query, params } = selectQuery.toNumericParamQuery();
  const { rows } = await db.query(query, params);
  return z.array(objectBackfillStatusSchema).parse(rows);
}

export async function getProjectBackfillStatus({
  projectId,
}: {
  projectId: string;
}): Promise<ProjectBackfillStatus> {
  return {
    global_status: await getGlobalBackfillStatus(),
    object_statuses: await getTrackedObjectStatus({
      projectIds: [projectId],
      objectType: null,
    }),
  };
}

export async function getBackfillingObjects({
  queryTrackedObjects,
  conn,
}: {
  queryTrackedObjects: TrackedObject[];
  conn: PoolingBtPg | PoolingBtPgClient;
}): Promise<TrackedObject[]> {
  conn = conn ?? getPG();
  const { rows } = await conn.query(
    `
      select project_id, object_type
      from brainstore_backfill_tracked_objects
      where
        is_backfilling
        and (project_id, object_type) in (
            select project_id, object_type
            from jsonb_to_recordset($1::jsonb) as x(project_id text, object_type text)
        )
    `,
    [JSON.stringify(queryTrackedObjects)],
  );
  return trackedObjectSchema.array().parse(rows);
}

async function getMaxSequenceId(tbl: string): Promise<{
  id: number;
}> {
  const conn = getPG();
  const { rows } = await conn.query(`select max(sequence_id) id from ${tbl}`);
  if (rows.length === 0) {
    return { id: 0 };
  }
  if (rows.length !== 1) {
    throw new InternalServerError("Expected up to one row. Found multiple.");
  }
  const data = z
    .object({
      id: z.number().nullish(),
    })
    .parse(rows[0]);
  return { id: data.id ?? 0 };
}

async function getCommentsMaxSequenceId(): Promise<{
  comments_max_sequence_id: number;
}> {
  const conn = getPG();
  const { rows } = await conn.query(
    "select max(sequence_id) comments_max_sequence_id from comments",
  );
  if (rows.length !== 1) {
    throw new InternalServerError("Expected exactly one row (empty database?)");
  }
  const data = z
    .object({
      comments_max_sequence_id: z.number().nullish(),
    })
    .parse(rows[0]);
  return { comments_max_sequence_id: data.comments_max_sequence_id ?? 0 };
}

export async function getBackfillGlobalState(
  conn?: PoolingBtPg | PoolingBtPgClient,
): Promise<{
  one: {
    frontier_sequence_id: number;
    historical_full_backfill_sequence_id: number;
  };
  two: {
    frontier_sequence_id: number;
    historical_full_backfill_sequence_id: number;
  };
  comments_backfill_sequence_id: number;
  completed_initial_comments_backfill_ts: Date | null;
}> {
  if (!conn) {
    conn = getPG();
  }
  const { rows } = await conn.query(`
    select
        frontier_sequence_id,
        frontier_sequence_id_2,
        historical_full_backfill_sequence_id,
        historical_full_backfill_sequence_id_2,
        comments_backfill_sequence_id,
        completed_initial_comments_backfill_ts
    from brainstore_backfill_global_state
    `);
  if (rows.length === 0) {
    return {
      one: {
        frontier_sequence_id: 0,
        historical_full_backfill_sequence_id: 0,
      },
      two: {
        frontier_sequence_id: 0,
        historical_full_backfill_sequence_id: 0,
      },
      comments_backfill_sequence_id: 0,
      completed_initial_comments_backfill_ts: null,
    };
  } else if (rows.length !== 1) {
    throw new InternalServerError("Expected up to one row. Found multiple.");
  }
  const data = z
    .object({
      frontier_sequence_id: z.number(),
      frontier_sequence_id_2: z.number(),
      historical_full_backfill_sequence_id: z.number(),
      historical_full_backfill_sequence_id_2: z.number(),
      comments_backfill_sequence_id: z.number(),
      completed_initial_comments_backfill_ts: z.coerce.date().nullable(),
    })
    .parse(rows[0]);
  return {
    one: {
      frontier_sequence_id: data.frontier_sequence_id,
      historical_full_backfill_sequence_id:
        data.historical_full_backfill_sequence_id,
    },
    two: {
      frontier_sequence_id: data.frontier_sequence_id_2,
      historical_full_backfill_sequence_id:
        data.historical_full_backfill_sequence_id_2,
    },
    comments_backfill_sequence_id: data.comments_backfill_sequence_id,
    completed_initial_comments_backfill_ts:
      data.completed_initial_comments_backfill_ts,
  };
}

export const brainstoreBackfillRunRequest = z.object({
  timeout: z.number().nullish(),
  iters: z.number().nullish(),
  batch_size: z.number().nullish(),
  frontier_batch_size: z.number().nullish(),
  object_batch_size: z.number().nullish(),
  prefer_per_project_historical_backfill: z.boolean().nullish(),
  disable_historical_backfill: z.boolean().nullish(),
  enable_realtime_backfill: z.boolean().nullish(),
});
export type BrainstoreBackfillRunRequest = z.infer<
  typeof brainstoreBackfillRunRequest
>;

function getBrainstoreEtlAdvisoryLocks(): (keyof AdvisoryLocks)[] {
  const locks: (keyof AdvisoryLocks)[] = [];
  if (!BRAINSTORE_BACKFILL_DISABLE_HISTORICAL) {
    locks.push("brainstore_etl_only_historcal");
  }
  if (BRAINSTORE_BACKFILL_ENABLE_NONHISTORICAL) {
    locks.push("brainstore_etl_only_nonhistorcal");
  }
  return locks;
}

export async function runBrainstoreEtlLoopRequest({
  timeout: timeoutS,
  iters,
  batch_size: batchSize,
  object_batch_size: objectBatchSize,
  frontier_batch_size: frontierBatchSize,
  prefer_per_project_historical_backfill: preferPerProjectHistoricalBackfill,
  disable_historical_backfill: disableHistoricalBackfill,
  enable_realtime_backfill: enableRealtimeBackfill,
}: BrainstoreBackfillRunRequest): Promise<RunEtlLoopOutput> {
  assertBrainstoreEnabled();
  const start = new Date();
  const pgConn = getPG();
  const pgClient = await pgConn.connect();
  await pgClient.query("begin");
  try {
    for (const lock of getBrainstoreEtlAdvisoryLocks()) {
      if (!(await tryAcquireAdvisoryLock(pgClient, lock))) {
        return { locked: false };
      }
    }
    const taskId = uuidv4();
    getLogger().debug({ taskId }, `[BrainstoreTS] [backfill] task starting`);
    const batches = await runBrainstoreEtlLoop({
      sourceDbTable: PG_LOGS_TABLE,
      sourceDbTable2: PG_LOGS2_TABLE,
      commentsDbTable: PG_COMMENTS_TABLE,
      loopStart: new Date(),
      timeoutS: timeoutS ?? 60,
      batchSize: batchSize ?? ETL_BATCH_SIZE,
      objectBatchSize: objectBatchSize ?? null,
      frontierBatchSize: frontierBatchSize ?? null,
      iters: iters ?? null,
      preferPerProjectHistoricalBackfill:
        preferPerProjectHistoricalBackfill ?? false,
      disableHistoricalBackfill: disableHistoricalBackfill ?? undefined,
      enableRealtimeBackfill: enableRealtimeBackfill ?? undefined,
      taskId,
    });
    getLogger().debug(
      {
        taskId,
        durationMs: new Date().getTime() - start.getTime(),
      },
      `[BrainstoreTS] [backfill] task completed`,
    );
    return { locked: true, tableResults: { [PG_LOGS_TABLE]: batches } };
  } finally {
    await pgClient.query("rollback");
    pgClient.release();
  }
}

let IS_FULLY_AVAILABLE_REALTIME = false;
let HAS_INITIALIZED_BACKFILL_STATUS = false;

export function isFullyAvailableRealtime(): boolean {
  return IS_FULLY_AVAILABLE_REALTIME;
}

// IMPORTANT: This function should only be called *before* any inserts can hit
// the DB. Otherwise it's possible for the server to start up and allow some
// inserts in before this function is called, which can lead the DB to get out
// of sync with the historical backfill. Currently it's called in
// run_log_data.ts before we write anything to postgres.
export async function initializeIsFullyAvailableRealtime(): Promise<void> {
  // Both BRAINSTORE_ADD_TRACKED_OBJECTS_SYNC and BRAINSTORE_REALTIME_WAL_BUCKET_NAME
  // are required to be set to true to mark every object as backfilled.
  if (
    !HAS_INITIALIZED_BACKFILL_STATUS &&
    !(
      BRAINSTORE_ADD_TRACKED_OBJECTS_SYNC &&
      BRAINSTORE_REALTIME_WAL_BUCKET_NAME &&
      BRAINSTORE_BACKFILL_NEW_OBJECTS
    )
  ) {
    HAS_INITIALIZED_BACKFILL_STATUS = true;
    IS_FULLY_AVAILABLE_REALTIME = false;
  }

  if (!HAS_INITIALIZED_BACKFILL_STATUS) {
    const conn = getPG();
    const { rows } = await conn.query(
      `
      WITH historical as (
        SELECT
          COALESCE(historical_full_backfill_sequence_id, 0) as c,
          COALESCE(historical_full_backfill_sequence_id_2, 0) as c_2
        FROM brainstore_backfill_global_state
      ),
      live as (
        SELECT COALESCE(MAX(sequence_id), 0) c FROM ${PG_LOGS_TABLE}
      ),
      live_2 as (
        SELECT COALESCE(MAX(sequence_id), 0) c_2 FROM ${PG_LOGS2_TABLE}
      )
      SELECT historical.c = live.c AND historical.c_2 = live_2.c_2 is_fully_backfilled FROM historical, live, live_2
      `,
    );
    const rowsParsed = z
      .object({ is_fully_backfilled: z.boolean() })
      .array()
      .parse(rows);
    IS_FULLY_AVAILABLE_REALTIME = rowsParsed[0].is_fully_backfilled;
    HAS_INITIALIZED_BACKFILL_STATUS = true;
  }
}

export function startBrainstoreEtlLoop(): void {
  if (!brainstoreEnabled() || BRAINSTORE_DISABLE_ETL_LOOP) {
    return;
  }

  (async () => {
    getLogger().info("Starting Brainstore ETL loop");
    while (true) {
      const startTime = new Date();
      const endTime = new Date();
      const elapsedS = (endTime.getTime() - startTime.getTime()) / 1000;
      // Commented log: ETL loop took ${elapsedS} seconds
      const sleepS = ETL_PERIOD_S - elapsedS;
      if (sleepS > 0) {
        await new Promise((resolve) => setTimeout(resolve, sleepS * 1000));
      }
    }
  })().catch((err) => {
    getLogger().error({ error: err }, "Error in Brainstore ETL loop");
  });
}

const NUM_UNBACKFILLED_OBJECTS_TO_FETCH = 200;
const DEFAULT_FRONTIER_BATCH_SIZE = 1000000;
const LOOP_FINISHED_SLEEP_MS = 100;

// This function mirrors runEtlLoop in api-ts/src/clickhouse_etl.ts. It assumes that you have
// taken an exclusive lock on the brainstore ETL process.
async function runBrainstoreEtlLoop({
  sourceDbTable,
  sourceDbTable2,
  commentsDbTable,
  loopStart,
  timeoutS,
  batchSize,
  objectBatchSize,
  frontierBatchSize: frontierBatchSizeParam,
  iters,
  preferPerProjectHistoricalBackfill,
  disableHistoricalBackfill,
  enableRealtimeBackfill,
  taskId,
}: {
  sourceDbTable: string;
  sourceDbTable2: string;
  commentsDbTable: string;
  loopStart: Date;
  timeoutS: number;
  batchSize: number;
  objectBatchSize: number | null;
  frontierBatchSize: number | null;
  iters: number | null;
  preferPerProjectHistoricalBackfill: boolean;
  disableHistoricalBackfill?: boolean;
  enableRealtimeBackfill?: boolean;
  taskId: string;
}): Promise<EtlLoopBatch[] | null> {
  const conn = getPG();

  // Do nothing if we have no tracked objects.
  if (!BRAINSTORE_ENABLE_HISTORICAL_FULL_BACKFILL) {
    const { rows } = await conn.query(
      "select exists(select 1 from brainstore_backfill_tracked_objects) is_tracking",
    );
    if (!z.object({ is_tracking: z.boolean() }).parse(rows[0]).is_tracking) {
      return [];
    }
  }

  // Accumulated by updateFrontierLoop.
  const batches: EtlLoopBatch[] = [];

  // Accumulated by all loops.
  let iter = 0;

  // Each loop can mark when it has run out of objects to process for the first
  // time. The loops will still keep running to pick up new activity until all
  // loops have flipped their flag to true.
  const hasFinishedAtLeastOnce = {
    one: {
      updateFrontierLoop: false,
      processBackfillLoopAlreadyBackfilled: false,
      processBackfillLoopUnbackfilled: false,
    },
    two: {
      updateFrontierLoop: false,
      processBackfillLoopAlreadyBackfilled: false,
      processBackfillLoopUnbackfilled: false,
    },
    flipCompletedInitialBackfillLoop: false,
    processCommentsBackfillLoop: false,
  };

  // Each loop will sleep for LOOP_FINISHED_SLEEP_MS if it has run out of work
  // in its previous iteration but is still waiting for other loops to finish at
  // least once.
  const loopSleepTask = async (callerIdentifier: string) => {
    getLogger().debug(
      {
        callerIdentifier,
        sleepMs: LOOP_FINISHED_SLEEP_MS,
      },
      `Loop sleeping`,
    );
    await new Promise((resolve) => setTimeout(resolve, LOOP_FINISHED_SLEEP_MS));
    getLogger().debug({ callerIdentifier }, `Loop woke up`);
  };

  const errorSleepTask = async (callerIdentifier: string) => {
    getLogger().debug(
      {
        callerIdentifier,
        sleepMs: 1000,
      },
      `Error recovery sleep`,
    );
    await new Promise((resolve) => setTimeout(resolve, 1000));
    getLogger().debug({ callerIdentifier }, `Error recovery wake up`);
  };

  const hasTimedOut = (callerIdentifier: string) => {
    if (new Date().getTime() - loopStart.getTime() > timeoutS * 1000) {
      getLogger().debug({ callerIdentifier, timeoutS }, `Timeout reached`);
      return true;
    }
    if (iters !== null && iter >= iters) {
      getLogger().debug(
        { callerIdentifier, iterations: iter, maxIterations: iters },
        `Iteration limit reached`,
      );
      return true;
    }
    if (
      hasFinishedAtLeastOnce.one.updateFrontierLoop &&
      hasFinishedAtLeastOnce.two.updateFrontierLoop &&
      hasFinishedAtLeastOnce.one.processBackfillLoopAlreadyBackfilled &&
      hasFinishedAtLeastOnce.two.processBackfillLoopAlreadyBackfilled &&
      hasFinishedAtLeastOnce.one.processBackfillLoopUnbackfilled &&
      hasFinishedAtLeastOnce.two.processBackfillLoopUnbackfilled &&
      hasFinishedAtLeastOnce.flipCompletedInitialBackfillLoop &&
      hasFinishedAtLeastOnce.processCommentsBackfillLoop
    ) {
      getLogger().debug({ callerIdentifier }, `All loops have finished`);
      return true;
    }
    return false;
  };

  // In the first loop, we advance the frontier as far as possible towards the
  // maximum source-table sequence id.
  //
  // In batches of size frontierBatchSize, we scan the logs table from the
  // current frontier sequence_id and collect the last-encountered sequence id
  // for any object. We aggregate these manually into a max
  // last_encountered_sequence_id for each (project_id, object_type).
  //
  // Finally, we take the brainstore_etl_modify_tracked_objects advisory lock and
  // simultaneously update the frontier sequence id and the last-encountered
  // sequence ids of any existing tracked objects. We must take the lock so that
  // any concurrent process adding new tracked objects doesn't race with our
  // updates to the status of existing tracked objects.
  const updateFrontierLoop = async (sourceDbTableIndex: SourceDbTableIndex) => {
    const tbl = sourceDbTableIndex === "one" ? sourceDbTable : sourceDbTable2;
    const counterSuffix = makeCounterSuffix(sourceDbTableIndex);
    const frontierBatchSize =
      frontierBatchSizeParam ?? DEFAULT_FRONTIER_BATCH_SIZE;

    let previousIterationFinished = false;
    const callerIdentifier = `[BrainstoreTS] [updateFrontierLoop] ${tbl}`;
    getLogger().debug(
      { taskId, table: tbl },
      `${callerIdentifier}: task starting`,
    );
    while (true) {
      getLogger().debug(
        { iteration: iter, table: tbl },
        `${callerIdentifier}: starting loop iteration`,
      );
      try {
        if (hasTimedOut(callerIdentifier)) {
          break;
        }
        if (previousIterationFinished) {
          await loopSleepTask(callerIdentifier);
        }
        const startTime = new Date();

        // See runEtlLoop in api-ts/src/clickhouse_etl.ts for more details on why we do this. But it's essentially
        // a read-write-locked way of deriving the true max sequence id on the source table.
        const sourceMaxSequenceId = await getSourceMaxSequenceId(
          tbl,
          callerIdentifier,
        );

        const globalState = await getBackfillGlobalState();
        const frontierSequenceId =
          globalState[sourceDbTableIndex].frontier_sequence_id;
        if (frontierSequenceId >= sourceMaxSequenceId) {
          previousIterationFinished = true;
          if (!hasFinishedAtLeastOnce[sourceDbTableIndex].updateFrontierLoop) {
            hasFinishedAtLeastOnce[sourceDbTableIndex].updateFrontierLoop =
              true;
          }
          continue;
        } else {
          previousIterationFinished = false;
        }
        const upperBoundSequenceId = Math.min(
          frontierSequenceId + frontierBatchSize,
          sourceMaxSequenceId,
        );

        const trackedObjectsBatchInfo = await (async () => {
          const querySnippet = sql`
          select
              ${makeObjectIdExprFromTable(tbl, "postgres")} object_id,
              max(sequence_id) max_sequence_id,
              min(project_id) project_id,
              min(experiment_id) experiment_id,
              min(dataset_id) dataset_id,
              min(prompt_session_id) prompt_session_id,
              min(log_id) log_id
          from ${ident(tbl)}
          where
              sequence_id > ${frontierSequenceId}
              and sequence_id <= ${upperBoundSequenceId}
              and ${makeObjectIdExprFromTable(tbl, "postgres")} is not null
          group by object_id
        `;
          const { query, params } = querySnippet.toNumericParamQuery();
          const { rows } = await conn.query(query, params);
          return groupByTrackedObject(
            frontierUpdateObjectSchema.array().parse(rows),
          ).groupedRows;
        })();

        // Turn this into a list of records: [{ project_id, object_type,
        // last_encountered_sequence_id }].
        const trackedObjectRecords = Array.from(
          trackedObjectsBatchInfo.entries(),
        ).map(([key, rows]) => {
          const last_encountered_sequence_id = Math.max(
            ...rows.map((r) => r.max_sequence_id),
          );
          return {
            ...fromTrackedObjectKey(key),
            last_encountered_sequence_id,
          };
        });
        getLogger().debug(
          {
            callerIdentifier,
            trackedObjectCount: trackedObjectRecords.length,
          },
          `Found tracked objects to update`,
        );

        // Now under the brainstore_etl_modify_tracked_objects lock, update the
        // last_encountered_sequence_id for each tracked object and the global
        // frontier_sequence_id.
        const updateLastEncounteredSequenceId = async () => {
          const client = await conn.connect();
          await client.query("begin");
          try {
            await acquireAdvisoryLock(
              client,
              "brainstore_etl_modify_tracked_objects",
            );
            {
              const queryTemplate = sql`
                with
                update_data as (
                    select *
                    from jsonb_to_recordset(${JSON.stringify(trackedObjectRecords)}::jsonb) as x(project_id text, object_type text, last_encountered_sequence_id bigint)
                ),
                ordered_update_data as (
                   select project_id, object_type, update_data.last_encountered_sequence_id
                   from brainstore_backfill_tracked_objects join update_data using (project_id, object_type)
                   order by project_id, object_type
                   for update
                )
                update brainstore_backfill_tracked_objects
                set last_encountered_sequence_id${counterSuffix} = greatest(
                    brainstore_backfill_tracked_objects.last_encountered_sequence_id${counterSuffix},
                    ordered_update_data.last_encountered_sequence_id)
                from ordered_update_data
                where
                    brainstore_backfill_tracked_objects.project_id = ordered_update_data.project_id
                    and brainstore_backfill_tracked_objects.object_type = ordered_update_data.object_type
              `;
              const { query, params } = queryTemplate.toNumericParamQuery();
              await client.query(query, params);
            }
            {
              const queryTemplate = sql`
                update brainstore_backfill_global_state set frontier_sequence_id${counterSuffix} = ${upperBoundSequenceId}
              `;
              const { query, params } = queryTemplate.toNumericParamQuery();
              await client.query(query, params);
            }
            await client.query("commit");
          } catch (e) {
            await client.query("rollback");
            throw e;
          } finally {
            client.release();
          }
        };
        await Promise.all([
          updateLastEncounteredSequenceId(),
          populateBrainstoreBackfillTrackedObjectsToBrainstoreObjects(
            conn,
            trackedObjectsBatchInfo,
          ),
        ]);
        getLogger().debug(
          {
            callerIdentifier,
            upperBoundSequenceId,
          },
          `[${callerIdentifier}] Updated frontier sequence id`,
        );

        const endTime = new Date();
        batches.push({
          maxSequenceId: upperBoundSequenceId,
          elapsedTimeS: (endTime.getTime() - startTime.getTime()) / 1000,
        });
        iter++;
      } catch (e) {
        getLogger().error(
          {
            callerIdentifier,
            error: extractDebugErrorText(e),
          },
          `[${callerIdentifier}] Failed to update frontier loop: ${extractDebugErrorText(e)}`,
        );
        await errorSleepTask(callerIdentifier);
      }
    }

    // After pushing the frontier forward, we can fast-forward any
    // fully-backfilled objects to our frontier sequence id. This way, if they
    // do get encountered again later, we can start from the frontier, rather
    // than from wherever they were last backfilled.
    getLogger().debug(
      { callerIdentifier },
      `[${callerIdentifier}] Fast-forwarding fully-backfilled objects`,
    );
    {
      const globalState = await getBackfillGlobalState();
      const frontierSequenceId =
        globalState[sourceDbTableIndex].frontier_sequence_id;

      const querySnippet = sql`
        with
        ordered_tracked_objects as (
            select project_id, object_type
            from brainstore_backfill_tracked_objects
            where
              is_backfilling
              and ${compareSequenceIdsExpr(sourceDbTableIndex)} = false
            order by project_id, object_type
            for update
        )
        update brainstore_backfill_tracked_objects
        set last_processed_sequence_id${counterSuffix} = greatest(last_processed_sequence_id${counterSuffix}, ${frontierSequenceId})
        from ordered_tracked_objects
        where
            brainstore_backfill_tracked_objects.project_id = ordered_tracked_objects.project_id
            and brainstore_backfill_tracked_objects.object_type = ordered_tracked_objects.object_type
      `;
      const { query, params } = querySnippet.toNumericParamQuery();
      await conn.query(query, params);
    }
    getLogger().debug(
      { callerIdentifier },
      `[${callerIdentifier}] Done fast-forwarding fully-backfilled objects`,
    );
    getLogger().debug({ callerIdentifier, taskId }, `Task finished`);
  };

  // In another parallel loop, we can process any objects that need backfilling.
  // Any object whose last_processed_sequence_id < last_encountered_sequence_id
  // and is_backfilling = true needs to be backfilled.
  //
  // Do this in two separate loops, one catching up any already-backfilled
  // objects, and next catching up any un-backfilled objects.
  //
  // We do this in a for loop, grabbing batches of
  // NUM_UNBACKFILLED_OBJECTS_TO_FETCH objects and processing the WAL across the
  // next batchSize rows, starting from the minimum sequence_id out of all the
  // objects.
  const processBackfillRows = async (
    isAlreadyBackfilled: boolean,
    sourceDbTableIndex: SourceDbTableIndex,
  ) => {
    const tbl = sourceDbTableIndex === "one" ? sourceDbTable : sourceDbTable2;
    const counterSuffix = makeCounterSuffix(sourceDbTableIndex);
    const isFinishedProp = isAlreadyBackfilled
      ? ("processBackfillLoopAlreadyBackfilled" as const)
      : ("processBackfillLoopUnbackfilled" as const);
    const callerIdentifier = `[BrainstoreTS] [${isFinishedProp}] ${tbl}`;
    let previousIterationFinished = false;
    getLogger().debug(
      { taskId, callerIdentifier, table: tbl },
      `Task starting`,
    );
    while (true) {
      getLogger().debug(
        { iteration: iter, callerIdentifier, table: tbl },
        `Starting loop iteration`,
      );
      try {
        const loopStart = new Date();
        if (hasTimedOut(callerIdentifier)) {
          break;
        }
        if (previousIterationFinished) {
          await loopSleepTask(callerIdentifier);
        }

        const [backfillGlobalState, maxSequenceIdRet] = await Promise.all([
          getBackfillGlobalState(),
          getMaxSequenceId(tbl),
        ]);
        const frontierSequenceId =
          backfillGlobalState[sourceDbTableIndex].frontier_sequence_id;
        const maxSequenceId = maxSequenceIdRet.id;

        // Fetch a set of objects that need to be backfilled.
        //
        // We fetch half the batch in order of decreasing
        // last_processed_sequence_id, in hopes of addressing the
        // closest-to-complete objects first.
        //
        // We fetch the other half in order of increasing
        // last_encountered_sequence_id, in hopes of grabbing objects that are
        // furthest behind.
        const objectsToBackfill = await (async () => {
          const querySnippet = sql`
            (
            select
                project_id,
                object_type,
                last_processed_sequence_id${counterSuffix} last_processed_sequence_id,
                last_encountered_sequence_id${counterSuffix} last_encountered_sequence_id
            from
                brainstore_backfill_tracked_objects
            where
                is_backfilling
                and ${compareSequenceIdsExpr(sourceDbTableIndex)} = true
                and ${isAlreadyBackfilledExpr(isAlreadyBackfilled)}
                and last_encountered_sequence_id${counterSuffix} <= ${frontierSequenceId}
            order by last_processed_sequence_id${counterSuffix} desc
            limit ${NUM_UNBACKFILLED_OBJECTS_TO_FETCH / 2}
            )
            union
            (
            select
                project_id,
                object_type,
                last_processed_sequence_id${counterSuffix} last_processed_sequence_id,
                last_encountered_sequence_id${counterSuffix} last_encountered_sequence_id
            from
                brainstore_backfill_tracked_objects
            where
                is_backfilling
                and ${compareSequenceIdsExpr(sourceDbTableIndex)} = true
                and ${isAlreadyBackfilledExpr(isAlreadyBackfilled)}
                and last_encountered_sequence_id${counterSuffix} <= ${frontierSequenceId}
            order by last_processed_sequence_id${counterSuffix} asc
            limit ${NUM_UNBACKFILLED_OBJECTS_TO_FETCH / 2}
            )
        `;
          const { query, params } = querySnippet.toNumericParamQuery();
          return z
            .array(backfillObjectBatchSchema)
            .parse((await conn.query(query, params)).rows);
        })();
        if (objectsToBackfill.length === 0) {
          previousIterationFinished = true;
          // The already-backfilled loop will not be marked finished until the
          // frontier-advancing loop has also finished.
          if (
            (!isAlreadyBackfilled ||
              hasFinishedAtLeastOnce[sourceDbTableIndex].updateFrontierLoop) &&
            !hasFinishedAtLeastOnce[sourceDbTableIndex][isFinishedProp]
          ) {
            getLogger().debug({ callerIdentifier }, `No objects to backfill`);
            hasFinishedAtLeastOnce[sourceDbTableIndex][isFinishedProp] = true;
          }
          continue;
        } else {
          previousIterationFinished = false;
        }

        const trackedObjectsBackfillInfo = new Map<
          string,
          BackfillObjectBatch
        >();
        for (const row of objectsToBackfill) {
          trackedObjectsBackfillInfo.set(trackedObjectKey(row), row);
        }
        const uniqueProjectIds = [
          ...new Set(objectsToBackfill.map((o) => o.project_id)).values(),
        ];

        // Process the WAL starting from min(last_processed_sequence_id). We
        // accomplish this by grabbing the set of object ids in the batch,
        // filtering down to those that belong to any of the tracked objects that
        // need backfilling within that range, and running processWal on that set.
        const lowerBoundSequenceId = objectsToBackfill.reduce(
          (acc, x) => Math.min(acc, x.last_processed_sequence_id),
          Infinity,
        );
        if (lowerBoundSequenceId === Infinity) {
          throw new Error(
            `${callerIdentifier}: lowerBoundSequenceId is Infinity. This should never happen.`,
          );
        }
        const upperBoundSequenceId = Math.min(
          frontierSequenceId,
          lowerBoundSequenceId + batchSize,
        );
        getLogger().debug(
          {
            callerIdentifier,
            objectCount: objectsToBackfill.length,
            lowerBoundSequenceId,
            upperBoundSequenceId,
          },
          `Processing objects to backfill`,
        );
        const trackedObjectsBatchInfo = (
          await fetchTrackedObjectsBatchInfo({
            conn,
            tbl,
            lowerBoundSequenceId,
            upperBoundSequenceId,
            additionalWhereClause: sql`project_id in (${join(
              uniqueProjectIds.map((x) => sql`${x}`),
              ", ",
            )})`,
          })
        ).batchInfo;
        getLogger().debug(
          {
            callerIdentifier,
            objectCount: trackedObjectsBatchInfo.size,
            elapsedSeconds: (Date.now() - loopStart.getTime()) / 1000,
          },
          `Fetched objects for processing`,
        );
        await runProcessWalBackfillBatch({
          conn,
          callerIdentifier: `${isFinishedProp} ${tbl}`,
          lowerBoundSequenceId,
          upperBoundSequenceId,
          maxSequenceId,
          readLogs2: sourceDbTableIndex === "two",
          objectBatchSize,
          trackedObjectsBatchInfo,
          trackedObjectsBackfillInfo,
        });

        // Update the last_processed_sequence_id for each object that we
        // originally grabbed in this batch.
        const trackedObjectRecords = [
          ...trackedObjectsBackfillInfo.values(),
        ].map((x) => ({
          project_id: x.project_id,
          object_type: x.object_type,
        }));
        {
          const queryTemplate = sql`
          with
          update_data as (
              select *
              from jsonb_to_recordset(${JSON.stringify(trackedObjectRecords)}::jsonb) as x(project_id text, object_type text)
          ),
          ordered_update_data as (
             select project_id, object_type
             from brainstore_backfill_tracked_objects join update_data using (project_id, object_type)
             order by project_id, object_type
             for update
          )
          update brainstore_backfill_tracked_objects
          set last_processed_sequence_id${counterSuffix} = greatest(
                  brainstore_backfill_tracked_objects.last_processed_sequence_id${counterSuffix}, ${upperBoundSequenceId}::bigint),
              last_backfilled_ts${counterSuffix} = (case
                  when brainstore_backfill_tracked_objects.last_processed_sequence_id${counterSuffix} < ${upperBoundSequenceId} then now()
                  else brainstore_backfill_tracked_objects.last_backfilled_ts${counterSuffix}
              end)
          from ordered_update_data
          where
              brainstore_backfill_tracked_objects.project_id = ordered_update_data.project_id
              and brainstore_backfill_tracked_objects.object_type = ordered_update_data.object_type
        `;
          const { query, params } = queryTemplate.toNumericParamQuery();
          await conn.query(query, params);
        }

        iter++;
      } catch (e) {
        getLogger().error(
          {
            callerIdentifier,
            error: e,
          },
          "failed. continuing...",
        );
        await errorSleepTask(callerIdentifier);
      }
    }
    getLogger().debug(
      {
        callerIdentifier,
        taskId,
      },
      "task finished",
    );
  };

  // As an alternative to the processBackfillRows loop for un-backfilled
  // objects, this function backfills the entire 'logs' table. Each iteration,
  // it reads from `historical_full_backfill_sequence_id`, and processes a
  // `batchSize` batch of rows. It creates new tracking entries for
  // newly-encountered objects, and then processes the WAL for them.
  const historicalFullBackfillLoop = async (
    sourceDbTableIndex: SourceDbTableIndex,
  ) => {
    const tbl = sourceDbTableIndex === "one" ? sourceDbTable : sourceDbTable2;
    const counterSuffix = makeCounterSuffix(sourceDbTableIndex);
    let previousIterationFinished = false;
    let finishedOneFullIteration = false;
    const callerIdentifier = `[BrainstoreTS] [historicalFullBackfillLoop] ${tbl}`;
    const pino = getLogger().child({
      task: "brainstore-backfill",
      loop: "historicalFullBackfillLoop",
    });
    pino.debug("task starting");
    while (true) {
      pino.debug({ iteration: iter }, "starting loop iteration");
      try {
        const loopStart = new Date();
        if (hasTimedOut(callerIdentifier)) {
          break;
        }
        if (previousIterationFinished) {
          await loopSleepTask(callerIdentifier);
        }

        const [backfillGlobalState, maxSequenceIdRet] = await Promise.all([
          getBackfillGlobalState(),
          getMaxSequenceId(tbl),
        ]);
        const frontierSequenceId =
          backfillGlobalState[sourceDbTableIndex].frontier_sequence_id;
        const historicalFullBackfillSequenceId =
          backfillGlobalState[sourceDbTableIndex]
            .historical_full_backfill_sequence_id;
        const maxSequenceId = maxSequenceIdRet.id;

        if (historicalFullBackfillSequenceId >= frontierSequenceId) {
          // If we haven't yet finished one full iteration, then we may never
          // get to the point in the code which fast-forwards the
          // last_processed_sequence_id of any eligible tracking entries. Since
          // we may not encounter entries on the logs table if we are only
          // inserting into logs2, we still need to run the fast-forward on the
          // logs-table sequence ID counter.
          if (!finishedOneFullIteration) {
            pino.debug(
              "Caught up. fast-forwarding any tracking entries to frontier_sequence_id.",
            );
            {
              const queryTemplate = sql`
                with
                ordered_rows as (
                    select project_id, object_type
                    from brainstore_backfill_tracked_objects
                    where
                      is_backfilling
                      and ${isAlreadyBackfilledExpr(false)}
                    order by project_id, object_type
                    for update
                )
                update brainstore_backfill_tracked_objects
                set last_processed_sequence_id${counterSuffix} = greatest(last_processed_sequence_id${counterSuffix}, ${frontierSequenceId}::bigint),
                    last_backfilled_ts${counterSuffix} = (case
                        when last_processed_sequence_id${counterSuffix} < ${frontierSequenceId}::bigint then now()
                        else last_backfilled_ts${counterSuffix}
                    end)
                from ordered_rows
                where
                    brainstore_backfill_tracked_objects.project_id = ordered_rows.project_id
                    and brainstore_backfill_tracked_objects.object_type = ordered_rows.object_type
            `;
              const { query, params } = queryTemplate.toNumericParamQuery();
              await conn.query(query, params);
            }
          }

          previousIterationFinished = true;
          // This loop will not be marked finished until the frontier-advancing
          // loop has also finished.
          if (
            hasFinishedAtLeastOnce[sourceDbTableIndex].updateFrontierLoop &&
            !hasFinishedAtLeastOnce[sourceDbTableIndex]
              .processBackfillLoopUnbackfilled
          ) {
            pino.debug(
              "historical full backfill has caught up to frontier_sequence_id.",
            );
            hasFinishedAtLeastOnce[
              sourceDbTableIndex
            ].processBackfillLoopUnbackfilled = true;
          }
          continue;
        } else {
          previousIterationFinished = false;
        }

        // First grab the set of object IDs in the next batch of sequence ids, and
        // group them by tracking entry.
        const upperBoundSequenceId = Math.min(
          frontierSequenceId,
          historicalFullBackfillSequenceId +
            BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE,
        );
        const {
          fullTrackedObjects: batchFullTrackedObjects,
          batchInfo: trackedObjectsBatchInfo,
        } = await fetchTrackedObjectsBatchInfo({
          conn,
          tbl,
          lowerBoundSequenceId: historicalFullBackfillSequenceId,
          upperBoundSequenceId,
          additionalWhereClause: sql`true`,
        });
        pino.debug(
          {
            objectCount: trackedObjectsBatchInfo.size,
            elapsedSeconds: (Date.now() - loopStart.getTime()) / 1000,
          },
          "fetched objects",
        );

        // Add objects to the tracking table and mark any new entries as
        // backfilling=true.
        await addTrackedObjects({
          trackedObjects: batchFullTrackedObjects,
          shouldBackfill: true,
        });

        // Fetch the tracking entries for each object. Only include those which
        // are un-backfilled, since the already-backfilled objects will be taken
        // care of in the other loop.
        //
        // Also make sure to only include tracking entries marked
        // is_backfilling, because we may have objects in
        // `batchFullTrackedObjects` which have backfilling disabled and we
        // don't want to consider those.
        const trackedObjectsBackfillInfo = await (async () => {
          const jsonRecords = JSON.stringify(
            batchFullTrackedObjects.map((o) => ({
              project_id: o.project_id,
              object_type: o.object_type,
            })),
          );
          const querySnippet = sql`
            select project_id, object_type, last_processed_sequence_id${counterSuffix} last_processed_sequence_id, last_encountered_sequence_id${counterSuffix} last_encountered_sequence_id
            from brainstore_backfill_tracked_objects
            where
                (project_id, object_type) in (
                    select x.project_id, x.object_type
                    from jsonb_to_recordset(${jsonRecords}::jsonb) as x(project_id text, object_type text)
                )
                and is_backfilling
                and ${isAlreadyBackfilledExpr(false)}
        `;
          const { query, params } = querySnippet.toNumericParamQuery();
          const { rows } = await conn.query(query, params);
          const parsedRows = backfillObjectBatchSchema.array().parse(rows);
          return new Map<string, BackfillObjectBatch>(
            parsedRows.map((x) => [trackedObjectKey(x), x]),
          );
        })();

        await runProcessWalBackfillBatch({
          conn,
          callerIdentifier: `historicalFullBackfillLoop ${tbl}`,
          lowerBoundSequenceId: historicalFullBackfillSequenceId,
          upperBoundSequenceId,
          maxSequenceId,
          readLogs2: sourceDbTableIndex === "two",
          objectBatchSize,
          trackedObjectsBatchInfo,
          trackedObjectsBackfillInfo,
        });

        // Update the last_processed_sequence_id for every un-backfilled tracked
        // object. We assume that since the historical_full_backfill_sequence_id
        // must have started from the beginning, then every existing entry will
        // now have been covered up to the upperBoundSequenceId.
        {
          const queryTemplate = sql`
            with
            ordered_rows as (
                select project_id, object_type
                from brainstore_backfill_tracked_objects
                where
                  is_backfilling
                  and ${isAlreadyBackfilledExpr(false)}
                order by project_id, object_type
                for update
            )
            update brainstore_backfill_tracked_objects
            set last_processed_sequence_id${counterSuffix} = greatest(last_processed_sequence_id${counterSuffix}, ${upperBoundSequenceId}::bigint),
                last_backfilled_ts${counterSuffix} = (case
                    when last_processed_sequence_id${counterSuffix} < ${upperBoundSequenceId}::bigint then now()
                    else last_backfilled_ts${counterSuffix}
                end)
            from ordered_rows
            where
                brainstore_backfill_tracked_objects.project_id = ordered_rows.project_id
                and brainstore_backfill_tracked_objects.object_type = ordered_rows.object_type
        `;
          const { query, params } = queryTemplate.toNumericParamQuery();
          await conn.query(query, params);
        }

        // Finally, update the global backfill state to advance the
        // historical_full_backfill_sequence_id.
        {
          const queryTemplate = sql`
            update brainstore_backfill_global_state set historical_full_backfill_sequence_id${counterSuffix} = ${upperBoundSequenceId}
          `;
          const { query, params } = queryTemplate.toNumericParamQuery();
          await conn.query(query, params);
        }
        pino.debug(
          {
            from: historicalFullBackfillSequenceId,
            to: upperBoundSequenceId,
          },
          `Advanced sequence ID`,
        );
        finishedOneFullIteration = true;
        iter++;
      } catch (e) {
        pino.error({ error: e }, "failed. continuing...");
        await errorSleepTask(callerIdentifier);
      }
    }
    pino.debug({ taskId }, "task finished");
  };

  // Analogue to the historicalFullBackfillLoop for the comments table. This
  // runs without the "explicit object tracking" infrastructure we have for the
  // logs table.
  const backfillCommentsLoop = async () => {
    let previousIterationFinished = false;
    const callerIdentifier = `[BrainstoreTS] [backfillCommentsLoop] ${commentsDbTable}`;
    const pino = getLogger().child({
      task: "brainstore-backfill",
      loop: "backfillCommentsLoop",
    });
    pino.debug("task starting");
    while (true) {
      pino.debug({ iteration: iter }, "starting loop iteration");
      try {
        const loopStart = new Date();
        if (hasTimedOut(callerIdentifier)) {
          break;
        }
        if (previousIterationFinished) {
          await loopSleepTask(callerIdentifier);
        }

        const [getCommentsMaxSequenceIdRes, getBackfillGlobalStateRes] =
          await Promise.all([
            getCommentsMaxSequenceId(),
            getBackfillGlobalState(),
          ]);
        const { comments_max_sequence_id: commentsMaxSequenceId } =
          getCommentsMaxSequenceIdRes;
        const {
          comments_backfill_sequence_id: commentsBackfillSequenceId,
          completed_initial_comments_backfill_ts:
            completedInitialCommentsBackfillTs,
        } = getBackfillGlobalStateRes;

        if (commentsBackfillSequenceId >= commentsMaxSequenceId) {
          previousIterationFinished = true;
          if (!hasFinishedAtLeastOnce.processCommentsBackfillLoop) {
            pino.debug("comments backfill has caught up.");
            hasFinishedAtLeastOnce.processCommentsBackfillLoop = true;
          }
          continue;
        } else {
          previousIterationFinished = false;
        }

        // First grab the set of object IDs in the next batch of sequence ids, and
        // group them by tracking entry.
        const upperBoundSequenceId = Math.min(
          commentsMaxSequenceId,
          commentsBackfillSequenceId +
            BRAINSTORE_BACKFILL_HISTORICAL_BATCH_SIZE,
        );
        const trackedObjectsBatchInfo = (
          await fetchTrackedObjectsBatchInfo({
            conn,
            tbl: commentsDbTable,
            lowerBoundSequenceId: commentsBackfillSequenceId,
            upperBoundSequenceId,
            additionalWhereClause: sql`true`,
          })
        ).batchInfo;
        pino.debug(
          {
            objectCount: trackedObjectsBatchInfo.size,
            elapsedSeconds: (Date.now() - loopStart.getTime()) / 1000,
          },
          "fetched objects",
        );

        await runProcessWalBackfillBatch({
          conn,
          callerIdentifier: "backfillCommentsLoop",
          lowerBoundSequenceId: commentsBackfillSequenceId,
          upperBoundSequenceId,
          maxSequenceId: commentsMaxSequenceId,
          readComments: true,
          // If we haven't yet reached the end of the comments table, mark all of
          // the processed entries as compacted, so that we don't trigger
          // compaction on their behalf. This way, we won't incur a time-consuming
          // compaction from an old xact_id due to an old comment. But once we've
          // caught up, it's fine to run compactions from comments.
          markAsCompacted: !completedInitialCommentsBackfillTs,
          objectBatchSize,
          trackedObjectsBatchInfo,
          trackedObjectsBackfillInfo: null,
        });

        // Finally, update the global backfill state to advance the
        // comments_backfill_sequence_id, and flip the
        // completed_ts if we have gotten to the end.
        {
          const queryTemplate = sql`
          update brainstore_backfill_global_state
          set
              comments_backfill_sequence_id = greatest(comments_backfill_sequence_id, ${upperBoundSequenceId}::bigint),
              completed_initial_comments_backfill_ts = (case
                  when completed_initial_comments_backfill_ts is null and ${upperBoundSequenceId}::bigint >= ${commentsMaxSequenceId}::bigint then now()
                  else completed_initial_comments_backfill_ts
              end)
        `;
          const { query, params } = queryTemplate.toNumericParamQuery();
          await conn.query(query, params);
        }
        pino.debug(
          {
            from: commentsBackfillSequenceId,
            to: upperBoundSequenceId,
          },
          "advanced sequence ID",
        );

        iter++;
      } catch (e) {
        pino.error({ error: e }, "failed. continuing...");
        await errorSleepTask(callerIdentifier);
      }
    }
    pino.debug({ taskId }, "task finished");
  };

  // In the final loop, we flip the completed_initial_backfill_ts for any
  // tracking entry that meets the following criteria:
  //
  // - completed_initial_backfill_ts is null
  //
  // - last_processed_sequence_id >= last_encountered_sequence_id
  //
  // - for all brainstore objects linked to this tracking entry, the value of
  // (ts(last_processed_xact_id) - ts(max(last_compacted_xact_id))) <=
  // ETL_PERIOD_S. If there are no linked brainstore objects, this criterion is
  // vacuously true.
  //
  // Since the compaction lag criterion is somewhat more expensive to check, we
  // restrict the update to a batch of tracked entries that fulfill the other
  // criteria before checking it.
  const flipCompletedInitialBackfillLoop = async () => {
    let previousIterationFinished = false;
    const callerIdentifier = `[BrainstoreTS] [flipCompletedInitialBackfillLoop]`;
    const pino = getLogger().child({
      task: "brainstore-backfill",
      loop: "flipCompletedInitialBackfillLoop",
    });
    pino.debug("task starting");
    while (true) {
      pino.debug({ iter }, "starting loop iteration");
      try {
        if (hasTimedOut(callerIdentifier)) {
          break;
        }
        if (previousIterationFinished) {
          await loopSleepTask(callerIdentifier);
        }

        const queryTemplate = sql`
          with
          candidate_tracking_entries as (
            select project_id, object_type
            from brainstore_backfill_tracked_objects
            where
                is_backfilling
                and (last_processed_sequence_id >= last_encountered_sequence_id)
                and (last_processed_sequence_id_2 >= last_encountered_sequence_id_2)
                and ${isAlreadyBackfilledExpr(false)}
            limit 100
          ),
          tracking_entry_to_brainstore_object as (
            select brainstore_backfill_tracked_objects_to_brainstore_objects.*
            from brainstore_backfill_tracked_objects_to_brainstore_objects
                join candidate_tracking_entries using (project_id, object_type)
          ),
          brainstore_object_to_last_compacted_xact_id as (
            select
                tracking_entry_to_brainstore_object.brainstore_object_id,
                -- We need the additional case machinery to avoid ignoring
                -- un-compacted segments.
                (case
                    when bool_and(brainstore_global_store_segment_id_to_metadata.last_compacted_index_meta_xact_id is not null)
                        then max(brainstore_global_store_segment_id_to_metadata.last_compacted_index_meta_xact_id)
                    else null
                end) last_compacted_xact_id
            from
                tracking_entry_to_brainstore_object
                join brainstore_global_store_segment_id_to_liveness
                    on tracking_entry_to_brainstore_object.brainstore_object_id = brainstore_global_store_segment_id_to_liveness.object_id
                    and brainstore_global_store_segment_id_to_liveness.is_live
                join brainstore_global_store_segment_id_to_metadata using (segment_id)
            group by brainstore_object_id
          ),
          brainstore_object_to_compaction_delta as (
            select
                tracking_entry_to_brainstore_object.brainstore_object_id,
                (((brainstore_global_store_object_id_to_metadata.last_processed_xact_id >> 16) & X'FFFFFFFF'::bigint) -
                 ((brainstore_object_to_last_compacted_xact_id.last_compacted_xact_id >> 16) & X'FFFFFFFF'::bigint)) compaction_delta
            from
                tracking_entry_to_brainstore_object
                join brainstore_global_store_object_id_to_metadata
                    on tracking_entry_to_brainstore_object.brainstore_object_id = brainstore_global_store_object_id_to_metadata.object_id
                join brainstore_object_to_last_compacted_xact_id using (brainstore_object_id)
          ),
          tracking_entry_to_max_compaction_delta as (
            select
                candidate_tracking_entries.project_id,
                candidate_tracking_entries.object_type,
                max(brainstore_object_to_compaction_delta.compaction_delta) max_compaction_delta
            from
                candidate_tracking_entries
                left join tracking_entry_to_brainstore_object using (project_id, object_type)
                left join brainstore_object_to_compaction_delta using (brainstore_object_id)
            group by project_id, object_type
          ),
          tracking_entries_to_flip as (
            select project_id, object_type from tracking_entry_to_max_compaction_delta
            where max_compaction_delta <= ${ETL_PERIOD_S}::float8 or max_compaction_delta isnull
          ),
          ordered_tracking_entries_to_flip as (
            select project_id, object_type
            from brainstore_backfill_tracked_objects
                join tracking_entries_to_flip using (project_id, object_type)
            order by project_id, object_type
            for update
          )
          update brainstore_backfill_tracked_objects
          set completed_initial_backfill_ts = now()
          from ordered_tracking_entries_to_flip
          where
            brainstore_backfill_tracked_objects.project_id = ordered_tracking_entries_to_flip.project_id
            and brainstore_backfill_tracked_objects.object_type = ordered_tracking_entries_to_flip.object_type
      `;
        const { query, params } = queryTemplate.toNumericParamQuery();
        const { rowCount } = await conn.query(query, params);
        if (rowCount === 0) {
          previousIterationFinished = true;
          // This loop will not be marked finished until the un-backfilled
          // catchup loops have been marked finished.
          if (
            hasFinishedAtLeastOnce.one.processBackfillLoopUnbackfilled &&
            hasFinishedAtLeastOnce.two.processBackfillLoopUnbackfilled &&
            !hasFinishedAtLeastOnce.flipCompletedInitialBackfillLoop
          ) {
            pino.debug(
              "Finished marking objects as completed initial backfill",
            );
            hasFinishedAtLeastOnce.flipCompletedInitialBackfillLoop = true;
          }
          continue;
        } else {
          previousIterationFinished = false;
        }

        iter++;
      } catch (e) {
        pino.error({ error: e }, "failed. continuing...");
        await errorSleepTask(callerIdentifier);
      }
    }
    pino.debug({ taskId }, "task finished");
  };

  const launchHistoricalBackfill = () => {
    return BRAINSTORE_ENABLE_HISTORICAL_FULL_BACKFILL &&
      !preferPerProjectHistoricalBackfill
      ? (async () => {
          await Promise.all([
            historicalFullBackfillLoop("one"),
            historicalFullBackfillLoop("two"),
          ]);
        })()
      : (async () => {
          await Promise.all([
            processBackfillRows(false, "one"),
            processBackfillRows(false, "two"),
          ]);
        })();
  };

  const tasks: (() => Promise<void>)[] = [];
  if (BRAINSTORE_BACKFILL_DISABLE_HISTORICAL || disableHistoricalBackfill) {
    hasFinishedAtLeastOnce.one.processBackfillLoopUnbackfilled = true;
    hasFinishedAtLeastOnce.two.processBackfillLoopUnbackfilled = true;
    hasFinishedAtLeastOnce.processCommentsBackfillLoop = true;
    hasFinishedAtLeastOnce.flipCompletedInitialBackfillLoop = true;
  } else {
    tasks.push(launchHistoricalBackfill);
    tasks.push(flipCompletedInitialBackfillLoop);
  }

  if (BRAINSTORE_BACKFILL_DISABLE_FRONTIER_LOOP) {
    hasFinishedAtLeastOnce.one.updateFrontierLoop = true;
    hasFinishedAtLeastOnce.two.updateFrontierLoop = true;
  } else {
    tasks.push(() => updateFrontierLoop("one"));
    tasks.push(() => updateFrontierLoop("two"));
  }

  if (BRAINSTORE_BACKFILL_ENABLE_NONHISTORICAL || enableRealtimeBackfill) {
    tasks.push(() => processBackfillRows(true, "one"));
    tasks.push(() => processBackfillRows(true, "two"));
  } else {
    hasFinishedAtLeastOnce.one.processBackfillLoopAlreadyBackfilled = true;
    hasFinishedAtLeastOnce.two.processBackfillLoopAlreadyBackfilled = true;
  }

  if (BRAINSTORE_BACKFILL_DISABLE_COMMENTS) {
    hasFinishedAtLeastOnce.processCommentsBackfillLoop = true;
  } else {
    tasks.push(backfillCommentsLoop);
  }

  await Promise.all(tasks.map((t) => t()));

  return batches;
}

const dbObjectIdsSchema = z.object({
  object_id: z.string(),
  project_id: z.string().nullish(),
  experiment_id: z.string().nullish(),
  dataset_id: z.string().nullish(),
  prompt_session_id: z.string().nullish(),
  log_id: z.string().nullish(),
  org_id: z.string().nullish(),
});
type DbObjectIds = z.infer<typeof dbObjectIdsSchema>;

const frontierUpdateObjectSchema = dbObjectIdsSchema.and(
  z.object({
    max_sequence_id: z.number(),
  }),
);

const processWalObjectSchema = dbObjectIdsSchema.and(
  z.object({
    min_sequence_id: z.number(),
    max_sequence_id: z.number(),
    min_xact_id: z.string(),
    max_xact_id: z.string(),
  }),
);

type ProcessWalObject = z.infer<typeof processWalObjectSchema>;

// NOTE: if you want the FullTrackedObject to include an org_id, then the input
// rows must include an org_id.
function groupByTrackedObject<T extends DbObjectIds>(
  rows: T[],
): { fullTrackedObjects: FullTrackedObject[]; groupedRows: Map<string, T[]> } {
  const fullTrackedObjects: Map<string, FullTrackedObject> = new Map();
  const groupedRows = new Map<string, T[]>();
  for (const row of rows) {
    const objectIdSet = objectIdsUnionSchema.parse(row);
    const parsedObjectType = backfillableObjectTypeSchema.safeParse(
      objectIdSet[OBJECT_TYPE_FIELD],
    );
    if (row.project_id && parsedObjectType.success) {
      const key = trackedObjectKey({
        project_id: row.project_id,
        object_type: parsedObjectType.data,
      });
      fullTrackedObjects.set(key, {
        org_id: row.org_id ?? null,
        project_id: row.project_id,
        object_type: parsedObjectType.data,
      });
      mapSetDefault(groupedRows, key, []).push(row);
    }
  }
  return {
    fullTrackedObjects: Array.from(fullTrackedObjects.values()),
    groupedRows,
  };
}

const backfillObjectBatchSchema = z.object({
  project_id: z.string(),
  object_type: z.string(),
  last_processed_sequence_id: z.number(),
  last_encountered_sequence_id: z.number(),
});
type BackfillObjectBatch = z.infer<typeof backfillObjectBatchSchema>;

export function triggerBackfill() {
  if (!brainstoreEnabled() || BRAINSTORE_DISABLE_ETL_LOOP) {
    return;
  }
  (async () => {
    await Promise.all([
      runBrainstoreEtlLoopRequest({
        timeout: ETL_PERIOD_S,
        batch_size: ETL_BATCH_SIZE,
      }),
    ]);
  })().catch((e) => {
    getLogger().error({ error: e }, "Failed to run brainstore ETL loop");
  });
}

function trackedObjectKey({
  project_id,
  object_type,
}: {
  project_id: string;
  object_type: string;
}) {
  return `${project_id}/${object_type}`;
}

function fromTrackedObjectKey(key: string) {
  const [project_id, object_type] = key.split("/");
  return { project_id, object_type };
}

async function fetchTrackedObjectsBatchInfo({
  conn,
  tbl,
  lowerBoundSequenceId,
  upperBoundSequenceId,
  additionalWhereClause,
}: {
  conn: PoolingBtPg;
  tbl: string;
  lowerBoundSequenceId: number;
  upperBoundSequenceId: number;
  additionalWhereClause: ToSQL;
}): Promise<{
  fullTrackedObjects: FullTrackedObject[];
  batchInfo: Map<string, ProcessWalObject[]>;
}> {
  const querySnippet = sql`
    select
        ${makeObjectIdExprFromTable(tbl, "postgres")} object_id,
        min(sequence_id) min_sequence_id,
        max(sequence_id) max_sequence_id,
        min(_xact_id)::text min_xact_id,
        max(_xact_id)::text max_xact_id,
        min(project_id) project_id,
        min(experiment_id) experiment_id,
        min(dataset_id) dataset_id,
        min(prompt_session_id) prompt_session_id,
        min(log_id) log_id,
        min(org_id) org_id
    from ${ident(tbl)}
    where
        true
        and sequence_id > ${lowerBoundSequenceId}
        and sequence_id <= ${upperBoundSequenceId}
        and ${makeObjectIdExprFromTable(tbl, "postgres")} is not null
        and project_id is not null
        and ${additionalWhereClause}
      group by object_id
  `;
  const { query, params } = querySnippet.toNumericParamQuery();
  const { rows } = await conn.query(query, params);
  const { fullTrackedObjects, groupedRows } = groupByTrackedObject(
    processWalObjectSchema.array().parse(rows),
  );
  return { fullTrackedObjects, batchInfo: groupedRows };
}

async function runProcessWalBackfillBatch({
  conn,
  callerIdentifier,
  lowerBoundSequenceId,
  upperBoundSequenceId,
  maxSequenceId,
  readLogs2,
  readComments,
  markAsCompacted,
  objectBatchSize,
  trackedObjectsBatchInfo,
  trackedObjectsBackfillInfo,
}: {
  conn: PoolingBtPg;
  callerIdentifier: string;
  lowerBoundSequenceId: number;
  upperBoundSequenceId: number;
  maxSequenceId: number;
  readLogs2?: boolean;
  readComments?: boolean;
  markAsCompacted?: boolean;
  objectBatchSize: number | null;
  // The keys should be constructed via trackedObjectKey.
  trackedObjectsBatchInfo: Map<string, ProcessWalObject[]>;
  trackedObjectsBackfillInfo: Map<string, BackfillObjectBatch> | null;
}): Promise<void> {
  // Filter the trackedObjectsBatchInfo down to a list of objects that need
  // backfilling within this batch. Map them to their original tracking entry.
  const objectIdsInBatch = new Map<
    string,
    { project_id: string; object_type: string }
  >();
  trackedObjectsBatchInfo.forEach((rows, trackedObjectsKey) => {
    rows.forEach((row) => {
      if (trackedObjectsBackfillInfo) {
        const backfillInfo = trackedObjectsBackfillInfo.get(trackedObjectsKey);
        if (!backfillInfo) {
          return;
        }
        if (
          row.max_sequence_id < backfillInfo.last_processed_sequence_id ||
          row.min_sequence_id > backfillInfo.last_encountered_sequence_id
        ) {
          // Skip this object because we don't need to backfill it.
          return;
        }
      }
      const trackedObject = fromTrackedObjectKey(trackedObjectsKey);
      const objectIdsUnion = objectIdsUnionSchema.parse(row);
      objectIdsInBatch.set(
        makeBrainstoreObjectId(objectIdsUnion),
        trackedObject,
      );
    });
  });

  // If we have any objects to backfill, process the WAL between the
  // sequence ID range. Also add the set of brainstore object ID <-> tracked
  // object associations to postgres.
  if (objectIdsInBatch.size > 0) {
    // There is a general issue with backfilling, due to the fact that we
    // process in ranges of sequence ids. It's possible that an individual
    // transaction for an object, spanning multiple sequence ids, may get split
    // into separate backfill batches. This is problematic for realtime queries,
    // because say our first batch has completed and updated its
    // `last_processed_xact_id` to X. If we then make a realtime query, it will
    // think the entire transaction X has been processed and read it from the
    // segment WAL, and then miss the remaining rows for that transaction.
    //
    // This issue shows up in our unit tests, so we insert a hacky-workaround
    // for it. If our upperBoundSequenceId is close-enough to the
    // maxSequenceId, we extend the upperBoundSequenceId to the
    // maxSequenceId, but bounding by the max_xact_id in our batch. This
    // will guarantee that we process all logs for any transactions in the
    // "original" batch we wanted to process.
    const { sequenceIdEnd, endXactId } = (() => {
      const maxXactIdInBatch = [...objectIdsInBatch.values()]
        .map((v) => {
          const batchInfoKey = trackedObjectKey(v);
          const batchInfo = trackedObjectsBatchInfo.get(batchInfoKey);
          if (!batchInfo) {
            return "";
          }
          return batchInfo.map((o) => o.max_xact_id);
        })
        .flat()
        .reduce((max, xactId) => {
          if (!max || (xactId && xactId > max)) {
            return xactId;
          }
          return max;
        }, "");
      if (upperBoundSequenceId + 20000 >= maxSequenceId && maxXactIdInBatch) {
        return {
          sequenceIdEnd: maxSequenceId,
          endXactId: maxXactIdInBatch,
        };
      } else {
        return {
          sequenceIdEnd: upperBoundSequenceId,
          endXactId: undefined,
        };
      }
    })();

    await Promise.all([
      processWal({
        callerIdentifier,
        objectIds: [...objectIdsInBatch.keys()],
        sequenceIdStart: lowerBoundSequenceId + 1,
        sequenceIdEnd,
        endXactId,
        readLogs2,
        readComments,
        markAsCompacted,
        disableTracing: true,
        batchSize: objectBatchSize ?? undefined,
      }),
      populateBrainstoreBackfillTrackedObjectsToBrainstoreObjects(
        conn,
        trackedObjectsBatchInfo,
      ),
    ]);
  }
}

async function populateBrainstoreBackfillTrackedObjectsToBrainstoreObjects(
  conn: PoolingBtPg,
  trackedObjectToBrainstoreObjects: Map<string, DbObjectIds[]>,
) {
  const snippets = [...trackedObjectToBrainstoreObjects.entries()].flatMap(
    ([trackedObjectKey, objectIdsArr]) => {
      const trackedObject = fromTrackedObjectKey(trackedObjectKey);
      return objectIdsArr.map((objectIds) => {
        const objectIdsUnion = objectIdsUnionSchema.parse(objectIds);
        const brainstoreObjectId = makeBrainstoreObjectId(objectIdsUnion);
        return sql`(${trackedObject.project_id}, ${trackedObject.object_type}, ${brainstoreObjectId})`;
      });
    },
  );
  if (!snippets.length) return;
  const queryTemplate = sql`
      insert into brainstore_backfill_tracked_objects_to_brainstore_objects(project_id, object_type, brainstore_object_id)
      values ${join(snippets, ",")} on conflict do nothing
  `;
  const { query, params } = queryTemplate.toNumericParamQuery();
  await conn.query(query, params);
}

export async function addTrackedObjects({
  trackedObjects,
  shouldBackfill,
  forceRefreshTrackingEntries,
}: {
  trackedObjects: FullTrackedObject[];
  shouldBackfill: boolean;
  forceRefreshTrackingEntries?: boolean;
}): Promise<void> {
  // The process for adding tracked objects is as follows:
  //
  // 0. Filter out any objects that are already tracked from the input list,
  // before acquiring a lock. If forceRefreshTrackingEntries is true, then we
  // skip this step, which will force a refresh of the tracking entries in step
  // 2.
  //
  // 1. Within a transaction, grab the brainstore_etl_modify_tracked_objects
  // advisory lock, to block out the frontier update process from reading the
  // set of tracked objects while we're updating it.
  //
  //    - Snapshot the current frontier sequence id
  //
  //    - Upsert the set of tracked objects, with a default
  //    (last_processed_xact_id, last_encountered_sequence_id) = (0,
  //    frontier_sequence_id).
  //
  // 2. Outside the transaction, flip the is_backfilling flag for any tracked
  // objects if specified.

  if (trackedObjects.length === 0) {
    return;
  }

  const conn = getPG();

  // Step 0, filter out any objects that are already tracked, before acquiring a
  // lock. Skip this step if forceRefreshTrackingEntries is true.
  if (!forceRefreshTrackingEntries) {
    const alreadyTrackedObjects = await (async () => {
      const { rows } = await conn.query(
        `
          select project_id, object_type from brainstore_backfill_tracked_objects
          where (project_id, object_type) in
            (select x.project_id, x.object_type
             from jsonb_to_recordset($1::jsonb) as x(project_id text, object_type text))
          `,
        [
          JSON.stringify(
            trackedObjects.map((o) => ({
              project_id: o.project_id,
              object_type: o.object_type,
            })),
          ),
        ],
      );

      return trackedObjectSchema.array().parse(rows);
    })();

    trackedObjects = trackedObjects.filter(
      (o) =>
        !alreadyTrackedObjects.some(
          (t) =>
            t.project_id === o.project_id && t.object_type === o.object_type,
        ),
    );

    if (trackedObjects.length === 0) {
      return;
    }
  }

  // Step 1.
  await (async () => {
    const client = await conn.connect();
    await client.query("begin");
    try {
      await acquireAdvisoryLock(
        client,
        "brainstore_etl_modify_tracked_objects",
      );
      const backfillGlobalState = await getBackfillGlobalState(client);
      const frontierSequenceId = backfillGlobalState.one.frontier_sequence_id;
      const frontierSequenceId2 = backfillGlobalState.two.frontier_sequence_id;
      await client.query(
        `
        insert into brainstore_backfill_tracked_objects(
            project_id, object_type, last_processed_sequence_id, last_encountered_sequence_id,
            last_processed_sequence_id_2, last_encountered_sequence_id_2,
            org_id, initial_sequence_id, initial_sequence_id_2)
         select * from jsonb_to_recordset($1::jsonb) as t(
            project_id text, object_type text, last_processed_sequence_id bigint, last_encountered_sequence_id bigint,
            last_processed_sequence_id_2 bigint, last_encountered_sequence_id_2 bigint,
            org_id text, initial_sequence_id bigint, initial_sequence_id_2 bigint)
         on conflict do nothing
      `,
        [
          JSON.stringify(
            trackedObjects.map((o) => ({
              ...o,
              last_processed_sequence_id: 0,
              last_encountered_sequence_id: frontierSequenceId,
              last_processed_sequence_id_2: 0,
              last_encountered_sequence_id_2: frontierSequenceId2,
              initial_sequence_id: 0,
              initial_sequence_id_2: 0,
            })),
          ),
        ],
      );
      await client.query("commit");
    } catch (e) {
      await client.query("rollback");
      throw e;
    } finally {
      client.release();
    }
  })();

  // Step 2.
  if (shouldBackfill) {
    const querySnippet = sql`
      with
      update_data as (
          select *
          from jsonb_to_recordset(${JSON.stringify(trackedObjects)}::jsonb) as x(project_id text, object_type text)
      ),
      ordered_update_data as (
         select project_id, object_type
         from brainstore_backfill_tracked_objects join update_data using (project_id, object_type)
         order by project_id, object_type
         for update
      )
      update brainstore_backfill_tracked_objects
      set is_backfilling = true
      ${isFullyAvailableRealtime() ? sql`, completed_initial_backfill_ts = NOW()` : sql``}
      from ordered_update_data
      where
        brainstore_backfill_tracked_objects.project_id = ordered_update_data.project_id
        and brainstore_backfill_tracked_objects.object_type = ordered_update_data.object_type
    `;
    const { query, params } = querySnippet.toNumericParamQuery();
    await conn.query(query, params);
  }
}
