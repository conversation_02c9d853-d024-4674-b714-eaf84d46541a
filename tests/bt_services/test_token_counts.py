import braintrust
import openai
import requests
from parameterized import parameterized

from autoevals import Factuality, <PERSON><PERSON><PERSON><PERSON>
from tests.braintrust_app_test_base import LOCAL_API_URL, TEST_ARGS, BraintrustAppTestBase

TOKEN_METRICS = [
    "prompt_tokens",
    "completion_tokens",
    "total_tokens",
]

PARAMETERS = [
    ("postgres",),
    ("duckdb",),
]

WITH_BRAINSTORE = PARAMETERS if BraintrustAppTestBase.skip_brainstore() else PARAMETERS + [("brainstore",)]


class TokenCountsTest(BraintrustAppTestBase):
    def setUp(self):
        super().setUp()

        self.proxy_url = braintrust.logger._state.proxy_url
        self.openai_base_url = f"{self.proxy_url}/v1"

    def tearDown(self):
        if TEST_ARGS.get("update"):
            requests.get(f"{self.proxy_url}/proxy/dump-cache")
        super().tearDown()

    def _openai_client(self):
        return braintrust.wrap_openai(
            openai.OpenAI(
                base_url=self.openai_base_url,
                api_key=self.org_api_key,
            )
        )

    def _get_aggregate_summary(self, experiment_id, mode):
        return self.run_request(
            "post",
            f"{LOCAL_API_URL}/btql",
            json=dict(
                query=make_experiment_query([experiment_id]),
                **self.make_mode_args(mode),
            ),
        ).json()["data"][0]

    @parameterized.expand(WITH_BRAINSTORE)
    def test_token_counts(self, mode):
        """Test token counts and verify that llm_duration is calculated as a sum across multiple LLM calls."""
        client = self._openai_client()

        dataset = [{"input": "foo", "expected": "bar"}]

        def basic_task(input):
            return (
                client.chat.completions.create(
                    model="gpt-4o",
                    messages=[{"role": "user", "content": "echo " + input}],
                )
                .choices[0]
                .message.content
            )

        # First, test that an experiment with 1 LLM call has token counts
        experiment_1 = braintrust.Eval("foo", data=dataset, task=basic_task, scores=[Levenshtein])

        agg_summary = self._get_aggregate_summary(experiment_1.summary.experiment_id, mode=mode)

        saved_metrics = {}
        for metric in TOKEN_METRICS:
            saved_metrics[metric] = experiment_1.summary.metrics[metric].metric
            self.assertGreater(experiment_1.summary.metrics[metric].metric, 0)
            self.assertEqual(agg_summary[metric], saved_metrics[metric])

        # Save llm_duration from single LLM call experiment
        # Get llm_duration from the aggregate summary (BTQL query result)
        saved_metrics["llm_duration"] = agg_summary["llm_duration"]
        self.assertGreater(saved_metrics["llm_duration"], 0)

        # Now use an LLM scorer, and make sure the metric counts are the same
        experiment_2 = braintrust.Eval("foo", data=dataset, task=basic_task, scores=[Factuality])
        agg_summary = self._get_aggregate_summary(experiment_2.summary.experiment_id, mode=mode)

        for metric in TOKEN_METRICS:
            self.assertEqual(experiment_2.summary.metrics[metric].metric, saved_metrics[metric])
            # Note: agg_summary may return different values when scorer is used due to filtering

        # Run two LLM calls in the task, and make sure that it has more prompt tokens than the
        # first experiment.

        def double_task(input):
            if input == "0":
                return "0"
            return basic_task(input) + " " + basic_task(input)

        experiment_3 = braintrust.Eval("foo", data=dataset, task=double_task, scores=[Levenshtein, Factuality])
        agg_summary = self._get_aggregate_summary(experiment_3.summary.experiment_id, mode=mode)

        for metric in TOKEN_METRICS:
            self.assertGreater(experiment_3.summary.metrics[metric].metric, saved_metrics[metric])
            self.assertEqual(agg_summary[metric], experiment_3.summary.metrics[metric].metric)

        # Test that llm_duration exists and is greater than 0 for double LLM calls
        # Note: Due to caching, the second call might be faster, so we can't reliably test
        # that it's exactly double. Instead, we verify it's calculated and positive.
        self.assertGreater(agg_summary["llm_duration"], 0)

        # Verify that with more LLM calls, we get more total tokens (this is more reliable than duration)
        # The double task should have roughly double the tokens
        self.assertGreater(experiment_3.summary.metrics["total_tokens"].metric, saved_metrics["total_tokens"] * 1.8)

        # Include a row that does not make any LLM calls
        dataset.extend([{"input": "0", "expected": "0"}])
        experiment_4 = braintrust.Eval("foo", data=dataset, task=double_task, scores=[Levenshtein, Factuality])
        agg_summary = self._get_aggregate_summary(experiment_4.summary.experiment_id, mode=mode)

        for metric in TOKEN_METRICS:
            self.assertLess(experiment_4.summary.metrics[metric].metric, experiment_3.summary.metrics[metric].metric)
            self.assertEqual(agg_summary[metric], experiment_4.summary.metrics[metric].metric)


def make_experiment_query(experiment_ids):
    return {
        "from": {
            "op": "function",
            "name": {"op": "ident", "name": ["experiment"]},
            "args": [{"op": "literal", "value": id} for id in experiment_ids],
        },
        "measures": [
            {
                "alias": "num_examples",
                "expr": {"btql": "sum(is_root)"},
            },
            {
                "alias": "duration",
                "expr": {
                    "btql": "COALESCE(AVG(span_attributes.name = 'task' ? metrics.end-metrics.start : NULL), AVG(is_root ? metrics.end-metrics.start : NULL))",
                },
            },
            {
                "alias": "llm_duration",
                "expr": {
                    "btql": "SUM(COALESCE(span_attributes.purpose, 'default') != 'scorer' AND span_attributes.type = 'llm' ? metrics.end-metrics.start : NULL)/num_examples",
                },
            },
            {
                "alias": "prompt_tokens",
                "expr": {
                    "btql": "SUM(COALESCE(span_attributes.purpose, 'default') != 'scorer' ? metrics.prompt_tokens : NULL)/num_examples",
                },
            },
            {
                "alias": "completion_tokens",
                "expr": {
                    "btql": "SUM(COALESCE(span_attributes.purpose, 'default') != 'scorer' ? metrics.completion_tokens : NULL)/num_examples",
                },
            },
            {
                "alias": "total_tokens",
                "expr": {
                    "btql": "prompt_tokens + completion_tokens",
                },
            },
        ],
        "dimensions": [
            {"alias": "experiment_id", "expr": {"btql": "experiment_id"}},
        ],
    }
